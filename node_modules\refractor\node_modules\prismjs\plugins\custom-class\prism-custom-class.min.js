!function(){if("undefined"!=typeof Prism){var a,t,e="";Prism.plugins.customClass={add:function(n){a=n},map:function(s){t="function"==typeof s?s:function(n){return s[n]||n}},prefix:function(n){e=n||""},apply:u},Prism.hooks.add("wrap",function(s){if(a){var n=a({content:s.content,type:s.type,language:s.language});Array.isArray(n)?s.classes.push.apply(s.classes,n):n&&s.classes.push(n)}(t||e)&&(s.classes=s.classes.map(function(n){return u(n,s.language)}))})}function u(n,s){return e+(t?t(n,s):n)}}();