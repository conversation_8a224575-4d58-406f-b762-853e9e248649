import { NextRequest, NextResponse } from 'next/server';
import ZA<PERSON> from 'z-ai-web-dev-sdk';

// Simple cache for storing recent responses
const responseCache = new Map<string, { response: string; timestamp: number }>();
const CACHE_DURATION = 3 * 60 * 1000; // 3 minutes

export async function POST(request: NextRequest) {
  try {
    const { message, sessionHistory, enableWebSearch = false, optimizeForSpeed = true } = await request.json();

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required and must be a string' },
        { status: 400 }
      );
    }

    // Check cache for identical recent requests
    const cacheKey = message.trim().toLowerCase();
    const cached = responseCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      console.log('Returning cached response for:', message);
      return NextResponse.json({
        response: cached.response,
        success: true,
        cached: true,
        usedWebSearch: false
      });
    }

    // Create ZAI instance
    const zai = await ZAI.create();

    let enhancedSystemPrompt = 'أنت مساعد ذكاء اصطناعي يدعى Elashrafy AI. أجب بسرعة وبدقة.';

    // Simple web search if enabled
    if (enableWebSearch) {
      try {
        console.log('جاري البحث عبر الإنترنت...');
        const searchResponse = await zai.functions.invoke("web_search", {
          query: message,
          num: 2 // Minimal for speed
        });
        
        if (searchResponse && searchResponse.length > 0) {
          enhancedSystemPrompt += '\n\nاستخدم هذه المعلومات للإجابة:\n';
          searchResponse.forEach((result: any, index: number) => {
            enhancedSystemPrompt += `${index + 1}. ${result.snippet}\n`;
          });
        }
      } catch (searchError) {
        console.log('Skip web search due to error');
      }
    }

    // Prepare messages - limit history for speed
    const messages = [
      {
        role: 'system',
        content: enhancedSystemPrompt
      },
      ...(sessionHistory || []).slice(-4).map((msg: any) => ({
        role: msg.role === 'user' ? 'user' : 'assistant',
        content: msg.content
      })),
      {
        role: 'user',
        content: message
      }
    ];

    // Optimized settings for maximum speed
    const completion = await zai.chat.completions.create({
      messages,
      temperature: 0.7,
      max_tokens: optimizeForSpeed ? 800 : 1200,
      stream: false,
    });

    const response = completion.choices[0]?.message?.content || 'عذراً، حدث خطأ.';

    // Cache the response
    responseCache.set(cacheKey, { response, timestamp: Date.now() });

    return NextResponse.json({
      response,
      success: true,
      usedWebSearch: enableWebSearch,
      cached: false,
      responseTime: Date.now()
    });

  } catch (error) {
    console.error('Fast Chat API Error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}