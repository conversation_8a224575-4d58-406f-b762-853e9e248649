!function(){if("undefined"!=typeof Prism&&"undefined"!=typeof document){var s=[];t(function(t){if(t&&t.meta&&t.data){if(t.meta.status&&400<=t.meta.status)return"Error: "+(t.data.message||t.meta.status);if("string"==typeof t.data.content)return"function"==typeof atob?atob(t.data.content.replace(/\s/g,"")):"Your browser cannot decode base64"}return null},"github"),t(function(t,e){if(t&&t.meta&&t.data&&t.data.files){if(t.meta.status&&400<=t.meta.status)return"Error: "+(t.data.message||t.meta.status);var n=t.data.files,a=e.getAttribute("data-filename");if(null==a)for(var r in n)if(n.hasOwnProperty(r)){a=r;break}return void 0!==n[a]?n[a].content:"Error: unknown or missing gist file "+a}return null},"gist"),t(function(t){return t&&t.node&&"string"==typeof t.data?t.data:null},"bitbucket");var f=0,d="data-jsonp-status",l="loading",c="loaded",m="failed",p="pre[data-jsonp]:not(["+d+'="'+c+'"]):not(['+d+'="'+l+'"])';Prism.hooks.add("before-highlightall",function(t){t.selector+=", "+p}),Prism.hooks.add("before-sanity-check",function(t){var r=t.element;if(r.matches(p)){t.code="",r.setAttribute(d,l);var i=r.appendChild(document.createElement("CODE"));i.textContent="Loading…";var e=t.language;i.className="language-"+e;var n=Prism.plugins.autoloader;n&&n.loadLanguages(e);var a=r.getAttribute("data-adapter"),o=null;if(a){if("function"!=typeof window[a])return r.setAttribute(d,m),void(i.textContent=function(t){return'✖ Error: JSONP adapter function "'+t+"\" doesn't exist"}(a));o=window[a]}var u=r.getAttribute("data-jsonp");!function(t,e,n,a){var r="prismjsonp"+f++,i=document.createElement("a");i.href=t,i.href+=(i.search?"&":"?")+(e||"callback")+"="+r;var o=document.createElement("script");o.src=i.href,o.onerror=function(){s(),a("network")};var u=setTimeout(function(){s(),a("timeout")},Prism.plugins.jsonphighlight.timeout);function s(){clearTimeout(u),document.head.removeChild(o),delete window[r]}window[r]=function(t){s(),n(t)},document.head.appendChild(o)}(u,r.getAttribute("data-callback"),function(t){var e=null;if(o)e=o(t,r);else for(var n=0,a=s.length;n<a&&null===(e=s[n].adapter(t,r));n++);null===e?(r.setAttribute(d,m),i.textContent="✖ Error: Cannot parse response (perhaps you need an adapter function?)"):(r.setAttribute(d,c),i.textContent=e,Prism.highlightElement(i))},function(){r.setAttribute(d,m),i.textContent=function(t){return"✖ Error: Timeout loading "+t}(u)})}}),Prism.plugins.jsonphighlight={timeout:5e3,registerAdapter:t,removeAdapter:function(e){if("string"==typeof e&&(e=n(e)),"function"==typeof e){var t=s.findIndex(function(t){return t.adapter===e});0<=t&&s.splice(t,1)}},highlight:function(t){for(var e,n=(t||document).querySelectorAll(p),a=0;e=n[a++];)Prism.highlightElement(e)}}}function t(t,e){e=e||t.name,"function"!=typeof t||n(t)||n(e)||s.push({adapter:t,name:e})}function n(t){if("function"==typeof t){for(var e=0;n=s[e++];)if(n.adapter.valueOf()===t.valueOf())return n.adapter}else if("string"==typeof t){var n;for(e=0;n=s[e++];)if(n.name===t)return n.adapter}return null}}();