{"version": 3, "sources": ["../../../../src/shared/lib/page-path/normalize-page-path.ts"], "sourcesContent": ["import { ensureLeadingSlash } from './ensure-leading-slash'\nimport { isDynamicRoute } from '../router/utils'\nimport { NormalizeError } from '../utils'\n\n/**\n * Takes a page and transforms it into its file counterpart ensuring that the\n * output is normalized. Note this function is not idempotent because a page\n * `/index` can be referencing `/index/index.js` and `/index/index` could be\n * referencing `/index/index/index.js`. Examples:\n *  - `/` -> `/index`\n *  - `/index/foo` -> `/index/index/foo`\n *  - `/index` -> `/index/index`\n */\nexport function normalizePagePath(page: string): string {\n  const normalized =\n    /^\\/index(\\/|$)/.test(page) && !isDynamicRoute(page)\n      ? `/index${page}`\n      : page === '/'\n        ? '/index'\n        : ensureLeadingSlash(page)\n\n  if (process.env.NEXT_RUNTIME !== 'edge') {\n    const { posix } = require('path')\n    const resolvedPage = posix.normalize(normalized)\n    if (resolvedPage !== normalized) {\n      throw new NormalizeError(\n        `Requested and resolved page mismatch: ${normalized} ${resolvedPage}`\n      )\n    }\n  }\n\n  return normalized\n}\n"], "names": ["ensureLeadingSlash", "isDynamicRoute", "NormalizeError", "normalizePagePath", "page", "normalized", "test", "process", "env", "NEXT_RUNTIME", "posix", "require", "resolvedPage", "normalize"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,yBAAwB;AAC3D,SAASC,cAAc,QAAQ,kBAAiB;AAChD,SAASC,cAAc,QAAQ,WAAU;AAEzC;;;;;;;;CAQC,GACD,OAAO,SAASC,kBAAkBC,IAAY;IAC5C,MAAMC,aACJ,iBAAiBC,IAAI,CAACF,SAAS,CAACH,eAAeG,QAC3C,AAAC,WAAQA,OACTA,SAAS,MACP,WACAJ,mBAAmBI;IAE3B,IAAIG,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;QACvC,MAAM,EAAEC,KAAK,EAAE,GAAGC,QAAQ;QAC1B,MAAMC,eAAeF,MAAMG,SAAS,CAACR;QACrC,IAAIO,iBAAiBP,YAAY;YAC/B,MAAM,IAAIH,eACR,AAAC,2CAAwCG,aAAW,MAAGO;QAE3D;IACF;IAEA,OAAOP;AACT"}