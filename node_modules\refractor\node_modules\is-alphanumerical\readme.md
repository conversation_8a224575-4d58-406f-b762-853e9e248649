# is-alphanumerical

[![Build][build-badge]][build]
[![Coverage][coverage-badge]][coverage]
[![Downloads][downloads-badge]][downloads]
[![Size][size-badge]][size]

Check if a character is alphanumerical (`[a-zA-Z0-9]`).

## Install

[npm][]:

```sh
npm install is-alphanumerical
```

## Use

```js
var alphanumerical = require('is-alphanumerical')

alphanumerical('a') // => true
alphanumerical('Z') // => true
alphanumerical('0') // => true
alphanumerical(' ') // => false
alphanumerical('💩') // => false
```

## API

### `alphanumerical(character)`

Check whether the given character code (`number`), or the character code at the
first position (`string`), is alphanumerical.

## Related

*   [`is-alphabetical`](https://github.com/wooorm/is-alphabetical)
*   [`is-decimal`](https://github.com/wooorm/is-decimal)
*   [`is-hexadecimal`](https://github.com/wooorm/is-hexadecimal)
*   [`is-whitespace-character`](https://github.com/wooorm/is-whitespace-character)
*   [`is-word-character`](https://github.com/wooorm/is-word-character)

## License

[MIT][license] © [Titus Wormer][author]

<!-- Definitions -->

[build-badge]: https://img.shields.io/travis/wooorm/is-alphanumerical.svg

[build]: https://travis-ci.org/wooorm/is-alphanumerical

[coverage-badge]: https://img.shields.io/codecov/c/github/wooorm/is-alphanumerical.svg

[coverage]: https://codecov.io/github/wooorm/is-alphanumerical

[downloads-badge]: https://img.shields.io/npm/dm/is-alphanumerical.svg

[downloads]: https://www.npmjs.com/package/is-alphanumerical

[size-badge]: https://img.shields.io/bundlephobia/minzip/is-alphanumerical.svg

[size]: https://bundlephobia.com/result?p=is-alphanumerical

[npm]: https://docs.npmjs.com/cli/install

[license]: license

[author]: https://wooorm.com
