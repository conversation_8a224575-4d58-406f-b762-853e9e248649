{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/overlay/overlay.tsx"], "sourcesContent": ["import { Overlay, type OverlayProps } from '../../overlay/overlay'\n\nexport function ErrorOverlayOverlay({ children, ...props }: OverlayProps) {\n  return <Overlay {...props}>{children}</Overlay>\n}\n\nexport const OVERLAY_STYLES = `\n  [data-nextjs-dialog-overlay] {\n    padding: initial;\n    top: 10vh;\n  }\n`\n"], "names": ["Overlay", "ErrorOverlayOverlay", "children", "props", "OVERLAY_STYLES"], "mappings": ";AAAA,SAASA,OAAO,QAA2B,wBAAuB;AAElE,OAAO,SAASC,oBAAoB,KAAoC;IAApC,IAAA,EAAEC,QAAQ,EAAE,GAAGC,OAAqB,GAApC;IAClC,qBAAO,KAACH;QAAS,GAAGG,KAAK;kBAAGD;;AAC9B;AAEA,OAAO,MAAME,iBAAkB,mFAK9B"}