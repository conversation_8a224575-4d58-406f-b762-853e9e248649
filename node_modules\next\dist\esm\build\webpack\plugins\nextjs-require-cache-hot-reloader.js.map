{"version": 3, "sources": ["../../../../src/build/webpack/plugins/nextjs-require-cache-hot-reloader.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { deleteCache } from '../../../server/dev/require-cache'\nimport { clearModuleContext } from '../../../server/web/sandbox'\nimport path from 'path'\n\ntype Compiler = webpack.Compiler\ntype WebpackPluginInstance = webpack.WebpackPluginInstance\n\nconst RUNTIME_NAMES = ['webpack-runtime', 'webpack-api-runtime']\nconst PLUGIN_NAME = 'NextJsRequireCacheHotReloader'\n\n// This plugin flushes require.cache after emitting the files. Providing 'hot reloading' of server files.\nexport class NextJsRequireCacheHotReloader implements WebpackPluginInstance {\n  prevAssets: any = null\n  serverComponents: boolean\n\n  constructor(opts: { serverComponents: boolean }) {\n    this.serverComponents = opts.serverComponents\n  }\n\n  apply(compiler: Compiler) {\n    compiler.hooks.assetEmitted.tap(PLUGIN_NAME, (_file, { targetPath }) => {\n      // Clear module context in this process\n      clearModuleContext(targetPath)\n      deleteCache(targetPath)\n    })\n\n    compiler.hooks.afterEmit.tapPromise(PLUGIN_NAME, async (compilation) => {\n      for (const name of RUNTIME_NAMES) {\n        const runtimeChunkPath = path.join(\n          compilation.outputOptions.path!,\n          `${name}.js`\n        )\n        deleteCache(runtimeChunkPath)\n      }\n\n      // we need to make sure to clear all server entries from cache\n      // since they can have a stale webpack-runtime cache\n      // which needs to always be in-sync\n      const entries = [...compilation.entrypoints.keys()].filter((entry) => {\n        const isAppPath = entry.toString().startsWith('app/')\n        return entry.toString().startsWith('pages/') || isAppPath\n      })\n\n      for (const page of entries) {\n        const outputPath = path.join(\n          compilation.outputOptions.path!,\n          page + '.js'\n        )\n        deleteCache(outputPath)\n      }\n    })\n  }\n}\n"], "names": ["deleteCache", "clearModuleContext", "path", "RUNTIME_NAMES", "PLUGIN_NAME", "NextJsRequireCacheHotReloader", "constructor", "opts", "prevAssets", "serverComponents", "apply", "compiler", "hooks", "assetEmitted", "tap", "_file", "targetPath", "afterEmit", "tapPromise", "compilation", "name", "runtimeChunk<PERSON><PERSON>", "join", "outputOptions", "entries", "entrypoints", "keys", "filter", "entry", "isAppPath", "toString", "startsWith", "page", "outputPath"], "mappings": "AACA,SAASA,WAAW,QAAQ,oCAAmC;AAC/D,SAASC,kBAAkB,QAAQ,8BAA6B;AAChE,OAAOC,UAAU,OAAM;AAKvB,MAAMC,gBAAgB;IAAC;IAAmB;CAAsB;AAChE,MAAMC,cAAc;AAEpB,yGAAyG;AACzG,OAAO,MAAMC;IAIXC,YAAYC,IAAmC,CAAE;aAHjDC,aAAkB;QAIhB,IAAI,CAACC,gBAAgB,GAAGF,KAAKE,gBAAgB;IAC/C;IAEAC,MAAMC,QAAkB,EAAE;QACxBA,SAASC,KAAK,CAACC,YAAY,CAACC,GAAG,CAACV,aAAa,CAACW,OAAO,EAAEC,UAAU,EAAE;YACjE,uCAAuC;YACvCf,mBAAmBe;YACnBhB,YAAYgB;QACd;QAEAL,SAASC,KAAK,CAACK,SAAS,CAACC,UAAU,CAACd,aAAa,OAAOe;YACtD,KAAK,MAAMC,QAAQjB,cAAe;gBAChC,MAAMkB,mBAAmBnB,KAAKoB,IAAI,CAChCH,YAAYI,aAAa,CAACrB,IAAI,EAC9B,GAAGkB,KAAK,GAAG,CAAC;gBAEdpB,YAAYqB;YACd;YAEA,8DAA8D;YAC9D,oDAAoD;YACpD,mCAAmC;YACnC,MAAMG,UAAU;mBAAIL,YAAYM,WAAW,CAACC,IAAI;aAAG,CAACC,MAAM,CAAC,CAACC;gBAC1D,MAAMC,YAAYD,MAAME,QAAQ,GAAGC,UAAU,CAAC;gBAC9C,OAAOH,MAAME,QAAQ,GAAGC,UAAU,CAAC,aAAaF;YAClD;YAEA,KAAK,MAAMG,QAAQR,QAAS;gBAC1B,MAAMS,aAAa/B,KAAKoB,IAAI,CAC1BH,YAAYI,aAAa,CAACrB,IAAI,EAC9B8B,OAAO;gBAEThC,YAAYiC;YACd;QACF;IACF;AACF"}