# 🚀 دليل الإعداد السريع - Z.ai Code Scaffold

## 📋 متطلبات النظام

- **Node.js:** الإصدار 18.0.0 أو أحدث
- **npm:** الإصدار 8.0.0 أو أحدث
- **Git:** للتحكم في الإصدارات

## ⚡ الإعداد السريع (5 دقائق)

### 1️⃣ تثبيت التبعيات
```bash
# تثبيت جميع الحزم المطلوبة
npm install
```

### 2️⃣ إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات وتطبيق المخططات
npm run db:push

# توليد عميل Prisma
npm run db:generate
```

### 3️⃣ إنشاء ملف البيئة
```bash
# إنشاء ملف متغيرات البيئة
echo 'DATABASE_URL="file:./db/custom.db"' > .env.local
echo 'NEXTAUTH_SECRET="your-secret-key-here"' >> .env.local
echo 'NEXTAUTH_URL="http://localhost:3000"' >> .env.local
```

### 4️⃣ تشغيل المشروع
```bash
# تشغيل خادم التطوير
npm run dev
```

🎉 **تهانينا!** المشروع يعمل الآن على: http://localhost:3000

## 🔧 إعدادات متقدمة

### 🗄️ قاعدة البيانات

#### SQLite (افتراضي - للتطوير)
```env
DATABASE_URL="file:./db/custom.db"
```

#### PostgreSQL (للإنتاج)
```env
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"
```

#### MySQL
```env
DATABASE_URL="mysql://username:password@localhost:3306/database_name"
```

### 🔐 إعداد المصادقة

#### Google OAuth
```env
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
```

#### GitHub OAuth
```env
GITHUB_ID="your-github-client-id"
GITHUB_SECRET="your-github-client-secret"
```

#### Discord OAuth
```env
DISCORD_CLIENT_ID="your-discord-client-id"
DISCORD_CLIENT_SECRET="your-discord-client-secret"
```

### 🌐 إعدادات Socket.IO
```env
SOCKET_IO_PORT="3001"
SOCKET_IO_CORS_ORIGIN="http://localhost:3000"
```

## 📁 بنية الملفات المهمة

```
📁 المشروع/
├── 📄 .env.local              # متغيرات البيئة (لا تُرفع لـ Git)
├── 📄 .env.example            # مثال على متغيرات البيئة
├── 📁 src/
│   ├── 📁 app/
│   │   ├── 📄 layout.tsx      # تخطيط التطبيق الرئيسي
│   │   ├── 📄 page.tsx        # الصفحة الرئيسية
│   │   └── 📁 api/           # API Routes
│   ├── 📁 components/
│   │   └── 📁 ui/            # مكونات shadcn/ui
│   ├── 📁 lib/
│   │   ├── 📄 db.ts          # إعداد قاعدة البيانات
│   │   ├── 📄 utils.ts       # وظائف مساعدة
│   │   └── 📄 socket.ts      # إعداد Socket.IO
│   └── 📁 hooks/             # React Hooks مخصصة
├── 📁 prisma/
│   └── 📄 schema.prisma      # مخطط قاعدة البيانات
└── 📁 public/                # الملفات العامة
```

## 🛠️ أوامر التطوير المفيدة

### 🔄 إدارة قاعدة البيانات
```bash
# عرض البيانات في المتصفح
npx prisma studio

# إعادة تعيين قاعدة البيانات
npm run db:reset

# إنشاء migration جديد
npm run db:migrate

# تطبيق التغييرات على قاعدة البيانات
npm run db:push
```

### 🧹 جودة الكود
```bash
# فحص الكود
npm run lint

# إصلاح مشاكل الكود تلقائياً
npm run lint -- --fix

# فحص TypeScript
npx tsc --noEmit
```

### 📦 إدارة الحزم
```bash
# إضافة حزمة جديدة
npm install package-name

# إضافة حزمة للتطوير فقط
npm install -D package-name

# تحديث جميع الحزم
npm update
```

## 🚀 النشر

### Vercel (مُوصى به)
```bash
# تثبيت Vercel CLI
npm i -g vercel

# نشر المشروع
vercel
```

### Netlify
```bash
# بناء المشروع
npm run build

# رفع مجلد .next إلى Netlify
```

### Docker
```dockerfile
# إنشاء Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## 🔍 استكشاف الأخطاء

### ❌ مشاكل شائعة وحلولها

#### خطأ في قاعدة البيانات
```bash
# حذف قاعدة البيانات وإعادة إنشائها
rm -rf db/custom.db
npm run db:push
```

#### خطأ في التبعيات
```bash
# حذف node_modules وإعادة التثبيت
rm -rf node_modules package-lock.json
npm install
```

#### خطأ في TypeScript
```bash
# إعادة بناء أنواع TypeScript
npm run db:generate
npx tsc --noEmit
```

#### مشكلة في Socket.IO
```bash
# التحقق من تشغيل الخادم
curl http://localhost:3000/api/health
```

## 📞 الدعم والمساعدة

- **التوثيق الرسمي:** [Next.js Docs](https://nextjs.org/docs)
- **مجتمع shadcn/ui:** [GitHub](https://github.com/shadcn-ui/ui)
- **Prisma Docs:** [prisma.io](https://www.prisma.io/docs)
- **Z.ai Support:** [chat.z.ai](https://chat.z.ai)

## 🎯 نصائح للتطوير

1. **استخدم TypeScript بالكامل** - لا تستخدم `any`
2. **اتبع اصطلاحات التسمية** - camelCase للمتغيرات، PascalCase للمكونات
3. **اكتب تعليقات واضحة** - خاصة للوظائف المعقدة
4. **استخدم Git بانتظام** - commit صغيرة ومتكررة
5. **اختبر التغييرات** - قبل الـ commit

---

🎉 **مبروك!** أنت الآن جاهز لبناء تطبيقات رائعة مع Z.ai Code Scaffold!
