/// <reference types="node" />
/**
 * (Nonstandard) String utility function for 8-bit ASCII with the extended
 * character set. Unlike the ASCII above, we do not mask the high bits.
 *
 * Placed into a separate file so it can be used with other Buffer implementations.
 * @see http://en.wikipedia.org/wiki/Extended_ASCII
 */
export default class ExtendedASCII {
    private static extendedChars;
    static str2byte(str: string, buf: Buffer): number;
    static byte2str(buff: Buffer): string;
    static byteLength(str: string): number;
}
