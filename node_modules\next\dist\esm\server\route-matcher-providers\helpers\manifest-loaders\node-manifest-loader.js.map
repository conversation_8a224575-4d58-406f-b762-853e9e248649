{"version": 3, "sources": ["../../../../../src/server/route-matcher-providers/helpers/manifest-loaders/node-manifest-loader.ts"], "sourcesContent": ["import { SERVER_DIRECTORY } from '../../../../shared/lib/constants'\nimport path from '../../../../shared/lib/isomorphic/path'\nimport type { <PERSON>ife<PERSON>, ManifestLoader } from './manifest-loader'\n\nexport class NodeManifestLoader implements ManifestLoader {\n  constructor(private readonly distDir: string) {}\n\n  static require(id: string) {\n    try {\n      return require(id)\n    } catch {\n      return null\n    }\n  }\n\n  public load(name: string): Manifest | null {\n    return NodeManifestLoader.require(\n      path.join(this.distDir, SERVER_DIRECTORY, name)\n    )\n  }\n}\n"], "names": ["SERVER_DIRECTORY", "path", "NodeManifestLoader", "constructor", "distDir", "require", "id", "load", "name", "join"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,mCAAkC;AACnE,OAAOC,UAAU,yCAAwC;AAGzD,OAAO,MAAMC;IACXC,YAAY,AAAiBC,OAAe,CAAE;aAAjBA,UAAAA;IAAkB;IAE/C,OAAOC,QAAQC,EAAU,EAAE;QACzB,IAAI;YACF,OAAOD,QAAQC;QACjB,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEOC,KAAKC,IAAY,EAAmB;QACzC,OAAON,mBAAmBG,OAAO,CAC/BJ,KAAKQ,IAAI,CAAC,IAAI,CAACL,OAAO,EAAEJ,kBAAkBQ;IAE9C;AACF"}