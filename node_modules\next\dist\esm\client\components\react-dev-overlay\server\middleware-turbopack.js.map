{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/server/middleware-turbopack.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport {\n  getOriginalCodeFrame,\n  type OriginalStackFrameResponse,\n  type OriginalStackFramesRequest,\n  type OriginalStackFramesResponse,\n} from './shared'\nimport { middlewareResponse } from './middleware-response'\nimport fs, { constants as FS } from 'fs/promises'\nimport path from 'path'\nimport url from 'url'\nimport { launchEditor } from '../utils/launch-editor'\nimport type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport {\n  SourceMapConsumer,\n  type BasicSourceMapConsumer,\n  type NullableMappedPosition,\n} from 'next/dist/compiled/source-map08'\nimport type { Project, TurbopackStackFrame } from '../../../../build/swc/types'\nimport { getSourceMapFromFile } from '../utils/get-source-map-from-file'\nimport { findSourceMap, type SourceMapPayload } from 'node:module'\nimport { pathToFileURL } from 'node:url'\nimport { inspect } from 'node:util'\n\nfunction shouldIgnorePath(modulePath: string): boolean {\n  return (\n    modulePath.includes('node_modules') ||\n    // Only relevant for when Next.js is symlinked e.g. in the Next.js monorepo\n    modulePath.includes('next/dist') ||\n    modulePath.startsWith('node:')\n  )\n}\n\ntype IgnorableStackFrame = StackFrame & { ignored: boolean }\n\nconst currentSourcesByFile: Map<string, Promise<string | null>> = new Map()\nasync function batchedTraceSource(\n  project: Project,\n  frame: TurbopackStackFrame\n): Promise<{ frame: IgnorableStackFrame; source: string | null } | undefined> {\n  const file = frame.file\n    ? // TODO(veil): Why are the frames sent encoded?\n      decodeURIComponent(frame.file)\n    : undefined\n\n  if (!file) return\n\n  // For node internals they cannot traced the actual source code with project.traceSource,\n  // we need an early return to indicate it's ignored to avoid the unknown scheme error from `project.traceSource`.\n  if (file.startsWith('node:')) {\n    return {\n      frame: {\n        file,\n        lineNumber: frame.line ?? 0,\n        column: frame.column ?? 0,\n        methodName: frame.methodName ?? '<unknown>',\n        ignored: true,\n        arguments: [],\n      },\n      source: null,\n    }\n  }\n\n  const currentDirectoryFileUrl = pathToFileURL(process.cwd()).href\n\n  const sourceFrame = await project.traceSource(frame, currentDirectoryFileUrl)\n  if (!sourceFrame) {\n    return {\n      frame: {\n        file,\n        lineNumber: frame.line ?? 0,\n        column: frame.column ?? 0,\n        methodName: frame.methodName ?? '<unknown>',\n        ignored: shouldIgnorePath(file),\n        arguments: [],\n      },\n      source: null,\n    }\n  }\n\n  let source = null\n  const originalFile = sourceFrame.originalFile\n\n  // Don't look up source for node_modules or internals. These can often be large bundled files.\n  const ignored =\n    shouldIgnorePath(originalFile ?? sourceFrame.file) ||\n    // isInternal means resource starts with turbopack:///[turbopack]\n    !!sourceFrame.isInternal\n  if (originalFile && !ignored) {\n    let sourcePromise = currentSourcesByFile.get(originalFile)\n    if (!sourcePromise) {\n      sourcePromise = project.getSourceForAsset(originalFile)\n      currentSourcesByFile.set(originalFile, sourcePromise)\n      setTimeout(() => {\n        // Cache file reads for 100ms, as frames will often reference the same\n        // files and can be large.\n        currentSourcesByFile.delete(originalFile!)\n      }, 100)\n    }\n    source = await sourcePromise\n  }\n\n  // TODO: get ignoredList from turbopack source map\n  const ignorableFrame = {\n    file: sourceFrame.file,\n    lineNumber: sourceFrame.line ?? 0,\n    column: sourceFrame.column ?? 0,\n    methodName:\n      // We ignore the sourcemapped name since it won't be the correct name.\n      // The callsite will point to the column of the variable name instead of the\n      // name of the enclosing function.\n      // TODO(NDX-531): Spy on prepareStackTrace to get the enclosing line number for method name mapping.\n      frame.methodName ?? '<unknown>',\n    ignored,\n    arguments: [],\n  }\n\n  return {\n    frame: ignorableFrame,\n    source,\n  }\n}\nfunction parseFile(fileParam: string | null): string | undefined {\n  if (!fileParam) {\n    return undefined\n  }\n\n  // rsc://React/Server/file://<filename>?42 => file://<filename>\n  return fileParam.replace(/^rsc:\\/\\/React\\/[^/]+\\//, '').replace(/\\?\\d+$/, '')\n}\n\nfunction createStackFrames(\n  body: OriginalStackFramesRequest\n): TurbopackStackFrame[] {\n  const { frames, isServer } = body\n\n  return frames\n    .map((frame): TurbopackStackFrame | undefined => {\n      const file = parseFile(frame.file)\n\n      if (!file) {\n        return undefined\n      }\n\n      return {\n        file,\n        methodName: frame.methodName ?? '<unknown>',\n        line: frame.lineNumber ?? 0,\n        column: frame.column ?? 0,\n        isServer,\n      } satisfies TurbopackStackFrame\n    })\n    .filter((f): f is TurbopackStackFrame => f !== undefined)\n}\n\nfunction createStackFrame(\n  searchParams: URLSearchParams\n): TurbopackStackFrame | undefined {\n  const file = parseFile(searchParams.get('file'))\n\n  if (!file) {\n    return undefined\n  }\n\n  return {\n    file,\n    methodName: searchParams.get('methodName') ?? '<unknown>',\n    line: parseInt(searchParams.get('lineNumber') ?? '0', 10) || 0,\n    column: parseInt(searchParams.get('column') ?? '0', 10) || 0,\n    isServer: searchParams.get('isServer') === 'true',\n  } satisfies TurbopackStackFrame\n}\n\n/**\n * https://tc39.es/source-map/#index-map\n */\ninterface IndexSourceMapSection {\n  offset: {\n    line: number\n    column: number\n  }\n  map: ModernRawSourceMap\n}\n\n// TODO(veil): Upstream types\ninterface IndexSourceMap {\n  version: number\n  file: string\n  sections: IndexSourceMapSection[]\n}\n\ninterface ModernRawSourceMap extends SourceMapPayload {\n  ignoreList?: number[]\n}\n\ntype ModernSourceMapPayload = ModernRawSourceMap | IndexSourceMap\n\n/**\n * Finds the sourcemap payload applicable to a given frame.\n * Equal to the input unless an Index Source Map is used.\n */\nfunction findApplicableSourceMapPayload(\n  frame: TurbopackStackFrame,\n  payload: ModernSourceMapPayload\n): ModernRawSourceMap | undefined {\n  if ('sections' in payload) {\n    const frameLine = frame.line ?? 0\n    const frameColumn = frame.column ?? 0\n    // Sections must not overlap and must be sorted: https://tc39.es/source-map/#section-object\n    // Therefore the last section that has an offset less than or equal to the frame is the applicable one.\n    // TODO(veil): Binary search\n    let section: IndexSourceMapSection | undefined = payload.sections[0]\n    for (\n      let i = 0;\n      i < payload.sections.length &&\n      payload.sections[i].offset.line <= frameLine &&\n      payload.sections[i].offset.column <= frameColumn;\n      i++\n    ) {\n      section = payload.sections[i]\n    }\n\n    return section === undefined ? undefined : section.map\n  } else {\n    return payload\n  }\n}\n\n/**\n * @returns 1-based lines and 0-based columns\n */\nasync function nativeTraceSource(\n  frame: TurbopackStackFrame\n): Promise<{ frame: IgnorableStackFrame; source: string | null } | undefined> {\n  const sourceURL = // TODO(veil): Why are the frames sent encoded?\n    decodeURIComponent(frame.file)\n  let sourceMapPayload: ModernSourceMapPayload | undefined\n  try {\n    sourceMapPayload = findSourceMap(sourceURL)?.payload\n  } catch (cause) {\n    throw new Error(\n      `${sourceURL}: Invalid source map. Only conformant source maps can be used to find the original code.`,\n      { cause }\n    )\n  }\n\n  if (sourceMapPayload !== undefined) {\n    let consumer: BasicSourceMapConsumer\n    try {\n      consumer = await new SourceMapConsumer(sourceMapPayload)\n    } catch (cause) {\n      throw new Error(\n        `${sourceURL}: Invalid source map. Only conformant source maps can be used to find the original code.`,\n        { cause }\n      )\n    }\n    let traced: {\n      originalPosition: NullableMappedPosition\n      sourceContent: string | null\n    } | null\n    try {\n      const originalPosition = consumer.originalPositionFor({\n        line: frame.line ?? 1,\n        // 0-based columns out requires 0-based columns in.\n        column: (frame.column ?? 1) - 1,\n      })\n\n      if (originalPosition.source === null) {\n        traced = null\n      } else {\n        const sourceContent: string | null =\n          consumer.sourceContentFor(\n            originalPosition.source,\n            /* returnNullOnMissing */ true\n          ) ?? null\n\n        traced = { originalPosition, sourceContent }\n      }\n    } finally {\n      consumer.destroy()\n    }\n\n    if (traced !== null) {\n      const { originalPosition, sourceContent } = traced\n      const applicableSourceMap = findApplicableSourceMapPayload(\n        frame,\n        sourceMapPayload\n      )\n\n      // TODO(veil): Upstream a method to sourcemap consumer that immediately says if a frame is ignored or not.\n      let ignored = false\n      if (applicableSourceMap === undefined) {\n        console.error(\n          'No applicable source map found in sections for frame',\n          frame\n        )\n      } else {\n        // TODO: O(n^2). Consider moving `ignoreList` into a Set\n        const sourceIndex = applicableSourceMap.sources.indexOf(\n          originalPosition.source!\n        )\n        ignored =\n          applicableSourceMap.ignoreList?.includes(sourceIndex) ??\n          // When sourcemap is not available, fallback to checking `frame.file`.\n          // e.g. In pages router, nextjs server code is not bundled into the page.\n          shouldIgnorePath(frame.file)\n      }\n\n      const originalStackFrame: IgnorableStackFrame = {\n        methodName:\n          // We ignore the sourcemapped name since it won't be the correct name.\n          // The callsite will point to the column of the variable name instead of the\n          // name of the enclosing function.\n          // TODO(NDX-531): Spy on prepareStackTrace to get the enclosing line number for method name mapping.\n          frame.methodName\n            ?.replace('__WEBPACK_DEFAULT_EXPORT__', 'default')\n            ?.replace('__webpack_exports__.', '') || '<unknown>',\n        column: (originalPosition.column ?? 0) + 1,\n        file: originalPosition.source,\n        lineNumber: originalPosition.line ?? 0,\n        // TODO: c&p from async createOriginalStackFrame but why not frame.arguments?\n        arguments: [],\n        ignored,\n      }\n\n      return {\n        frame: originalStackFrame,\n        source: sourceContent,\n      }\n    }\n  }\n\n  return undefined\n}\n\nasync function createOriginalStackFrame(\n  project: Project,\n  projectPath: string,\n  frame: TurbopackStackFrame\n): Promise<OriginalStackFrameResponse | null> {\n  const traced =\n    (await nativeTraceSource(frame)) ??\n    // TODO(veil): When would the bundler know more than native?\n    // If it's faster, try the bundler first and fall back to native later.\n    (await batchedTraceSource(project, frame))\n  if (!traced) {\n    return null\n  }\n\n  let normalizedStackFrameLocation = traced.frame.file\n  if (\n    normalizedStackFrameLocation !== null &&\n    normalizedStackFrameLocation.startsWith('file://')\n  ) {\n    normalizedStackFrameLocation = path.relative(\n      projectPath,\n      url.fileURLToPath(normalizedStackFrameLocation)\n    )\n  }\n\n  return {\n    originalStackFrame: {\n      arguments: traced.frame.arguments,\n      column: traced.frame.column,\n      file: normalizedStackFrameLocation,\n      ignored: traced.frame.ignored,\n      lineNumber: traced.frame.lineNumber,\n      methodName: traced.frame.methodName,\n    },\n    originalCodeFrame: getOriginalCodeFrame(traced.frame, traced.source),\n  }\n}\n\nexport function getOverlayMiddleware(project: Project, projectPath: string) {\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(req.url!, 'http://n')\n\n    if (pathname === '/__nextjs_original-stack-frames') {\n      if (req.method !== 'POST') {\n        return middlewareResponse.badRequest(res)\n      }\n\n      const body = await new Promise<string>((resolve, reject) => {\n        let data = ''\n        req.on('data', (chunk) => {\n          data += chunk\n        })\n        req.on('end', () => resolve(data))\n        req.on('error', reject)\n      })\n\n      const request = JSON.parse(body) as OriginalStackFramesRequest\n      const stackFrames = createStackFrames(request)\n      const result: OriginalStackFramesResponse = await Promise.all(\n        stackFrames.map(async (frame) => {\n          try {\n            const stackFrame = await createOriginalStackFrame(\n              project,\n              projectPath,\n              frame\n            )\n            if (stackFrame === null) {\n              return {\n                status: 'rejected',\n                reason: 'Failed to create original stack frame',\n              }\n            }\n            return { status: 'fulfilled', value: stackFrame }\n          } catch (error) {\n            return {\n              status: 'rejected',\n              reason: inspect(error, { colors: false }),\n            }\n          }\n        })\n      )\n\n      return middlewareResponse.json(res, result)\n    } else if (pathname === '/__nextjs_launch-editor') {\n      const frame = createStackFrame(searchParams)\n\n      if (!frame) return middlewareResponse.badRequest(res)\n\n      const fileExists = await fs.access(frame.file, FS.F_OK).then(\n        () => true,\n        () => false\n      )\n      if (!fileExists) return middlewareResponse.notFound(res)\n\n      try {\n        launchEditor(frame.file, frame.line ?? 1, frame.column ?? 1)\n      } catch (err) {\n        console.log('Failed to launch editor:', err)\n        return middlewareResponse.internalServerError(res)\n      }\n\n      return middlewareResponse.noContent(res)\n    }\n\n    return next()\n  }\n}\n\nexport function getSourceMapMiddleware(project: Project) {\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(req.url!, 'http://n')\n\n    if (pathname !== '/__nextjs_source-map') {\n      return next()\n    }\n\n    let filename = searchParams.get('filename')\n\n    if (!filename) {\n      return middlewareResponse.badRequest(res)\n    }\n\n    // TODO(veil): Always try the native version first.\n    // Externals could also be files that aren't bundled via Webpack.\n    if (\n      filename.startsWith('webpack://') ||\n      filename.startsWith('webpack-internal:///')\n    ) {\n      const sourceMap = findSourceMap(filename)\n\n      if (sourceMap) {\n        return middlewareResponse.json(res, sourceMap.payload)\n      }\n\n      return middlewareResponse.noContent(res)\n    }\n\n    try {\n      // Turbopack chunk filenames might be URL-encoded.\n      filename = decodeURI(filename)\n\n      if (path.isAbsolute(filename)) {\n        filename = url.pathToFileURL(filename).href\n      }\n\n      const sourceMapString = await project.getSourceMap(filename)\n\n      if (sourceMapString) {\n        return middlewareResponse.jsonString(res, sourceMapString)\n      }\n\n      if (filename.startsWith('file:')) {\n        const sourceMap = await getSourceMapFromFile(filename)\n\n        if (sourceMap) {\n          return middlewareResponse.json(res, sourceMap)\n        }\n      }\n    } catch (error) {\n      console.error('Failed to get source map:', error)\n    }\n\n    middlewareResponse.noContent(res)\n  }\n}\n"], "names": ["getOriginalCodeFrame", "middlewareResponse", "fs", "constants", "FS", "path", "url", "launchEditor", "SourceMapConsumer", "getSourceMapFromFile", "findSourceMap", "pathToFileURL", "inspect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modulePath", "includes", "startsWith", "currentSourcesByFile", "Map", "batchedTraceSource", "project", "frame", "file", "decodeURIComponent", "undefined", "lineNumber", "line", "column", "methodName", "ignored", "arguments", "source", "currentDirectoryFileUrl", "process", "cwd", "href", "sourceFrame", "traceSource", "originalFile", "isInternal", "sourcePromise", "get", "getSourceForAsset", "set", "setTimeout", "delete", "ignorableFrame", "parseFile", "fileParam", "replace", "createStackFrames", "body", "frames", "isServer", "map", "filter", "f", "createStackFrame", "searchParams", "parseInt", "findApplicableSourceMapPayload", "payload", "frameLine", "frameColumn", "section", "sections", "i", "length", "offset", "nativeTraceSource", "sourceURL", "sourceMapPayload", "cause", "Error", "consumer", "traced", "originalPosition", "originalPositionFor", "sourceContent", "sourceContentFor", "destroy", "applicableSourceMap", "console", "error", "sourceIndex", "sources", "indexOf", "ignoreList", "originalStackFrame", "createOriginalStackFrame", "projectPath", "normalizedStackFrameLocation", "relative", "fileURLToPath", "originalCodeFrame", "getOverlayMiddleware", "req", "res", "next", "pathname", "URL", "method", "badRequest", "Promise", "resolve", "reject", "data", "on", "chunk", "request", "JSON", "parse", "stackFrames", "result", "all", "stackFrame", "status", "reason", "value", "colors", "json", "fileExists", "access", "F_OK", "then", "notFound", "err", "log", "internalServerError", "noContent", "getSourceMapMiddleware", "filename", "sourceMap", "decodeURI", "isAbsolute", "sourceMapString", "getSourceMap", "jsonString"], "mappings": "AACA,SACEA,oBAAoB,QAIf,WAAU;AACjB,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,OAAOC,MAAMC,aAAaC,EAAE,QAAQ,cAAa;AACjD,OAAOC,UAAU,OAAM;AACvB,OAAOC,SAAS,MAAK;AACrB,SAASC,YAAY,QAAQ,yBAAwB;AAErD,SACEC,iBAAiB,QAGZ,kCAAiC;AAExC,SAASC,oBAAoB,QAAQ,oCAAmC;AACxE,SAASC,aAAa,QAA+B,cAAa;AAClE,SAASC,aAAa,QAAQ,WAAU;AACxC,SAASC,OAAO,QAAQ,YAAW;AAEnC,SAASC,iBAAiBC,UAAkB;IAC1C,OACEA,WAAWC,QAAQ,CAAC,mBACpB,2EAA2E;IAC3ED,WAAWC,QAAQ,CAAC,gBACpBD,WAAWE,UAAU,CAAC;AAE1B;AAIA,MAAMC,uBAA4D,IAAIC;AACtE,eAAeC,mBACbC,OAAgB,EAChBC,KAA0B;IAE1B,MAAMC,OAAOD,MAAMC,IAAI,GAEnBC,mBAAmBF,MAAMC,IAAI,IAC7BE;IAEJ,IAAI,CAACF,MAAM;IAEX,yFAAyF;IACzF,iHAAiH;IACjH,IAAIA,KAAKN,UAAU,CAAC,UAAU;YAIZK,aACJA,eACIA;QALhB,OAAO;YACLA,OAAO;gBACLC;gBACAG,YAAYJ,CAAAA,cAAAA,MAAMK,IAAI,YAAVL,cAAc;gBAC1BM,QAAQN,CAAAA,gBAAAA,MAAMM,MAAM,YAAZN,gBAAgB;gBACxBO,YAAYP,CAAAA,oBAAAA,MAAMO,UAAU,YAAhBP,oBAAoB;gBAChCQ,SAAS;gBACTC,WAAW,EAAE;YACf;YACAC,QAAQ;QACV;IACF;IAEA,MAAMC,0BAA0BrB,cAAcsB,QAAQC,GAAG,IAAIC,IAAI;IAEjE,MAAMC,cAAc,MAAMhB,QAAQiB,WAAW,CAAChB,OAAOW;IACrD,IAAI,CAACI,aAAa;YAIAf,cACJA,gBACIA;QALhB,OAAO;YACLA,OAAO;gBACLC;gBACAG,YAAYJ,CAAAA,eAAAA,MAAMK,IAAI,YAAVL,eAAc;gBAC1BM,QAAQN,CAAAA,iBAAAA,MAAMM,MAAM,YAAZN,iBAAgB;gBACxBO,YAAYP,CAAAA,qBAAAA,MAAMO,UAAU,YAAhBP,qBAAoB;gBAChCQ,SAAShB,iBAAiBS;gBAC1BQ,WAAW,EAAE;YACf;YACAC,QAAQ;QACV;IACF;IAEA,IAAIA,SAAS;IACb,MAAMO,eAAeF,YAAYE,YAAY;IAE7C,8FAA8F;IAC9F,MAAMT,UACJhB,iBAAiByB,uBAAAA,eAAgBF,YAAYd,IAAI,KACjD,iEAAiE;IACjE,CAAC,CAACc,YAAYG,UAAU;IAC1B,IAAID,gBAAgB,CAACT,SAAS;QAC5B,IAAIW,gBAAgBvB,qBAAqBwB,GAAG,CAACH;QAC7C,IAAI,CAACE,eAAe;YAClBA,gBAAgBpB,QAAQsB,iBAAiB,CAACJ;YAC1CrB,qBAAqB0B,GAAG,CAACL,cAAcE;YACvCI,WAAW;gBACT,sEAAsE;gBACtE,0BAA0B;gBAC1B3B,qBAAqB4B,MAAM,CAACP;YAC9B,GAAG;QACL;QACAP,SAAS,MAAMS;IACjB;QAKcJ,mBACJA,qBAEN,sEAAsE;IACtE,4EAA4E;IAC5E,kCAAkC;IAClC,oGAAoG;IACpGf;IAVJ,kDAAkD;IAClD,MAAMyB,iBAAiB;QACrBxB,MAAMc,YAAYd,IAAI;QACtBG,YAAYW,CAAAA,oBAAAA,YAAYV,IAAI,YAAhBU,oBAAoB;QAChCT,QAAQS,CAAAA,sBAAAA,YAAYT,MAAM,YAAlBS,sBAAsB;QAC9BR,YAKEP,CAAAA,qBAAAA,MAAMO,UAAU,YAAhBP,qBAAoB;QACtBQ;QACAC,WAAW,EAAE;IACf;IAEA,OAAO;QACLT,OAAOyB;QACPf;IACF;AACF;AACA,SAASgB,UAAUC,SAAwB;IACzC,IAAI,CAACA,WAAW;QACd,OAAOxB;IACT;IAEA,+DAA+D;IAC/D,OAAOwB,UAAUC,OAAO,CAAC,2BAA2B,IAAIA,OAAO,CAAC,UAAU;AAC5E;AAEA,SAASC,kBACPC,IAAgC;IAEhC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAE,GAAGF;IAE7B,OAAOC,OACJE,GAAG,CAAC,CAACjC;QACJ,MAAMC,OAAOyB,UAAU1B,MAAMC,IAAI;QAEjC,IAAI,CAACA,MAAM;YACT,OAAOE;QACT;YAIcH,mBACNA,mBACEA;QAJV,OAAO;YACLC;YACAM,YAAYP,CAAAA,oBAAAA,MAAMO,UAAU,YAAhBP,oBAAoB;YAChCK,MAAML,CAAAA,oBAAAA,MAAMI,UAAU,YAAhBJ,oBAAoB;YAC1BM,QAAQN,CAAAA,gBAAAA,MAAMM,MAAM,YAAZN,gBAAgB;YACxBgC;QACF;IACF,GACCE,MAAM,CAAC,CAACC,IAAgCA,MAAMhC;AACnD;AAEA,SAASiC,iBACPC,YAA6B;IAE7B,MAAMpC,OAAOyB,UAAUW,aAAajB,GAAG,CAAC;IAExC,IAAI,CAACnB,MAAM;QACT,OAAOE;IACT;QAIckC,mBACGA,oBACEA;IAJnB,OAAO;QACLpC;QACAM,YAAY8B,CAAAA,oBAAAA,aAAajB,GAAG,CAAC,yBAAjBiB,oBAAkC;QAC9ChC,MAAMiC,SAASD,CAAAA,qBAAAA,aAAajB,GAAG,CAAC,yBAAjBiB,qBAAkC,KAAK,OAAO;QAC7D/B,QAAQgC,SAASD,CAAAA,qBAAAA,aAAajB,GAAG,CAAC,qBAAjBiB,qBAA8B,KAAK,OAAO;QAC3DL,UAAUK,aAAajB,GAAG,CAAC,gBAAgB;IAC7C;AACF;AA0BA;;;CAGC,GACD,SAASmB,+BACPvC,KAA0B,EAC1BwC,OAA+B;IAE/B,IAAI,cAAcA,SAAS;YACPxC;QAAlB,MAAMyC,YAAYzC,CAAAA,cAAAA,MAAMK,IAAI,YAAVL,cAAc;YACZA;QAApB,MAAM0C,cAAc1C,CAAAA,gBAAAA,MAAMM,MAAM,YAAZN,gBAAgB;QACpC,2FAA2F;QAC3F,uGAAuG;QACvG,4BAA4B;QAC5B,IAAI2C,UAA6CH,QAAQI,QAAQ,CAAC,EAAE;QACpE,IACE,IAAIC,IAAI,GACRA,IAAIL,QAAQI,QAAQ,CAACE,MAAM,IAC3BN,QAAQI,QAAQ,CAACC,EAAE,CAACE,MAAM,CAAC1C,IAAI,IAAIoC,aACnCD,QAAQI,QAAQ,CAACC,EAAE,CAACE,MAAM,CAACzC,MAAM,IAAIoC,aACrCG,IACA;YACAF,UAAUH,QAAQI,QAAQ,CAACC,EAAE;QAC/B;QAEA,OAAOF,YAAYxC,YAAYA,YAAYwC,QAAQV,GAAG;IACxD,OAAO;QACL,OAAOO;IACT;AACF;AAEA;;CAEC,GACD,eAAeQ,kBACbhD,KAA0B;IAE1B,MAAMiD,YACJ/C,mBAAmBF,MAAMC,IAAI;IAC/B,IAAIiD;IACJ,IAAI;YACiB7D;QAAnB6D,oBAAmB7D,iBAAAA,cAAc4D,+BAAd5D,eAA0BmD,OAAO;IACtD,EAAE,OAAOW,OAAO;QACd,MAAM,qBAGL,CAHK,IAAIC,MACR,AAAC,KAAEH,YAAU,4FACb;YAAEE;QAAM,IAFJ,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEA,IAAID,qBAAqB/C,WAAW;QAClC,IAAIkD;QACJ,IAAI;YACFA,WAAW,MAAM,IAAIlE,kBAAkB+D;QACzC,EAAE,OAAOC,OAAO;YACd,MAAM,qBAGL,CAHK,IAAIC,MACR,AAAC,KAAEH,YAAU,4FACb;gBAAEE;YAAM,IAFJ,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QACA,IAAIG;QAIJ,IAAI;gBAEMtD,aAEGA;YAHX,MAAMuD,mBAAmBF,SAASG,mBAAmB,CAAC;gBACpDnD,MAAML,CAAAA,cAAAA,MAAMK,IAAI,YAAVL,cAAc;gBACpB,mDAAmD;gBACnDM,QAAQ,AAACN,CAAAA,CAAAA,gBAAAA,MAAMM,MAAM,YAAZN,gBAAgB,CAAA,IAAK;YAChC;YAEA,IAAIuD,iBAAiB7C,MAAM,KAAK,MAAM;gBACpC4C,SAAS;YACX,OAAO;oBAEHD;gBADF,MAAMI,gBACJJ,CAAAA,6BAAAA,SAASK,gBAAgB,CACvBH,iBAAiB7C,MAAM,EACvB,uBAAuB,GAAG,iBAF5B2C,6BAGK;gBAEPC,SAAS;oBAAEC;oBAAkBE;gBAAc;YAC7C;QACF,SAAU;YACRJ,SAASM,OAAO;QAClB;QAEA,IAAIL,WAAW,MAAM;gBA4Bf,sEAAsE;YACtE,4EAA4E;YAC5E,kCAAkC;YAClC,oGAAoG;YACpGtD,2BAAAA;YA/BJ,MAAM,EAAEuD,gBAAgB,EAAEE,aAAa,EAAE,GAAGH;YAC5C,MAAMM,sBAAsBrB,+BAC1BvC,OACAkD;YAGF,0GAA0G;YAC1G,IAAI1C,UAAU;YACd,IAAIoD,wBAAwBzD,WAAW;gBACrC0D,QAAQC,KAAK,CACX,wDACA9D;YAEJ,OAAO;oBAMH4D;gBALF,wDAAwD;gBACxD,MAAMG,cAAcH,oBAAoBI,OAAO,CAACC,OAAO,CACrDV,iBAAiB7C,MAAM;oBAGvBkD;gBADFpD,UACEoD,CAAAA,4CAAAA,kCAAAA,oBAAoBM,UAAU,qBAA9BN,gCAAgClE,QAAQ,CAACqE,wBAAzCH,2CACA,sEAAsE;gBACtE,yEAAyE;gBACzEpE,iBAAiBQ,MAAMC,IAAI;YAC/B;gBAWWsD,0BAEGA;YAXd,MAAMY,qBAA0C;gBAC9C5D,YAKEP,EAAAA,oBAAAA,MAAMO,UAAU,sBAAhBP,4BAAAA,kBACI4B,OAAO,CAAC,8BAA8B,+BAD1C5B,0BAEI4B,OAAO,CAAC,wBAAwB,QAAO;gBAC7CtB,QAAQ,AAACiD,CAAAA,CAAAA,2BAAAA,iBAAiBjD,MAAM,YAAvBiD,2BAA2B,CAAA,IAAK;gBACzCtD,MAAMsD,iBAAiB7C,MAAM;gBAC7BN,YAAYmD,CAAAA,yBAAAA,iBAAiBlD,IAAI,YAArBkD,yBAAyB;gBACrC,6EAA6E;gBAC7E9C,WAAW,EAAE;gBACbD;YACF;YAEA,OAAO;gBACLR,OAAOmE;gBACPzD,QAAQ+C;YACV;QACF;IACF;IAEA,OAAOtD;AACT;AAEA,eAAeiE,yBACbrE,OAAgB,EAChBsE,WAAmB,EACnBrE,KAA0B;QAGvB;IADH,MAAMsD,SACJ,CAAC,OAAA,MAAMN,kBAAkBhD,kBAAxB,OACD,4DAA4D;IAC5D,uEAAuE;IACtE,MAAMF,mBAAmBC,SAASC;IACrC,IAAI,CAACsD,QAAQ;QACX,OAAO;IACT;IAEA,IAAIgB,+BAA+BhB,OAAOtD,KAAK,CAACC,IAAI;IACpD,IACEqE,iCAAiC,QACjCA,6BAA6B3E,UAAU,CAAC,YACxC;QACA2E,+BAA+BtF,KAAKuF,QAAQ,CAC1CF,aACApF,IAAIuF,aAAa,CAACF;IAEtB;IAEA,OAAO;QACLH,oBAAoB;YAClB1D,WAAW6C,OAAOtD,KAAK,CAACS,SAAS;YACjCH,QAAQgD,OAAOtD,KAAK,CAACM,MAAM;YAC3BL,MAAMqE;YACN9D,SAAS8C,OAAOtD,KAAK,CAACQ,OAAO;YAC7BJ,YAAYkD,OAAOtD,KAAK,CAACI,UAAU;YACnCG,YAAY+C,OAAOtD,KAAK,CAACO,UAAU;QACrC;QACAkE,mBAAmB9F,qBAAqB2E,OAAOtD,KAAK,EAAEsD,OAAO5C,MAAM;IACrE;AACF;AAEA,OAAO,SAASgE,qBAAqB3E,OAAgB,EAAEsE,WAAmB;IACxE,OAAO,eACLM,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAEzC,YAAY,EAAE,GAAG,IAAI0C,IAAIJ,IAAI1F,GAAG,EAAG;QAErD,IAAI6F,aAAa,mCAAmC;YAClD,IAAIH,IAAIK,MAAM,KAAK,QAAQ;gBACzB,OAAOpG,mBAAmBqG,UAAU,CAACL;YACvC;YAEA,MAAM9C,OAAO,MAAM,IAAIoD,QAAgB,CAACC,SAASC;gBAC/C,IAAIC,OAAO;gBACXV,IAAIW,EAAE,CAAC,QAAQ,CAACC;oBACdF,QAAQE;gBACV;gBACAZ,IAAIW,EAAE,CAAC,OAAO,IAAMH,QAAQE;gBAC5BV,IAAIW,EAAE,CAAC,SAASF;YAClB;YAEA,MAAMI,UAAUC,KAAKC,KAAK,CAAC5D;YAC3B,MAAM6D,cAAc9D,kBAAkB2D;YACtC,MAAMI,SAAsC,MAAMV,QAAQW,GAAG,CAC3DF,YAAY1D,GAAG,CAAC,OAAOjC;gBACrB,IAAI;oBACF,MAAM8F,aAAa,MAAM1B,yBACvBrE,SACAsE,aACArE;oBAEF,IAAI8F,eAAe,MAAM;wBACvB,OAAO;4BACLC,QAAQ;4BACRC,QAAQ;wBACV;oBACF;oBACA,OAAO;wBAAED,QAAQ;wBAAaE,OAAOH;oBAAW;gBAClD,EAAE,OAAOhC,OAAO;oBACd,OAAO;wBACLiC,QAAQ;wBACRC,QAAQzG,QAAQuE,OAAO;4BAAEoC,QAAQ;wBAAM;oBACzC;gBACF;YACF;YAGF,OAAOtH,mBAAmBuH,IAAI,CAACvB,KAAKgB;QACtC,OAAO,IAAId,aAAa,2BAA2B;YACjD,MAAM9E,QAAQoC,iBAAiBC;YAE/B,IAAI,CAACrC,OAAO,OAAOpB,mBAAmBqG,UAAU,CAACL;YAEjD,MAAMwB,aAAa,MAAMvH,GAAGwH,MAAM,CAACrG,MAAMC,IAAI,EAAElB,GAAGuH,IAAI,EAAEC,IAAI,CAC1D,IAAM,MACN,IAAM;YAER,IAAI,CAACH,YAAY,OAAOxH,mBAAmB4H,QAAQ,CAAC5B;YAEpD,IAAI;oBACuB5E,aAAiBA;gBAA1Cd,aAAac,MAAMC,IAAI,EAAED,CAAAA,cAAAA,MAAMK,IAAI,YAAVL,cAAc,GAAGA,CAAAA,gBAAAA,MAAMM,MAAM,YAAZN,gBAAgB;YAC5D,EAAE,OAAOyG,KAAK;gBACZ5C,QAAQ6C,GAAG,CAAC,4BAA4BD;gBACxC,OAAO7H,mBAAmB+H,mBAAmB,CAAC/B;YAChD;YAEA,OAAOhG,mBAAmBgI,SAAS,CAAChC;QACtC;QAEA,OAAOC;IACT;AACF;AAEA,OAAO,SAASgC,uBAAuB9G,OAAgB;IACrD,OAAO,eACL4E,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAEzC,YAAY,EAAE,GAAG,IAAI0C,IAAIJ,IAAI1F,GAAG,EAAG;QAErD,IAAI6F,aAAa,wBAAwB;YACvC,OAAOD;QACT;QAEA,IAAIiC,WAAWzE,aAAajB,GAAG,CAAC;QAEhC,IAAI,CAAC0F,UAAU;YACb,OAAOlI,mBAAmBqG,UAAU,CAACL;QACvC;QAEA,mDAAmD;QACnD,iEAAiE;QACjE,IACEkC,SAASnH,UAAU,CAAC,iBACpBmH,SAASnH,UAAU,CAAC,yBACpB;YACA,MAAMoH,YAAY1H,cAAcyH;YAEhC,IAAIC,WAAW;gBACb,OAAOnI,mBAAmBuH,IAAI,CAACvB,KAAKmC,UAAUvE,OAAO;YACvD;YAEA,OAAO5D,mBAAmBgI,SAAS,CAAChC;QACtC;QAEA,IAAI;YACF,kDAAkD;YAClDkC,WAAWE,UAAUF;YAErB,IAAI9H,KAAKiI,UAAU,CAACH,WAAW;gBAC7BA,WAAW7H,IAAIK,aAAa,CAACwH,UAAUhG,IAAI;YAC7C;YAEA,MAAMoG,kBAAkB,MAAMnH,QAAQoH,YAAY,CAACL;YAEnD,IAAII,iBAAiB;gBACnB,OAAOtI,mBAAmBwI,UAAU,CAACxC,KAAKsC;YAC5C;YAEA,IAAIJ,SAASnH,UAAU,CAAC,UAAU;gBAChC,MAAMoH,YAAY,MAAM3H,qBAAqB0H;gBAE7C,IAAIC,WAAW;oBACb,OAAOnI,mBAAmBuH,IAAI,CAACvB,KAAKmC;gBACtC;YACF;QACF,EAAE,OAAOjD,OAAO;YACdD,QAAQC,KAAK,CAAC,6BAA6BA;QAC7C;QAEAlF,mBAAmBgI,SAAS,CAAChC;IAC/B;AACF"}