import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/toaster";
import { ThemeProvider } from "next-themes";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Elashrafy AI - مساعد الذكاء الاصطناعي",
  description: "واجهة محادثة متطورة مدعومة بنموذج GLM 4.5. تجربة ذكاء اصطناعي احترافية باللغة العربية.",
  keywords: ["Elashrafy AI", "ذكاء اصطناعي", "GLM 4.5", "مساعد ذكي", "محادثة", "عربي"],
  authors: [{ name: "Elashrafy AI Team" }],
  openGraph: {
    title: "Elashrafy AI - مساعد الذكاء الاصطناعي",
    description: "واجهة محادثة متطورة مدعومة بنموذج GLM 4.5",
    url: "https://elashrafy-ai.com",
    siteName: "Elashrafy AI",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Elashrafy AI - مساعد الذكاء الاصطناعي",
    description: "واجهة محادثة متطورة مدعومة بنموذج GLM 4.5",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-background text-foreground`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {children}
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}
