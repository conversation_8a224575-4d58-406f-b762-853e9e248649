{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/utils/merge-refs.ts"], "sourcesContent": ["import type * as React from 'react'\n\n/**\n * A function that merges React refs into one.\n * Supports both functions and ref objects created using createRef() and useRef().\n *\n * Usage:\n * ```tsx\n * <div ref={mergeRefs(ref1, ref2, ref3)} />\n * ```\n *\n * @param {(React.Ref<T> | undefined)[]} inputRefs Array of refs\n * @returns {React.Ref<T> | React.RefCallback<T>} Merged refs\n */\nexport default function mergeRefs<T>(\n  ...inputRefs: (React.Ref<T> | undefined)[]\n): React.Ref<T> | React.RefCallback<T> {\n  const filteredInputRefs = inputRefs.filter(Boolean)\n\n  if (filteredInputRefs.length <= 1) {\n    const firstRef = filteredInputRefs[0]\n\n    return firstRef || null\n  }\n\n  return function mergedRefs(ref) {\n    for (const inputRef of filteredInputRefs) {\n      if (typeof inputRef === 'function') {\n        inputRef(ref)\n      } else if (inputRef) {\n        ;(inputRef as React.MutableRefObject<T | null>).current = ref\n      }\n    }\n  }\n}\n"], "names": ["mergeRefs", "inputRefs", "filteredInputRefs", "filter", "Boolean", "length", "firstRef", "mergedRefs", "ref", "inputRef", "current"], "mappings": "AAEA;;;;;;;;;;;CAWC,GACD,eAAe,SAASA;IACtB,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,YAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,UAAH,QAAA,SAAA,CAAA,KAA0C;;IAE1C,MAAMC,oBAAoBD,UAAUE,MAAM,CAACC;IAE3C,IAAIF,kBAAkBG,MAAM,IAAI,GAAG;QACjC,MAAMC,WAAWJ,iBAAiB,CAAC,EAAE;QAErC,OAAOI,YAAY;IACrB;IAEA,OAAO,SAASC,WAAWC,GAAG;QAC5B,KAAK,MAAMC,YAAYP,kBAAmB;YACxC,IAAI,OAAOO,aAAa,YAAY;gBAClCA,SAASD;YACX,OAAO,IAAIC,UAAU;;gBACjBA,SAA8CC,OAAO,GAAGF;YAC5D;QACF;IACF;AACF"}