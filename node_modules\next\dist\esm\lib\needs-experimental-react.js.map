{"version": 3, "sources": ["../../src/lib/needs-experimental-react.ts"], "sourcesContent": ["import type { NextConfig } from '../server/config-shared'\n\nexport function needsExperimentalReact(config: NextConfig) {\n  const { ppr, taint, viewTransition, routerBFCache } =\n    config.experimental || {}\n  return Boolean(ppr || taint || viewTransition || routerBFCache)\n}\n"], "names": ["needsExperimentalReact", "config", "ppr", "taint", "viewTransition", "routerBFCache", "experimental", "Boolean"], "mappings": "AAEA,OAAO,SAASA,uBAAuBC,MAAkB;IACvD,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAEC,cAAc,EAAEC,aAAa,EAAE,GACjDJ,OAAOK,YAAY,IAAI,CAAC;IAC1B,OAAOC,QAAQL,OAAOC,SAASC,kBAAkBC;AACnD"}