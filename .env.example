# 🔐 متغيرات البيئة - Z.ai Code Scaffold
# انسخ هذا الملف إلى .env.local وأضف القيم الحقيقية

# 🗄️ قاعدة البيانات
DATABASE_URL="file:./db/custom.db"

# 🔐 NextAuth.js
NEXTAUTH_SECRET="your-secret-key-here-change-this-in-production"
NEXTAUTH_URL="http://localhost:3000"

# 🌐 Google OAuth (اختياري)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# 🐙 GitHub OAuth (اختياري)
GITHUB_ID="your-github-client-id"
GITHUB_SECRET="your-github-client-secret"

# 🎮 Discord OAuth (اختياري)
DISCORD_CLIENT_ID="your-discord-client-id"
DISCORD_CLIENT_SECRET="your-discord-client-secret"

# 🌐 Socket.IO (اختياري)
SOCKET_IO_PORT="3001"
SOCKET_IO_CORS_ORIGIN="http://localhost:3000"

# 🤖 Z.ai API (إذا كان متاحاً)
Z_AI_API_KEY="your-z-ai-api-key"
Z_AI_API_URL="https://api.z.ai"

# 🖼️ خدمات الصور (اختياري)
CLOUDINARY_CLOUD_NAME="your-cloudinary-cloud-name"
CLOUDINARY_API_KEY="your-cloudinary-api-key"
CLOUDINARY_API_SECRET="your-cloudinary-api-secret"

# 📧 خدمة البريد الإلكتروني (اختياري)
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT="587"
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
EMAIL_FROM="<EMAIL>"

# 🔍 Analytics (اختياري)
GOOGLE_ANALYTICS_ID="G-XXXXXXXXXX"

# 🚀 إعدادات الإنتاج
NODE_ENV="development"
PORT="3000"
