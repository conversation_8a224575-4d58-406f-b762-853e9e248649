<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الاتصال - Z.ai Project</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .loading { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 اختبار الاتصال بمشروع Z.ai</h1>
        
        <div id="status" class="status loading">
            🔄 جاري اختبار الاتصال...
        </div>
        
        <div>
            <button onclick="testConnection()">🔄 إعادة اختبار الاتصال</button>
            <button onclick="openProject()">🌐 فتح المشروع</button>
        </div>
        
        <div id="details" style="margin-top: 20px;"></div>
    </div>

    <script>
        async function testConnection() {
            const statusDiv = document.getElementById('status');
            const detailsDiv = document.getElementById('details');
            
            statusDiv.className = 'status loading';
            statusDiv.innerHTML = '🔄 جاري اختبار الاتصال...';
            detailsDiv.innerHTML = '';
            
            try {
                // Test API status
                const response = await fetch('http://localhost:3000/api/status');
                const data = await response.json();
                
                if (response.ok) {
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = '✅ الاتصال ناجح! المشروع يعمل بشكل صحيح';
                    detailsDiv.innerHTML = `
                        <h3>تفاصيل الاتصال:</h3>
                        <p><strong>الحالة:</strong> ${data.status}</p>
                        <p><strong>النموذج:</strong> ${data.model}</p>
                        <p><strong>الرسالة:</strong> ${data.message}</p>
                        <p><strong>المزود:</strong> ${data.details?.provider}</p>
                        <p><strong>الوقت:</strong> ${new Date().toLocaleString('ar-SA')}</p>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '❌ فشل الاتصال';
                detailsDiv.innerHTML = `
                    <h3>تفاصيل الخطأ:</h3>
                    <p><strong>الخطأ:</strong> ${error.message}</p>
                    <p><strong>الحلول المقترحة:</strong></p>
                    <ul>
                        <li>تأكد من تشغيل الخادم على المنفذ 3000</li>
                        <li>جرب الرابط: <a href="http://localhost:3000" target="_blank">http://localhost:3000</a></li>
                        <li>جرب الرابط البديل: <a href="http://127.0.0.1:3000" target="_blank">http://127.0.0.1:3000</a></li>
                        <li>تحقق من إعدادات Firewall</li>
                    </ul>
                `;
            }
        }
        
        function openProject() {
            window.open('http://localhost:3000', '_blank');
        }
        
        // Test connection on page load
        testConnection();
    </script>
</body>
</html>
