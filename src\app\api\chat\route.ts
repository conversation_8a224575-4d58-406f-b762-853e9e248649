import { NextRequest, NextResponse } from 'next/server';

// محاكي AI محلي للتجربة
function simulateAIResponse(message: string): string {
  const responses = [
    `شكراً لك على رسالتك: "${message}". هذا رد تجريبي من المحاكي المحلي.`,
    `أفهم أنك تسأل عن: "${message}". حالياً أعمل في وضع التجربة بدون اتصال خارجي.`,
    `رسالتك "${message}" وصلت بنجاح. هذا مشروع Z.ai Code Scaffold يعمل في الوضع التجريبي.`,
    `مرحباً! تلقيت رسالتك حول "${message}". المشروع يعمل بشكل ممتاز، لكن يحتاج مفتاح API للاتصال الحقيقي.`,
    `"${message}" - رسالة مستلمة! هذا رد من المحاكي المحلي. للحصول على ردود ذكية حقيقية، تحتاج مفتاح Z.ai API.`
  ];

  return responses[Math.floor(Math.random() * responses.length)];
}

export async function POST(request: NextRequest) {
  try {
    const { message, sessionHistory, enableWebSearch = false } = await request.json();

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required and must be a string' },
        { status: 400 }
      );
    }

    console.log('Processing message:', message);

    // محاكاة تأخير الشبكة
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    // محاكاة البحث عبر الإنترنت
    let searchResults: any[] = [];
    let usedWebSearch = false;

    if (enableWebSearch) {
      console.log('محاكاة البحث عبر الإنترنت...');
      usedWebSearch = true;
      // محاكاة نتائج البحث
      searchResults = [
        {
          name: "مثال على نتيجة بحث 1",
          url: "https://example1.com",
          snippet: "هذا مثال على نتيجة بحث تجريبية"
        },
        {
          name: "مثال على نتيجة بحث 2",
          url: "https://example2.com",
          snippet: "نتيجة بحث تجريبية أخرى"
        }
      ];
    }

    // توليد رد تجريبي
    const aiResponse = simulateAIResponse(message);
    console.log('رد تجريبي تم توليده:', aiResponse.substring(0, 100) + '...');

    return NextResponse.json({
      response: aiResponse,
      success: true,
      searchResults: searchResults.length > 0 ? searchResults : undefined,
      usedWebSearch,
      cached: false,
      responseTime: Date.now()
    });

  } catch (error) {
    console.error('Chat API Error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}