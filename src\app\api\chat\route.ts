import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { message, sessionHistory, enableWebSearch = false } = await request.json();

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required and must be a string' },
        { status: 400 }
      );
    }

    console.log('Processing message:', message);

    // Create ZAI instance
    const zai = await ZAI.create();

    let searchResults = [];
    let enhancedSystemPrompt = 'أنت مساعد ذكاء اصطناعي متطور يدعى Elashrafy AI، مدعوم بنموذج GLM 4.5. أنت تساعد المستخدمين بالإجابة على أسئلتهم وتقديم المساعدة بطريقة محترفة ودقيقة. تتحدث العربية بطلاقة وتقدم إجابات مفصلة ومفيدة.';

    // Web search if enabled
    if (enableWebSearch) {
      try {
        console.log('جاري البحث عبر الإنترنت...');
        const searchResponse = await zai.functions.invoke("web_search", {
          query: message,
          num: 5
        });
        
        searchResults = searchResponse || [];
        console.log(`تم العثور على ${searchResults.length} نتيجة بحث`);
        
        if (searchResults.length > 0) {
          enhancedSystemPrompt += '\n\nنتائج البحث عبر الإنترنت ذات الصلة:\n';
          searchResults.forEach((result: any, index: number) => {
            enhancedSystemPrompt += `${index + 1}. ${result.name} (${result.url}): ${result.snippet}\n`;
          });
          enhancedSystemPrompt += '\nيرجى استخدام هذه المعلومات لتقديم إجابة دقيقة ومحدثة مع الإشارة إلى المصادر عند الاقتضاء.';
        }
      } catch (searchError) {
        console.error('خطأ في البحث عبر الإنترنت:', searchError);
        enhancedSystemPrompt += '\n\nملاحظة: فشل البحث عبر الإنترنت، يرجى الاعتماد على معرفتك العامة.';
      }
    }

    // Prepare messages for the AI model
    const messages = [
      {
        role: 'system',
        content: enhancedSystemPrompt
      },
      ...(sessionHistory || []).map((msg: any) => ({
        role: msg.role === 'user' ? 'user' : 'assistant',
        content: msg.content
      })),
      {
        role: 'user',
        content: message
      }
    ];

    console.log('Sending request to AI model...');

    // Get completion from ZAI
    const completion = await zai.chat.completions.create({
      messages,
      temperature: 0.7,
      max_tokens: 1500,
      stream: false,
    });

    const response = completion.choices[0]?.message?.content || 'عذراً، حدث خطأ في معالجة طلبك.';
    console.log('Received response from AI');

    return NextResponse.json({
      response,
      success: true,
      searchResults: searchResults.length > 0 ? searchResults : undefined,
      usedWebSearch: enableWebSearch && searchResults.length > 0
    });

  } catch (error) {
    console.error('Chat API Error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}