import { NextRequest, NextResponse } from 'next/server';
import ZA<PERSON> from 'z-ai-web-dev-sdk';

export async function GET() {
  try {
    console.log('جاري التحقق من حالة الاتصال بنموذج GLM...');
    
    // Create ZAI instance
    const zai = await ZAI.create();
    
    // Test basic functionality
    const testCompletion = await zai.chat.completions.create({
      messages: [
        {
          role: 'system',
          content: 'أنت مساعد ذكاء اصطناعي للاختبار.'
        },
        {
          role: 'user',
          content: 'رد بكلمة "متصل" فقط للتأكد من أنك تعمل.'
        }
      ],
      temperature: 0.1,
      max_tokens: 10,
    });

    const response = testCompletion.choices[0]?.message?.content;
    
    if (response && response.includes('متصل')) {
      return NextResponse.json({
        status: 'connected',
        model: 'GLM 4.5',
        responseTime: Date.now(),
        message: 'متصل بنموذج GLM 4.5 بنجاح',
        details: {
          provider: 'Z AI Web Dev SDK',
          capabilities: ['chat', 'image_generation', 'web_search', 'code_execution'],
          latency: 'fast'
        }
      });
    } else {
      throw new Error('الرد من النموذج غير متوقع');
    }

  } catch (error) {
    console.error('Status Check Error:', error);
    return NextResponse.json(
      { 
        status: 'error',
        message: 'فشل الاتصال بنموذج GLM 4.5',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      },
      { status: 500 }
    );
  }
}