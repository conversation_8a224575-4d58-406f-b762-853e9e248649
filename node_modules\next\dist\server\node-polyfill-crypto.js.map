{"version": 3, "sources": ["../../src/server/node-polyfill-crypto.ts"], "sourcesContent": ["// Polyfill crypto() in the Node.js environment\n\nif (!global.crypto) {\n  let webcrypto: Crypto | undefined\n\n  Object.defineProperty(global, 'crypto', {\n    enumerable: false,\n    configurable: true,\n    get() {\n      if (!webcrypto) {\n        webcrypto = require('node:crypto').webcrypto\n      }\n      return webcrypto\n    },\n    set(value: Crypto) {\n      webcrypto = value\n    },\n  })\n}\n"], "names": ["global", "crypto", "webcrypto", "Object", "defineProperty", "enumerable", "configurable", "get", "require", "set", "value"], "mappings": "AAAA,+CAA+C;;AAE/C,IAAI,CAACA,OAAOC,MAAM,EAAE;IAClB,IAAIC;IAEJC,OAAOC,cAAc,CAACJ,QAAQ,UAAU;QACtCK,YAAY;QACZC,cAAc;QACdC;YACE,IAAI,CAACL,WAAW;gBACdA,YAAYM,QAAQ,eAAeN,SAAS;YAC9C;YACA,OAAOA;QACT;QACAO,KAAIC,KAAa;YACfR,YAAYQ;QACd;IACF;AACF"}