import { default as Stats } from '../core/node_fs_stats';
import { UNPKGMeta } from '../backend/UNPKGRequest';
import { JSDelivrMeta } from '../backend/JSDelivrRequest';
/**
 * A simple class for storing a filesystem index. Assumes that all paths passed
 * to it are *absolute* paths.
 *
 * Can be used as a partial or a full index, although care must be taken if used
 * for the former purpose, especially when directories are concerned.
 */
export declare class FileIndex<T> {
    /**
     * Static method for constructing indices from a JSON listing.
     * @param listing Directory listing generated by tools/XHRIndexer.coffee
     * @return A new FileIndex object.
     */
    static fromListing<T>(listing: any): FileIndex<T>;
    static fromUnpkg<T>(listing: UNPKGMeta): FileIndex<T>;
    static fromJSDelivr<T>(listing: JSDelivrMeta): FileIndex<T>;
    private _index;
    /**
     * Constructs a new FileIndex.
     */
    constructor();
    /**
     * Runs the given function over all files in the index.
     */
    fileIterator<T>(cb: (file: T | null, path?: string) => void): void;
    /**
     * Adds the given absolute path to the index if it is not already in the index.
     * Creates any needed parent directories.
     * @param path The path to add to the index.
     * @param inode The inode for the
     *   path to add.
     * @return 'True' if it was added or already exists, 'false' if there
     *   was an issue adding it (e.g. item in path is a file, item exists but is
     *   different).
     * @todo If adding fails and implicitly creates directories, we do not clean up
     *   the new empty directories.
     */
    addPath(path: string, inode: Inode): boolean;
    /**
     * Adds the given absolute path to the index if it is not already in the index.
     * The path is added without special treatment (no joining of adjacent separators, etc).
     * Creates any needed parent directories.
     * @param path The path to add to the index.
     * @param inode The inode for the
     *   path to add.
     * @return 'True' if it was added or already exists, 'false' if there
     *   was an issue adding it (e.g. item in path is a file, item exists but is
     *   different).
     * @todo If adding fails and implicitly creates directories, we do not clean up
     *   the new empty directories.
     */
    addPathFast(path: string, inode: Inode): boolean;
    /**
     * Removes the given path. Can be a file or a directory.
     * @return The removed item,
     *   or null if it did not exist.
     */
    removePath(path: string): Inode | null;
    /**
     * Retrieves the directory listing of the given path.
     * @return An array of files in the given path, or 'null' if it does not exist.
     */
    ls(path: string): string[] | null;
    /**
     * Returns the inode of the given item.
     * @return Returns null if the item does not exist.
     */
    getInode(path: string): Inode | null;
    /**
     * Split into a (directory path, item name) pair
     */
    private _split_path;
}
/**
 * Generic interface for file/directory inodes.
 * Note that Stats objects are what we use for file inodes.
 */
export interface Inode {
    isFile(): boolean;
    isDir(): boolean;
}
/**
 * Inode for a file. Stores an arbitrary (filesystem-specific) data payload.
 */
export declare class FileInode<T> implements Inode {
    private data;
    constructor(data: T);
    isFile(): boolean;
    isDir(): boolean;
    getData(): T;
    setData(data: T): void;
}
/**
 * Inode for a directory. Currently only contains the directory listing.
 */
export declare class DirInode<T> implements Inode {
    private data;
    private _ls;
    /**
     * Constructs an inode for a directory.
     */
    constructor(data?: T | null);
    isFile(): boolean;
    isDir(): boolean;
    getData(): T | null;
    /**
     * Return a Stats object for this inode.
     * @todo Should probably remove this at some point. This isn't the
     *       responsibility of the FileIndex.
     */
    getStats(): Stats;
    /**
     * Returns the directory listing for this directory. Paths in the directory are
     * relative to the directory's path.
     * @return The directory listing for this directory.
     */
    getListing(): string[];
    /**
     * Returns the inode for the indicated item, or null if it does not exist.
     * @param p Name of item in this directory.
     */
    getItem(p: string): Inode | null;
    /**
     * Add the given item to the directory listing. Note that the given inode is
     * not copied, and will be mutated by the DirInode if it is a DirInode.
     * @param p Item name to add to the directory listing.
     * @param inode The inode for the
     *   item to add to the directory inode.
     * @return True if it was added, false if it already existed.
     */
    addItem(p: string, inode: Inode): boolean;
    /**
     * Removes the given item from the directory listing.
     * @param p Name of item to remove from the directory listing.
     * @return Returns the item
     *   removed, or null if the item did not exist.
     */
    remItem(p: string): Inode | null;
}
/**
 * @hidden
 */
export declare function isFileInode<T>(inode: Inode | null): inode is FileInode<T>;
/**
 * @hidden
 */
export declare function isDirInode<T>(inode: Inode | null): inode is DirInode<T>;
