{"version": 3, "sources": ["../../../../src/lib/metadata/types/metadata-interface.ts"], "sourcesContent": ["/**\n * Next.js Metadata API\n *\n * This file defines the types used by Next.js to configure metadata\n * through static exports or dynamic `generateMetadata` functions in Server Components.\n *\n * @remarks\n * - The static `metadata` object and `generateMetadata` function are only supported in Server Components.\n * - Do not export both a `metadata` object and a `generateMetadata` function from the same route segment.\n * - You can still render metadata in client components directly as part of the component's JSX.\n *\n * @see https://nextjs.org/docs/app/api-reference/metadata\n */\n\nimport type {\n  AlternateURLs,\n  Languages,\n  ResolvedAlternateURLs,\n} from './alternative-urls-types'\nimport type {\n  AppleWebApp,\n  AppLinks,\n  Facebook,\n  FormatDetection,\n  ItunesApp,\n  Pinterest,\n  ResolvedAppleWebApp,\n  ResolvedAppLinks,\n  ResolvedFacebook,\n  ResolvedPinterest,\n  ViewportLayout,\n} from './extra-types'\nimport type {\n  DeprecatedMetadataFields,\n  AbsoluteTemplateString,\n  Author,\n  ColorSchemeEnum,\n  Icon,\n  Icons,\n  IconURL,\n  ReferrerEnum,\n  ResolvedIcons,\n  ResolvedVerification,\n  Robots,\n  ResolvedRobots,\n  TemplateString,\n  Verification,\n  ThemeColorDescriptor,\n  Videos,\n} from './metadata-types'\nimport type { Manifest as ManifestFile } from './manifest-types'\nimport type { OpenGraph, ResolvedOpenGraph } from './opengraph-types'\nimport type { ResolvedTwitterMetadata, Twitter } from './twitter-types'\n\n/**\n * Metadata interface to describe all the metadata fields that can be set in a document.\n *\n * @remarks\n * This interface covers all the metadata fields available in Next.js including title, description,\n * icons, openGraph, twitter, and more. Fields such as `metadataBase` help in composing absolute URLs\n * from relative ones. The `title` field supports both simple strings and a template object with `default`,\n * `template`, and `absolute` properties.\n *\n * @example\n * ```tsx\n * // Static metadata export in a layout or page:\n * import type { Metadata } from 'next'\n *\n * export const metadata: Metadata = {\n *   metadataBase: new URL('https://example.com'),\n *   title: { default: 'My Site', template: '%s | My Site' },\n *   description: 'Welcome to My Site',\n *   alternates: {\n *     canonical: 'https://example.com',\n *     languages: {\n *       'en-US': 'https://example.com/en-US',\n *       'de-DE': 'https://example.com/de-DE'\n *     }\n *   },\n *   openGraph: {\n *     title: 'My Site',\n *     description: 'Welcome to My Site',\n *     url: 'https://example.com',\n *     siteName: 'My Site',\n *     images: [{ url: 'https://example.com/og.png' }]\n *   },\n * }\n * ```\n */\ninterface Metadata extends DeprecatedMetadataFields {\n  /**\n   * The base path and origin for absolute URLs in various metadata fields.\n   *\n   * @remarks\n   * When relative URLs (for Open Graph images, alternates, etc.) are used, they are composed with this base.\n   * If not provided, Next.js will populate a default value based on environment variables.\n   */\n  metadataBase?: null | URL | undefined\n\n  /**\n   * The document title.\n   *\n   * @remarks\n   * The title can be a simple string (e.g., `\"My Blog\"`) or an object with:\n   * - `default`: A fallback title for child segments.\n   * - `template`: A title template (e.g., `\"%s | My Website\"`) applied to child titles.\n   * - `absolute`: A title that overrides parent templates.\n   *\n   * @example\n   * ```tsx\n   * // As a simple string:\n   * title: \"My Blog\"\n   *\n   * // As a template object:\n   * title: { default: \"Dashboard\", template: \"%s | My Website\" }\n   *\n   * // Using absolute value (ignores parent template):\n   * title: { absolute: \"My Blog\", template: \"%s | My Website\" }\n   * ```\n   */\n  title?: null | string | TemplateString | undefined\n\n  /**\n   * The document description, and optionally the Open Graph and Twitter descriptions.\n   *\n   * @example\n   * ```tsx\n   * description: \"My Blog Description\"\n   * // Renders: <meta name=\"description\" content=\"My Blog Description\" />\n   * ```\n   */\n  description?: null | string | undefined\n\n  // Standard metadata names\n  // https://developer.mozilla.org/docs/Web/HTML/Element/meta/name\n\n  /**\n   * The application name.\n   *\n   * @example\n   * ```tsx\n   * applicationName: \"My Blog\"\n   * // Renders: <meta name=\"application-name\" content=\"My Blog\" />\n   * ```\n   */\n  applicationName?: null | string | undefined\n\n  /**\n   * The authors of the document.\n   *\n   * @example\n   * ```tsx\n   * authors: [{ name: \"Next.js Team\", url: \"https://nextjs.org\" }]\n   * // Renders:\n   * // <meta name=\"author\" content=\"Next.js Team\" />\n   * // <link rel=\"author\" href=\"https://nextjs.org\" />\n   * ```\n   */\n  authors?: null | Author | Array<Author> | undefined\n\n  /**\n   * The generator used for the document.\n   *\n   * @example\n   * ```tsx\n   * generator: \"Next.js\"\n   * // Renders: <meta name=\"generator\" content=\"Next.js\" />\n   * ```\n   */\n  generator?: null | string | undefined\n\n  /**\n   * The keywords for the document.\n   *\n   * @remarks\n   * When an array is provided, keywords are flattened into a comma-separated string.\n   *\n   * @example\n   * ```tsx\n   * keywords: \"nextjs, react, blog\"\n   * // or\n   * keywords: [\"react\", \"server components\"]\n   * ```\n   */\n  keywords?: null | string | Array<string> | undefined\n\n  /**\n   * The referrer setting for the document.\n   *\n   * @example\n   * ```tsx\n   * referrer: \"origin\"\n   * // Renders: <meta name=\"referrer\" content=\"origin\" />\n   * ```\n   */\n  referrer?: null | ReferrerEnum | undefined\n\n  /**\n   * The theme color for the document.\n   *\n   * @deprecated Use the new viewport configuration (`export const viewport: Viewport = { ... }`) instead.\n   */\n  themeColor?:\n    | null\n    | string\n    | ThemeColorDescriptor\n    | ThemeColorDescriptor[]\n    | undefined\n\n  /**\n   * The color scheme for the document.\n   *\n   * @deprecated Use the new viewport configuration (`export const viewport: Viewport = { ... }`) instead.\n   */\n  colorScheme?: null | ColorSchemeEnum | undefined\n\n  /**\n   * The viewport setting for the document.\n   *\n   * @deprecated Use the new viewport configuration (`export const viewport: Viewport = { ... }`) instead.\n   */\n  viewport?: null | string | ViewportLayout | undefined\n\n  /**\n   * The creator of the document.\n   *\n   * @example\n   * ```tsx\n   * creator: \"Next.js Team\"\n   * // Renders: <meta name=\"creator\" content=\"Next.js Team\" />\n   * ```\n   */\n  creator?: null | string | undefined\n\n  /**\n   * The publisher of the document.\n   *\n   * @example\n   * ```tsx\n   * publisher: \"Vercel\"\n   * // Renders: <meta name=\"publisher\" content=\"Vercel\" />\n   * ```\n   */\n  publisher?: null | string | undefined\n\n  // https://developer.mozilla.org/docs/Web/HTML/Element/meta/name#other_metadata_names\n\n  /**\n   * The robots setting for the document.\n   *\n   * @remarks\n   * Can be a string (e.g., \"index, follow\") or an object with more granular rules.\n   *\n   * @example\n   * ```tsx\n   * robots: \"index, follow\"\n   * // or\n   * robots: { index: true, follow: true }\n   * ```\n   *\n   * @see https://developer.mozilla.org/docs/Glossary/Robots.txt\n   */\n  robots?: null | string | Robots | undefined\n\n  /**\n   * The canonical and alternate URLs for the document.\n   *\n   * @remarks\n   * This field allows defining a canonical URL as well as alternate URLs (such as for multiple languages).\n   *\n   * @example\n   * ```tsx\n   * alternates: {\n   *   canonical: \"https://example.com\",\n   *   languages: {\n   *     \"en-US\": \"https://example.com/en-US\"\n   *   }\n   * }\n   * ```\n   */\n  alternates?: null | AlternateURLs | undefined\n\n  /**\n   * The icons for the document. Defaults to rel=\"icon\".\n   *\n   * @remarks\n   * You can specify a simple URL or an object to differentiate between icon types (e.g., apple-touch-icon).\n   *\n   * @example\n   * ```tsx\n   * icons: \"https://example.com/icon.png\"\n   * // or\n   * icons: {\n   *   icon: \"https://example.com/icon.png\",\n   *   apple: \"https://example.com/apple-icon.png\"\n   * }\n   * ```\n   *\n   * @see https://developer.mozilla.org/docs/Web/HTML/Attributes/rel#attr-icon\n   */\n  icons?: null | IconURL | Array<Icon> | Icons | undefined\n\n  /**\n   * A web application manifest, as defined in the Web Application Manifest specification.\n   *\n   * @example\n   * ```tsx\n   * manifest: \"https://example.com/manifest.json\"\n   * // Renders: <link rel=\"manifest\" href=\"https://example.com/manifest.json\" />\n   * ```\n   *\n   * @see https://developer.mozilla.org/docs/Web/Manifest\n   */\n  manifest?: null | string | URL | undefined\n\n  /**\n   * The Open Graph metadata for the document.\n   *\n   * @remarks\n   * Follows the Open Graph protocol to enrich link previews.\n   *\n   * @example\n   * ```tsx\n   * openGraph: {\n   *   type: \"website\",\n   *   url: \"https://example.com\",\n   *   title: \"My Website\",\n   *   description: \"My Website Description\",\n   *   siteName: \"My Website\",\n   *   images: [{ url: \"https://example.com/og.png\" }]\n   * }\n   * ```\n   *\n   * @see https://ogp.me/\n   */\n  openGraph?: null | OpenGraph | undefined\n\n  /**\n   * The Twitter metadata for the document.\n   *\n   * @remarks\n   * - Used for configuring Twitter Cards and can include details such as `card`, `site`, and `creator`.\n   * - Notably, more sites than just Twitter (now X) use this format.\n   *\n   * @example\n   * ```tsx\n   * twitter: {\n   *   card: \"summary_large_image\",\n   *   site: \"@site\",\n   *   creator: \"@creator\",\n   *   images: \"https://example.com/og.png\"\n   * }\n   * ```\n   */\n  twitter?: null | Twitter | undefined\n\n  /**\n   * The Facebook metadata for the document.\n   *\n   * @remarks\n   * Specify either `appId` or `admins` (but not both) to configure Facebook integration.\n   *\n   * @example\n   * ```tsx\n   * facebook: { appId: \"12345678\" }\n   * // Renders <meta property=\"fb:app_id\" content=\"12345678\" />\n   * // or\n   * facebook: { admins: [\"12345678\"] }\n   * // Renders <meta property=\"fb:admins\" content=\"12345678\" />\n   * ```\n   */\n  facebook?: null | Facebook | undefined\n\n  /**\n   * The Pinterest metadata for the document to choose whether opt out of rich pin data.\n   *\n   * @example\n   * ```tsx\n   * pinterest: { richPin: true }\n   * // Renders <meta name=\"pinterest-rich-pin\" content=\"true\" />\n   * ```\n   */\n  pinterest?: null | Pinterest\n\n  /**\n   * The common verification tokens for the document.\n   *\n   * @example\n   * ```tsx\n   * verification: { google: \"1234567890\", yandex: \"1234567890\", \"me\": \"1234567890\" }\n   * // Renders <meta name=\"google-site-verification\" content=\"1234567890\" />\n   * // <meta name=\"yandex-verification\" content=\"1234567890\" />\n   * // <meta name=\"me\" content=\"@me\" />\n   * ```\n   */\n  verification?: Verification | undefined\n\n  /**\n   * The Apple web app metadata for the document.\n   *\n   * @example\n   * ```tsx\n   * appleWebApp: { capable: true, title: \"My Website\", statusBarStyle: \"black-translucent\" }\n   * ```\n   *\n   * @see https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariHTMLRef/Articles/MetaTags.html\n   */\n  appleWebApp?: null | boolean | AppleWebApp | undefined\n\n  /**\n   * Indicates whether devices should interpret certain formats (such as telephone numbers) as actionable links.\n   *\n   * @example\n   * ```tsx\n   * formatDetection: { telephone: false }\n   * // Renders: <meta name=\"format-detection\" content=\"telephone=no\" />\n   * ```\n   */\n  formatDetection?: null | FormatDetection | undefined\n\n  /**\n   * The metadata for the iTunes App.\n   *\n   * @remarks\n   * Adds the `name=\"apple-itunes-app\"` meta tag.\n   *\n   * @example\n   * ```tsx\n   * itunes: { app: { id: \"123456789\", affiliateData: \"123456789\", appArguments: \"123456789\" } }\n   * // Renders <meta name=\"apple-itunes-app\" content=\"app-id=123456789, affiliate-data=123456789, app-arguments=123456789\" />\n   * ```\n   */\n  itunes?: null | ItunesApp | undefined\n\n  /**\n   * A brief description of the web page.\n   *\n   * @remarks\n   * Rendered as the `abstract` meta tag. This is *not recommended* as it is superseded by `description`.\n   *\n   * @example\n   * ```tsx\n   * abstract: \"My Website Description\"\n   * // Renders <meta name=\"abstract\" content=\"My Website Description\" />\n   * ```\n   */\n  abstract?: null | string | undefined\n\n  /**\n   * The Facebook AppLinks metadata for the document.\n   *\n   * @example\n   * ```tsx\n   * appLinks: {\n   *   ios: { appStoreId: \"123456789\", url: \"https://example.com\" },\n   *   android: { packageName: \"com.example\", url: \"https://example.com\" }\n   * }\n   *\n   * // Renders\n   * <meta property=\"al:ios:app_store_id\" content=\"123456789\" />\n   * <meta property=\"al:ios:url\" content=\"https://example.com\" />\n   * <meta property=\"al:android:package\" content=\"com.example\" />\n   * <meta property=\"al:android:url\" content=\"https://example.com\" />\n   * ```\n   */\n  appLinks?: null | AppLinks | undefined\n\n  /**\n   * The archives link rel property.\n   *\n   * @example\n   * ```tsx\n   * archives: \"https://example.com/archives\"\n   * // Renders <link rel=\"archives\" href=\"https://example.com/archives\" />\n   * ```\n   */\n  archives?: null | string | Array<string> | undefined\n\n  /**\n   * The assets link rel property.\n   *\n   * @example\n   * ```tsx\n   * assets: \"https://example.com/assets\"\n   * // Renders <link rel=\"assets\" href=\"https://example.com/assets\" />\n   * ```\n   */\n  assets?: null | string | Array<string> | undefined\n\n  /**\n   * The bookmarks link rel property.\n   *\n   * @remarks\n   * Although technically against the HTML spec, this is used in practice.\n   *\n   * @example\n   * ```tsx\n   * bookmarks: \"https://example.com/bookmarks\"\n   * // Renders <link rel=\"bookmarks\" href=\"https://example.com/bookmarks\" />\n   * ```\n   */\n  bookmarks?: null | string | Array<string> | undefined\n\n  /**\n   * The pagination link rel properties.\n   *\n   * @example\n   * ```tsx\n   * pagination: {\n   *   previous: \"https://example.com/items?page=1\",\n   *   next: \"https://example.com/items?page=3\"\n   * }\n   *\n   * // Renders\n   * <link rel=\"prev\" href=\"https://example.com/items?page=1\" />\n   * <link rel=\"next\" href=\"https://example.com/items?page=3\" />\n   * ```\n   *\n   * @see https://developers.google.com/search/blog/2011/09/pagination-with-relnext-and-relprev\n   */\n  pagination?: {\n    previous?: null | string | URL | undefined\n    next?: null | string | URL | undefined\n  }\n\n  /**\n   * The category meta name property.\n   *\n   * @example\n   * ```tsx\n   * category: \"My Category\"\n   * // Renders <meta name=\"category\" content=\"My Category\" />\n   * ```\n   */\n  category?: null | string | undefined\n\n  /**\n   * The classification meta name property.\n   *\n   * @example\n   * ```tsx\n   * classification: \"My Classification\"\n   * // Renders <meta name=\"classification\" content=\"My Classification\" />\n   * ```\n   */\n  classification?: null | string | undefined\n\n  /**\n   * Arbitrary name/value pairs for additional metadata.\n   *\n   * @remarks\n   * Use this field to define custom meta tags that are not directly supported.\n   *\n   * @example\n   * ```tsx\n   * other: { custom: [\"meta1\", \"meta2\"] }\n   * ```\n   */\n  other?:\n    | ({\n        [name: string]: string | number | Array<string | number>\n      } & DeprecatedMetadataFields)\n    | undefined\n}\n\n/**\n * ResolvedMetadata represents the fully processed metadata after defaults are applied\n * and relative URLs are composed with `metadataBase`.\n */\ninterface ResolvedMetadata extends DeprecatedMetadataFields {\n  // origin and base path for absolute urls for various metadata links such as\n  // opengraph-image\n  metadataBase: null | URL\n\n  // The Document title and template if defined\n  title: null | AbsoluteTemplateString\n\n  // The Document description, and optionally the opengraph and twitter descriptions\n  description: null | string\n\n  // Standard metadata names\n  // https://developer.mozilla.org/docs/Web/HTML/Element/meta/name\n  applicationName: null | string\n  authors: null | Array<Author>\n  generator: null | string\n  // if you provide an array it will be flattened into a single tag with comma separation\n  keywords: null | Array<string>\n  referrer: null | ReferrerEnum\n  /**\n   * @deprecated\n   */\n  themeColor: null | ThemeColorDescriptor[]\n  /**\n   * @deprecated\n   */\n  colorScheme: null | ColorSchemeEnum\n  /**\n   * @deprecated\n   */\n  viewport: null | string\n  creator: null | string\n  publisher: null | string\n\n  // https://developer.mozilla.org/docs/Web/HTML/Element/meta/name#other_metadata_names\n  robots: null | ResolvedRobots\n\n  // The canonical and alternate URLs for this location\n  alternates: null | ResolvedAlternateURLs\n\n  // Defaults to rel=\"icon\" but the Icons type can be used\n  // to get more specific about rel types\n  icons: null | ResolvedIcons\n\n  openGraph: null | ResolvedOpenGraph\n\n  manifest: null | string | URL\n\n  twitter: null | ResolvedTwitterMetadata\n\n  facebook: null | ResolvedFacebook\n\n  pinterest: null | ResolvedPinterest\n\n  // common verification tokens\n  verification: null | ResolvedVerification\n\n  // Apple web app metadata\n  // https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariHTMLRef/Articles/MetaTags.html\n  appleWebApp: null | ResolvedAppleWebApp\n\n  // Should devices try to interpret various formats and make actionable links\n  // out of them? The canonical example is telephone numbers on mobile that can\n  // be clicked to dial\n  formatDetection: null | FormatDetection\n\n  // meta name=\"apple-itunes-app\"\n  itunes: null | ItunesApp\n\n  // meta name=\"abstract\"\n  // A brief description of what this web-page is about.\n  // Not recommended, superceded by description.\n  // https://www.metatags.org/all-meta-tags-overview/meta-name-abstract/\n  abstract: null | string\n\n  // Facebook AppLinks\n  appLinks: null | ResolvedAppLinks\n\n  // link rel properties\n  archives: null | Array<string>\n  assets: null | Array<string>\n  bookmarks: null | Array<string> // This is technically against HTML spec but is used in wild\n  pagination: {\n    previous: null | string\n    next: null | string\n  }\n\n  // meta name properties\n  category: null | string\n  classification: null | string\n  other:\n    | null\n    | ({\n        [name: string]: string | number | Array<string | number>\n      } & DeprecatedMetadataFields)\n}\n\ntype RobotsFile = {\n  // Apply rules for all\n  rules:\n    | {\n        userAgent?: string | string[] | undefined\n        allow?: string | string[] | undefined\n        disallow?: string | string[] | undefined\n        crawlDelay?: number | undefined\n      }\n    // Apply rules for specific user agents\n    | Array<{\n        userAgent: string | string[]\n        allow?: string | string[] | undefined\n        disallow?: string | string[] | undefined\n        crawlDelay?: number | undefined\n      }>\n  sitemap?: string | string[] | undefined\n  host?: string | undefined\n}\n\ntype SitemapFile = Array<{\n  url: string\n  lastModified?: string | Date | undefined\n  changeFrequency?:\n    | 'always'\n    | 'hourly'\n    | 'daily'\n    | 'weekly'\n    | 'monthly'\n    | 'yearly'\n    | 'never'\n    | undefined\n  priority?: number | undefined\n  alternates?:\n    | {\n        languages?: Languages<string> | undefined\n      }\n    | undefined\n  images?: string[] | undefined\n  videos?: Videos[] | undefined\n}>\n\ntype ResolvingMetadata = Promise<ResolvedMetadata>\ndeclare namespace MetadataRoute {\n  // eslint-disable-next-line @typescript-eslint/no-shadow\n  export type Robots = RobotsFile\n  export type Sitemap = SitemapFile\n  export type Manifest = ManifestFile\n}\n\n/**\n * Interface for the viewport configuration.\n *\n * @remarks\n * This configuration allows defining properties such as width, initial scale, theme colors,\n * and color scheme.\n *\n * @example\n * ```tsx\n * export const viewport: Viewport = {\n *   width: \"device-width\",\n *   initialScale: 1,\n *   themeColor: [\n *     { media: \"(prefers-color-scheme: dark)\", color: \"#000000\" },\n *     { media: \"(prefers-color-scheme: light)\", color: \"#ffffff\" }\n *   ],\n *   colorScheme: \"dark\"\n * }\n * ```\n */\ninterface Viewport extends ViewportLayout {\n  /**\n   * The theme color for the document.\n   *\n   * @example\n   * ```tsx\n   * themeColor: \"#000000\"\n   * // Renders <meta name=\"theme-color\" content=\"#000000\" />\n   *\n   * themeColor: { media: \"(prefers-color-scheme: dark)\", color: \"#000000\" }\n   * // Renders <meta name=\"theme-color\" media=\"(prefers-color-scheme: dark)\" content=\"#000000\" />\n   *\n   * themeColor: [\n   *  { media: \"(prefers-color-scheme: dark)\", color: \"#000000\" },\n   *  { media: \"(prefers-color-scheme: light)\", color: \"#ffffff\" }\n   * ]\n   * // Renders <meta name=\"theme-color\" media=\"(prefers-color-scheme: dark)\" content=\"#000000\" />\n   * // Renders <meta name=\"theme-color\" media=\"(prefers-color-scheme: light)\" content=\"#ffffff\" />\n   * ```\n   */\n  themeColor?:\n    | null\n    | string\n    | ThemeColorDescriptor\n    | ThemeColorDescriptor[]\n    | undefined\n\n  /**\n   * The color scheme for the document.\n   *\n   * @example\n   * ```tsx\n   * colorScheme: \"dark\"\n   * // Renders <meta name=\"color-scheme\" content=\"dark\" />\n   * ```\n   */\n  colorScheme?: null | ColorSchemeEnum | undefined\n}\n\ntype ResolvingViewport = Promise<Viewport>\n\ninterface ResolvedViewport extends ViewportLayout {\n  themeColor: null | ThemeColorDescriptor[]\n  colorScheme: null | ColorSchemeEnum\n}\n\nexport type {\n  Metadata,\n  ResolvedMetadata,\n  ResolvingMetadata,\n  MetadataRoute,\n  Viewport,\n  ResolvingViewport,\n  ResolvedViewport,\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;CAYC"}