"use client";

import { useState, useR<PERSON>, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Send, 
  Bot, 
  User, 
  Plus, 
  Moon, 
  Sun, 
  Trash2, 
  Edit3,
  Image,
  Code,
  FileText,
  Upload,
  Download,
  Palette,
  Wand2,
  FileImage,
  FileCode,
  File,
  Play,
  Square,
  RotateCcw,
  <PERSON><PERSON>,
  CheckCircle,
  AlertCircle,
  Lightbulb,
  Zap
} from "lucide-react";
import { useTheme } from "next-themes";

interface Message {
  id: string;
  content: string;
  role: "user" | "assistant";
  timestamp: Date;
  searchResults?: any[];
  usedWebSearch?: boolean;
  cached?: boolean;
  responseTime?: number;
}

interface ChatSession {
  id: string;
  title: string;
  messages: Message[];
  createdAt: Date;
}

export default function Home() {
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [inputMessage, setInputMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [enableWebSearch, setEnableWebSearch] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'error' | 'checking'>('checking');
  const [useFastMode, setUseFastMode] = useState(true);
  const [editingSessionId, setEditingSessionId] = useState<string | null>(null);
  const [editingTitle, setEditingTitle] = useState("");
  const [activeTool, setActiveTool] = useState<string>("chat");
  const [imageEditorData, setImageEditorData] = useState<{
    image: string | null;
    text: string;
    fontSize: number;
    color: string;
    filter: string;
  }>({
    image: null,
    text: "",
    fontSize: 24,
    color: "#ffffff",
    filter: "none"
  });
  const [codeEditorData, setCodeEditorData] = useState<{
    code: string;
    language: string;
    output: string;
    isRunning: boolean;
    error: string;
    aiSuggestion: string;
  }>({
    code: "",
    language: "javascript",
    output: "",
    isRunning: false,
    error: "",
    aiSuggestion: ""
  });
  const [documentData, setDocumentData] = useState<{
    content: string;
    title: string;
  }>({
    content: "",
    title: "مستند جديد"
  });
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { theme, setTheme } = useTheme();

  // Check connection status on mount
  useEffect(() => {
    const checkConnection = async () => {
      try {
        const response = await fetch('/api/status');
        const data = await response.json();
        if (data.status === 'connected') {
          setConnectionStatus('connected');
        } else {
          setConnectionStatus('error');
        }
      } catch (error) {
        console.error('Connection check failed:', error);
        setConnectionStatus('error');
      }
    };

    checkConnection();
    
    // Check connection every 30 seconds
    const interval = setInterval(checkConnection, 30000);
    
    return () => clearInterval(interval);
  }, []);

  // Load sessions from localStorage on mount
  useEffect(() => {
    const savedSessions = localStorage.getItem('elashrafy-ai-sessions');
    if (savedSessions) {
      const parsed = JSON.parse(savedSessions);
      // Convert date strings back to Date objects
      const sessionsWithDates = parsed.map((session: any) => ({
        ...session,
        createdAt: new Date(session.createdAt),
        messages: session.messages.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }))
      }));
      setSessions(sessionsWithDates);
      if (sessionsWithDates.length > 0) {
        setCurrentSessionId(sessionsWithDates[0].id);
      }
    } else {
      // Create default session if none exist
      createNewSession();
    }
  }, []);

  // Save sessions to localStorage whenever they change
  useEffect(() => {
    if (sessions.length > 0) {
      localStorage.setItem('elashrafy-ai-sessions', JSON.stringify(sessions));
    }
  }, [sessions]);

  const currentSession = sessions.find(s => s.id === currentSessionId);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [currentSession?.messages]);

  const generateSessionTitle = (message: string) => {
    // Generate a concise title from the first message
    const words = message.split(' ').slice(0, 4);
    return words.join(' ') + (message.split(' ').length > 4 ? '...' : '');
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading || !currentSessionId) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputMessage,
      role: "user",
      timestamp: new Date(),
    };

    // Update session with user message
    setSessions(prev => prev.map(session => {
      if (session.id === currentSessionId) {
        const updatedMessages = [...session.messages, userMessage];
        // Update title if this is the first message
        const updatedTitle = session.messages.length === 0 ? generateSessionTitle(inputMessage) : session.title;
        return { ...session, messages: updatedMessages, title: updatedTitle };
      }
      return session;
    }));

    setInputMessage("");
    setIsLoading(true);

    try {
      // Get current session history excluding the latest user message
      const currentSessionData = sessions.find(s => s.id === currentSessionId);
      const sessionHistory = currentSessionData?.messages || [];

      // Call the API - use fast endpoint for better performance
      const apiEndpoint = useFastMode ? '/api/chat-fast' : '/api/chat';
      
      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: inputMessage,
          sessionHistory: sessionHistory,
          enableWebSearch: enableWebSearch,
          optimizeForSpeed: useFastMode
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to get response from AI: ${errorText}`);
      }

      const data = await response.json();

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: data.response || 'عذراً، حدث خطأ في معالجة طلبك.',
        role: "assistant",
        timestamp: new Date(),
        searchResults: data.searchResults,
        usedWebSearch: data.usedWebSearch,
        cached: data.cached,
        responseTime: data.responseTime
      };

      setSessions(prev => prev.map(session => 
        session.id === currentSessionId
          ? { ...session, messages: [...session.messages, assistantMessage] }
          : session
      ));

    } catch (error) {
      console.error('Error sending message:', error);
      
      // Add error message
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: `عذراً، حدث خطأ في الاتصال بالخدمة: ${error instanceof Error ? error.message : 'خطأ غير معروف'}. يرجى المحاولة مرة أخرى.`,
        role: "assistant",
        timestamp: new Date(),
      };

      setSessions(prev => prev.map(session => 
        session.id === currentSessionId
          ? { ...session, messages: [...session.messages, errorMessage] }
          : session
      ));
    } finally {
      setIsLoading(false);
    }
  };

  const createNewSession = () => {
    const newSession: ChatSession = {
      id: Date.now().toString(),
      title: "محادثة جديدة",
      messages: [],
      createdAt: new Date(),
    };
    setSessions(prev => [newSession, ...prev]);
    setCurrentSessionId(newSession.id);
  };

  const deleteSession = (sessionId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setSessions(prev => prev.filter(session => session.id !== sessionId));
    if (sessionId === currentSessionId) {
      const remainingSessions = sessions.filter(s => s.id !== sessionId);
      if (remainingSessions.length > 0) {
        setCurrentSessionId(remainingSessions[0].id);
      } else {
        createNewSession();
      }
    }
  };

  const startEditingSession = (sessionId: string, currentTitle: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setEditingSessionId(sessionId);
    setEditingTitle(currentTitle);
  };

  const saveSessionTitle = (sessionId: string) => {
    setSessions(prev => prev.map(session => 
      session.id === sessionId
        ? { ...session, title: editingTitle }
        : session
    ));
    setEditingSessionId(null);
    setEditingTitle("");
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("ar-SA", { 
      hour: "2-digit", 
      minute: "2-digit" 
    });
  };

  const formatDate = (date: Date) => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
      return "اليوم";
    } else if (date.toDateString() === yesterday.toDateString()) {
      return "أمس";
    } else {
      return date.toLocaleDateString("ar-SA", { 
        month: "short", 
        day: "numeric" 
      });
    }
  };

  // Tool handlers
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImageEditorData(prev => ({
          ...prev,
          image: e.target?.result as string
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const applyImageFilter = (filter: string) => {
    setImageEditorData(prev => ({
      ...prev,
      filter
    }));
  };

  const downloadImage = () => {
    if (!imageEditorData.image) return;
    
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      
      if (ctx) {
        // Apply filter
        switch (imageEditorData.filter) {
          case 'grayscale':
            ctx.filter = 'grayscale(100%)';
            break;
          case 'sepia':
            ctx.filter = 'sepia(100%)';
            break;
          case 'blur':
            ctx.filter = 'blur(5px)';
            break;
          case 'brightness':
            ctx.filter = 'brightness(150%)';
            break;
          default:
            ctx.filter = 'none';
        }
        
        ctx.drawImage(img, 0, 0);
        
        // Reset filter for text
        ctx.filter = 'none';
        ctx.font = `${imageEditorData.fontSize}px Arial`;
        ctx.fillStyle = imageEditorData.color;
        ctx.textAlign = 'center';
        ctx.fillText(imageEditorData.text, canvas.width / 2, canvas.height - 50);
        
        const link = document.createElement('a');
        link.download = 'edited-image.png';
        link.href = canvas.toDataURL();
        link.click();
      }
    };
    
    img.src = imageEditorData.image;
  };

  const downloadCode = () => {
    const blob = new Blob([codeEditorData.code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.download = `code.${codeEditorData.language}`;
    link.href = url;
    link.click();
    URL.revokeObjectURL(url);
  };

  const downloadDocument = () => {
    const blob = new Blob([documentData.content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.download = `${documentData.title}.txt`;
    link.href = url;
    link.click();
    URL.revokeObjectURL(url);
  };

  const generateImageWithAI = async () => {
    if (!imageEditorData.text.trim()) return;
    
    try {
      const response = await fetch('/api/generate-image', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt: imageEditorData.text })
      });
      
      if (response.ok) {
        const data = await response.json();
        setImageEditorData(prev => ({
          ...prev,
          image: data.image
        }));
      }
    } catch (error) {
      console.error('Error generating image:', error);
    }
  };

  // Code editor functions
  const runCode = async () => {
    if (!codeEditorData.code.trim()) return;
    
    setCodeEditorData(prev => ({ ...prev, isRunning: true, error: "", output: "" }));
    
    try {
      const response = await fetch('/api/execute-code', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          code: codeEditorData.code,
          language: codeEditorData.language
        })
      });
      
      const data = await response.json();
      
      if (data.success) {
        setCodeEditorData(prev => ({ 
          ...prev, 
          output: data.output,
          error: ""
        }));
      } else {
        setCodeEditorData(prev => ({ 
          ...prev, 
          error: data.error || "خطأ في تنفيذ الكود",
          output: ""
        }));
      }
    } catch (error) {
      setCodeEditorData(prev => ({ 
        ...prev, 
        error: "فشل الاتصال بخدمة تنفيذ الكود",
        output: ""
      }));
    } finally {
      setCodeEditorData(prev => ({ ...prev, isRunning: false }));
    }
  };

  const generateCodeWithAI = async () => {
    const prompt = `اكتب كود ${codeEditorData.language} يعمل بشكل كامل ومفيد. الكود يجب أن يكون:
    - خالي من الأخطاء
    - يحتوي على تعليقات توضيحية
    - سهل القراءة والفهم
    - عملي وقابل للاستخدام
    
    اكتب الكود هنا:`;
    
    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: prompt,
          sessionHistory: []
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        setCodeEditorData(prev => ({ 
          ...prev, 
          code: data.response,
          aiSuggestion: ""
        }));
      }
    } catch (error) {
      console.error('Error generating code:', error);
    }
  };

  const optimizeCodeWithAI = async () => {
    if (!codeEditorData.code.trim()) return;
    
    const prompt = `حسن الكود التالي بلغة ${codeEditorData.language}. اجعله:
    - أكثر كفاءة
    - أسرع في التنفيذ
    - سهل الصيانة
    - يتبع أفضل الممارسات
    
    الكود الحالي:
    ${codeEditorData.code}`;
    
    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: prompt,
          sessionHistory: []
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        setCodeEditorData(prev => ({ 
          ...prev, 
          aiSuggestion: data.response
        }));
      }
    } catch (error) {
      console.error('Error optimizing code:', error);
    }
  };

  const debugCodeWithAI = async () => {
    if (!codeEditorData.code.trim()) return;
    
    const prompt = `اكتشف الأخطاء في الكود التالي بلغة ${codeEditorData.language} واقترح الحلول:
    
    الكود:
    ${codeEditorData.code}
    
    قدم:
    1. قائمة بالأخطاء الموجودة
    2. الكود المصحح
    3. شرح للإصلاحات`;
    
    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: prompt,
          sessionHistory: []
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        setCodeEditorData(prev => ({ 
          ...prev, 
          aiSuggestion: data.response
        }));
      }
    } catch (error) {
      console.error('Error debugging code:', error);
    }
  };

  const applySuggestion = () => {
    if (codeEditorData.aiSuggestion) {
      setCodeEditorData(prev => ({ 
        ...prev, 
        code: prev.aiSuggestion,
        aiSuggestion: ""
      }));
    }
  };

  const copyCode = () => {
    navigator.clipboard.writeText(codeEditorData.code);
  };

  const clearCode = () => {
    setCodeEditorData(prev => ({ 
      ...prev, 
      code: "",
      output: "",
      error: "",
      aiSuggestion: ""
    }));
  };

  // Code templates
  const loadCodeTemplate = (language: string) => {
    const templates = {
      javascript: `// JavaScript Hello World
function greet(name) {
    return "Hello, " + name + "!";
}

console.log(greet("World"));

// Array example
const numbers = [1, 2, 3, 4, 5];
const doubled = numbers.map(n => n * 2);
console.log("Doubled:", doubled);`,
      
      python: `# Python Hello World
def greet(name):
    return f"Hello, {name}!"

print(greet("World"))

# List example
numbers = [1, 2, 3, 4, 5]
doubled = [n * 2 for n in numbers]
print("Doubled:", doubled)`,
      
      html: `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مرحباً بالعالم</title>
</head>
<body>
    <h1>مرحباً بالعالم!</h1>
    <p>هذه صفحة HTML بسيطة</p>
    <button onclick="alert('تم الضغط!')">اضغط هنا</button>
</body>
</html>`,
      
      css: `/* CSS Styles */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f0f0f0;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.button {
    background-color: #007bff;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.button:hover {
    background-color: #0056b3;
}`,
      
      sql: `-- SQL Examples
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert data
INSERT INTO users (name, email) VALUES 
('أحمد', '<EMAIL>'),
('فاطمة', '<EMAIL>');

-- Query data
SELECT * FROM users WHERE name LIKE 'أ%';

-- Update data
UPDATE users SET email = '<EMAIL>' WHERE id = 1;`,
      
      java: `// Java Hello World
public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
        
        // Array example
        int[] numbers = {1, 2, 3, 4, 5};
        int[] doubled = new int[numbers.length];
        
        for (int i = 0; i < numbers.length; i++) {
            doubled[i] = numbers[i] * 2;
        }
        
        System.out.println("Doubled array:");
        for (int num : doubled) {
            System.out.print(num + " ");
        }
    }
}`,
      
      cpp: `// C++ Hello World
#include <iostream>
#include <vector>
#include <algorithm>

using namespace std;

int main() {
    cout << "Hello, World!" << endl;
    
    // Vector example
    vector<int> numbers = {1, 2, 3, 4, 5};
    vector<int> doubled;
    
    for (int num : numbers) {
        doubled.push_back(num * 2);
    }
    
    cout << "Doubled vector: ";
    for (int num : doubled) {
        cout << num << " ";
    }
    cout << endl;
    
    return 0;
}`
    };
    
    setCodeEditorData(prev => ({ 
      ...prev, 
      code: templates[language as keyof typeof templates] || "",
      language,
      output: "",
      error: "",
      aiSuggestion: ""
    }));
  };

  return (
    <div className="flex h-screen bg-background">
      {/* Tools Sidebar */}
      <div className="flex md:hidden w-16 bg-card border-r flex-col items-center py-4 gap-4">
        <Button
          variant={activeTool === "chat" ? "default" : "ghost"}
          size="icon"
          onClick={() => setActiveTool("chat")}
          className="w-12 h-12"
        >
          <Bot className="h-5 w-5" />
        </Button>
        <Button
          variant={activeTool === "image" ? "default" : "ghost"}
          size="icon"
          onClick={() => setActiveTool("image")}
          className="w-12 h-12"
        >
          <Image className="h-5 w-5" />
        </Button>
        <Button
          variant={activeTool === "code" ? "default" : "ghost"}
          size="icon"
          onClick={() => setActiveTool("code")}
          className="w-12 h-12"
        >
          <Code className="h-5 w-5" />
        </Button>
        <Button
          variant={activeTool === "document" ? "default" : "ghost"}
          size="icon"
          onClick={() => setActiveTool("document")}
          className="w-12 h-12"
        >
          <FileText className="h-5 w-5" />
        </Button>
      </div>

      {/* Chat Sessions Sidebar - Hidden on mobile */}
      <div className={`${activeTool === "chat" ? "flex" : "hidden"} md:flex w-80 bg-card border-r flex-col`}>
        <div className="p-4 border-b">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Bot className="h-6 w-6 text-primary" />
              <div>
                <h1 className="text-xl font-bold">Elashrafy AI</h1>
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${
                    connectionStatus === 'connected' ? 'bg-green-500' : 
                    connectionStatus === 'error' ? 'bg-red-500' : 'bg-yellow-500'
                  }`} />
                  <span className="text-xs text-muted-foreground">
                    {connectionStatus === 'connected' ? 'GLM 4.5 متصل' :
                     connectionStatus === 'error' ? 'غير متصل' : 'جاري التحقق...'}
                  </span>
                </div>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setTheme(theme === "light" ? "dark" : "light")}
            >
              {theme === "light" ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />}
            </Button>
          </div>
          <Button onClick={createNewSession} className="w-full" variant="outline">
            <Plus className="h-4 w-4 mr-2" />
            محادثة جديدة
          </Button>
        </div>
        
        <ScrollArea className="flex-1 p-4">
          <div className="space-y-2">
            {sessions.map((session) => (
              <Card
                key={session.id}
                className={`cursor-pointer transition-colors ${
                  session.id === currentSessionId ? "bg-primary/10" : ""
                }`}
                onClick={() => setCurrentSessionId(session.id)}
              >
                <CardContent className="p-3">
                  <div className="flex items-center justify-between mb-2">
                    {editingSessionId === session.id ? (
                      <div className="flex-1 flex gap-2">
                        <Input
                          value={editingTitle}
                          onChange={(e) => setEditingTitle(e.target.value)}
                          className="h-6 text-sm"
                          onBlur={() => saveSessionTitle(session.id)}
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              saveSessionTitle(session.id);
                            }
                          }}
                          autoFocus
                        />
                      </div>
                    ) : (
                      <h3 className="font-medium text-sm truncate">
                        {session.title}
                      </h3>
                    )}
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6"
                        onClick={(e) => startEditingSession(session.id, session.title, e)}
                      >
                        <Edit3 className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 text-destructive hover:text-destructive"
                        onClick={(e) => deleteSession(session.id, e)}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <Badge variant="secondary" className="text-xs">
                      {session.messages.length} رسائل
                    </Badge>
                    <p className="text-xs text-muted-foreground">
                      {formatDate(session.createdAt)}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </ScrollArea>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="border-b p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Avatar>
                <AvatarFallback>
                  <Bot className="h-4 w-4" />
                </AvatarFallback>
              </Avatar>
              <div>
                <h2 className="font-semibold">Elashrafy AI Assistant</h2>
                <p className="text-sm text-muted-foreground">
                  مدعوم بنموذج GLM 4.5
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {/* Desktop Tools */}
              <div className="hidden md:flex items-center gap-2">
                <Button
                  variant={activeTool === "chat" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setActiveTool("chat")}
                >
                  <Bot className="h-4 w-4 mr-1" />
                  محادثة
                </Button>
                <Button
                  variant={activeTool === "image" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setActiveTool("image")}
                >
                  <Image className="h-4 w-4 mr-1" />
                  صور
                </Button>
                <Button
                  variant={activeTool === "code" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setActiveTool("code")}
                >
                  <Code className="h-4 w-4 mr-1" />
                  كود
                </Button>
                <Button
                  variant={activeTool === "document" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setActiveTool("document")}
                >
                  <FileText className="h-4 w-4 mr-1" />
                  مستند
                </Button>
              </div>
              {/* Mobile Controls */}
              <div className="flex items-center gap-2 md:hidden">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setTheme(theme === "light" ? "dark" : "light")}
                >
                  {theme === "light" ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />}
                </Button>
                <Button onClick={createNewSession} variant="outline" size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Tool Content */}
        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTool} className="h-full">
            <TabsContent value="chat" className="h-full m-0">
              {/* Chat Interface */}
              <div className="flex flex-col h-full">
                <ScrollArea className="flex-1 p-4">
                  <div className="max-w-4xl mx-auto space-y-4">
                    {currentSession?.messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex gap-3 ${
                          message.role === "user" ? "justify-end" : "justify-start"
                        }`}
                      >
                        {message.role === "assistant" && (
                          <Avatar className="h-8 w-8">
                            <AvatarFallback>
                              <Bot className="h-4 w-4" />
                            </AvatarFallback>
                          </Avatar>
                        )}
                        <div
                          className={`max-w-[85%] md:max-w-[70%] rounded-lg px-4 py-3 ${
                            message.role === "user"
                              ? "bg-primary text-primary-foreground"
                              : "bg-muted"
                          }`}
                        >
                          {message.usedWebSearch && (
                            <div className="flex items-center gap-2 mb-2 text-xs text-blue-600 dark:text-blue-400">
                              <Zap className="h-3 w-3" />
                              <span>تم استخدام البحث عبر الإنترنت</span>
                            </div>
                          )}
                          {message.cached && (
                            <div className="flex items-center gap-2 mb-2 text-xs text-green-600 dark:text-green-400">
                              <Lightbulb className="h-3 w-3" />
                              <span>رد سريع (مخزن مؤقتاً)</span>
                            </div>
                          )}
                          <p className="text-sm whitespace-pre-wrap leading-relaxed">
                            {message.content}
                          </p>
                          {message.searchResults && message.searchResults.length > 0 && (
                            <div className="mt-3 pt-2 border-t border-border">
                              <p className="text-xs font-medium mb-2 text-muted-foreground">المصادر:</p>
                              <div className="space-y-1">
                                {message.searchResults.slice(0, 3).map((result: any, index: number) => (
                                  <a
                                    key={index}
                                    href={result.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="block text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 truncate"
                                  >
                                    {result.name}
                                  </a>
                                ))}
                              </div>
                            </div>
                          )}
                          <p className="text-xs opacity-70 mt-2">
                            {formatTime(message.timestamp)}
                            {message.responseTime && (
                              <span className="mr-2">⚡ {Math.round((Date.now() - message.responseTime) / 1000)}s</span>
                            )}
                          </p>
                        </div>
                        {message.role === "user" && (
                          <Avatar className="h-8 w-8">
                            <AvatarFallback>
                              <User className="h-4 w-4" />
                            </AvatarFallback>
                          </Avatar>
                        )}
                      </div>
                    ))}
                    {isLoading && (
                      <div className="flex gap-3 justify-start">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>
                            <Bot className="h-4 w-4" />
                          </AvatarFallback>
                        </Avatar>
                        <div className="bg-muted rounded-lg px-4 py-3">
                          <div className="flex items-center gap-2">
                            <div className="animate-pulse flex space-x-1">
                              <div className="h-2 w-2 bg-current rounded-full"></div>
                              <div className="h-2 w-2 bg-current rounded-full"></div>
                              <div className="h-2 w-2 bg-current rounded-full"></div>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              {useFastMode ? 'جاري الرد السريع...' : 'جاري التفكير...'}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                    {currentSession?.messages.length === 0 && (
                      <div className="text-center py-8">
                        <Bot className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium mb-2">مرحباً بك في Elashrafy AI</h3>
                        <p className="text-muted-foreground">
                          ابدأ محادثة جديدة مع مساعد الذكاء الاصطناعي المدعوم بنموذج GLM 4.5
                        </p>
                      </div>
                    )}
                    <div ref={messagesEndRef} />
                  </div>
                </ScrollArea>

                {/* Input Area */}
                <div className="border-t p-4">
                  <div className="max-w-4xl mx-auto space-y-3">
                    {/* Web Search Toggle */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          id="webSearch"
                          checked={enableWebSearch}
                          onChange={(e) => setEnableWebSearch(e.target.checked)}
                          className="rounded"
                        />
                        <label htmlFor="webSearch" className="text-sm font-medium cursor-pointer flex items-center gap-2">
                          <Zap className="h-4 w-4" />
                          البحث عبر الإنترنت للحصول على إجابات محدثة
                        </label>
                      </div>
                      {enableWebSearch && (
                        <Badge variant="secondary" className="text-xs">
                          مفعّل
                        </Badge>
                      )}
                    </div>

                    {/* Fast Mode Toggle */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          id="fastMode"
                          checked={useFastMode}
                          onChange={(e) => setUseFastMode(e.target.checked)}
                          className="rounded"
                        />
                        <label htmlFor="fastMode" className="text-sm font-medium cursor-pointer flex items-center gap-2">
                          <Lightbulb className="h-4 w-4" />
                          الوضع السريع (استجابات أسرع)
                        </label>
                      </div>
                      {useFastMode && (
                        <Badge variant="secondary" className="text-xs">
                          مفعّل
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex gap-2">
                      <Input
                        placeholder="اكتب رسالتك هنا..."
                        value={inputMessage}
                        onChange={(e) => setInputMessage(e.target.value)}
                        onKeyPress={(e) => {
                          if (e.key === "Enter" && !e.shiftKey) {
                            e.preventDefault();
                            handleSendMessage();
                          }
                        }}
                        disabled={isLoading}
                        className="flex-1"
                      />
                      <Button 
                        onClick={handleSendMessage} 
                        disabled={!inputMessage.trim() || isLoading}
                      >
                        <Send className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="image" className="h-full m-0">
              {/* Image Editor */}
              <div className="flex flex-col h-full p-4">
                <div className="max-w-6xl mx-auto w-full">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
                    {/* Image Preview */}
                    <div className="flex flex-col">
                      <h3 className="text-lg font-semibold mb-4">معاينة الصورة</h3>
                      <div className="flex-1 border-2 border-dashed border-muted-foreground/25 rounded-lg flex items-center justify-center bg-muted/5">
                        {imageEditorData.image ? (
                          <div className="relative">
                            <img 
                              src={imageEditorData.image} 
                              alt="Edited image preview" 
                              className="max-w-full max-h-96 rounded-lg"
                              style={{
                                filter: imageEditorData.filter === 'grayscale' ? 'grayscale(100%)' :
                                        imageEditorData.filter === 'sepia' ? 'sepia(100%)' :
                                        imageEditorData.filter === 'blur' ? 'blur(5px)' :
                                        imageEditorData.filter === 'brightness' ? 'brightness(150%)' : 'none'
                              }}
                            />
                            {imageEditorData.text && (
                              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-center">
                                <p 
                                  style={{ 
                                    fontSize: `${imageEditorData.fontSize}px`,
                                    color: imageEditorData.color,
                                    textShadow: '2px 2px 4px rgba(0,0,0,0.5)'
                                  }}
                                >
                                  {imageEditorData.text}
                                </p>
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="text-center">
                            <FileImage className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                            <p className="text-muted-foreground">ارفع صورة للبدء</p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Image Controls */}
                    <div className="flex flex-col">
                      <h3 className="text-lg font-semibold mb-4">أدوات الصورة</h3>
                      <ScrollArea className="flex-1">
                        <div className="space-y-4">
                          {/* Upload */}
                          <div>
                            <Button 
                              onClick={() => fileInputRef.current?.click()}
                              className="w-full"
                              variant="outline"
                            >
                              <Upload className="h-4 w-4 mr-2" />
                              رفع صورة
                            </Button>
                            <input
                              ref={fileInputRef}
                              type="file"
                              accept="image/*"
                              onChange={handleImageUpload}
                              className="hidden"
                            />
                          </div>

                          {/* Text Overlay */}
                          <div>
                            <label className="text-sm font-medium mb-2 block">نص فوق الصورة</label>
                            <Input
                              placeholder="أدخل النص..."
                              value={imageEditorData.text}
                              onChange={(e) => setImageEditorData(prev => ({ ...prev, text: e.target.value }))}
                            />
                          </div>

                          {/* Font Size */}
                          <div>
                            <label className="text-sm font-medium mb-2 block">حجم الخط</label>
                            <Input
                              type="number"
                              value={imageEditorData.fontSize}
                              onChange={(e) => setImageEditorData(prev => ({ ...prev, fontSize: parseInt(e.target.value) }))}
                              min="10"
                              max="100"
                            />
                          </div>

                          {/* Text Color */}
                          <div>
                            <label className="text-sm font-medium mb-2 block">لون النص</label>
                            <Input
                              type="color"
                              value={imageEditorData.color}
                              onChange={(e) => setImageEditorData(prev => ({ ...prev, color: e.target.value }))}
                            />
                          </div>

                          {/* Filters */}
                          <div>
                            <label className="text-sm font-medium mb-2 block">الفلاتر</label>
                            <div className="grid grid-cols-2 gap-2">
                              <Button
                                variant={imageEditorData.filter === 'none' ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => applyImageFilter('none')}
                              >
                                بدون فلتر
                              </Button>
                              <Button
                                variant={imageEditorData.filter === 'grayscale' ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => applyImageFilter('grayscale')}
                              >
                                أبيض وأسود
                              </Button>
                              <Button
                                variant={imageEditorData.filter === 'sepia' ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => applyImageFilter('sepia')}
                              >
                                سيبيا
                              </Button>
                              <Button
                                variant={imageEditorData.filter === 'blur' ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => applyImageFilter('blur')}
                              >
                                ضبابية
                              </Button>
                            </div>
                          </div>

                          {/* AI Generate */}
                          <div>
                            <Button 
                              onClick={generateImageWithAI}
                              className="w-full"
                              disabled={!imageEditorData.text.trim()}
                            >
                              <Wand2 className="h-4 w-4 mr-2" />
                              توليد صورة بالذكاء الاصطناعي
                            </Button>
                          </div>

                          {/* Download */}
                          <div>
                            <Button 
                              onClick={downloadImage}
                              className="w-full"
                              disabled={!imageEditorData.image}
                            >
                              <Download className="h-4 w-4 mr-2" />
                              تحميل الصورة
                            </Button>
                          </div>
                        </div>
                      </ScrollArea>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="code" className="h-full m-0">
              {/* Code Editor */}
              <div className="flex flex-col h-full p-4">
                <div className="max-w-7xl mx-auto w-full h-full">
                  <div className="grid grid-cols-1 xl:grid-cols-12 gap-6 h-full">
                    {/* Left Sidebar - Language and Tools */}
                    <div className="xl:col-span-3 space-y-6">
                      {/* Language Selection */}
                      <Card>
                        <CardHeader className="pb-3">
                          <CardTitle className="text-lg flex items-center gap-2">
                            <FileCode className="h-5 w-5" />
                            لغة البرمجة
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                          <Select 
                            value={codeEditorData.language} 
                            onValueChange={(value) => {
                              setCodeEditorData(prev => ({ ...prev, language: value }));
                              loadCodeTemplate(value);
                            }}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="javascript">JavaScript</SelectItem>
                              <SelectItem value="python">Python</SelectItem>
                              <SelectItem value="java">Java</SelectItem>
                              <SelectItem value="cpp">C++</SelectItem>
                              <SelectItem value="html">HTML</SelectItem>
                              <SelectItem value="css">CSS</SelectItem>
                              <SelectItem value="sql">SQL</SelectItem>
                            </SelectContent>
                          </Select>
                        </CardContent>
                      </Card>

                      {/* AI Tools */}
                      <Card>
                        <CardHeader className="pb-3">
                          <CardTitle className="text-lg flex items-center gap-2">
                            <Zap className="h-5 w-5" />
                            أدوات الذكاء الاصطناعي
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                          <Button 
                            onClick={generateCodeWithAI}
                            className="w-full justify-start"
                            variant="outline"
                          >
                            <Lightbulb className="h-4 w-4 mr-2" />
                            توليد كود
                          </Button>
                          <Button 
                            onClick={optimizeCodeWithAI}
                            className="w-full justify-start"
                            variant="outline"
                            disabled={!codeEditorData.code.trim()}
                          >
                            <Zap className="h-4 w-4 mr-2" />
                            تحسين الكود
                          </Button>
                          <Button 
                            onClick={debugCodeWithAI}
                            className="w-full justify-start"
                            variant="outline"
                            disabled={!codeEditorData.code.trim()}
                          >
                            <AlertCircle className="h-4 w-4 mr-2" />
                            تصحيح الأخطاء
                          </Button>
                        </CardContent>
                      </Card>

                      {/* Quick Actions */}
                      <Card>
                        <CardHeader className="pb-3">
                          <CardTitle className="text-lg">إجراءات سريعة</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                          <Button 
                            onClick={runCode}
                            className="w-full justify-start"
                            disabled={codeEditorData.isRunning || !codeEditorData.code.trim()}
                          >
                            {codeEditorData.isRunning ? (
                              <Square className="h-4 w-4 mr-2" />
                            ) : (
                              <Play className="h-4 w-4 mr-2" />
                            )}
                            {codeEditorData.isRunning ? 'جاري التنفيذ...' : 'تشغيل الكود'}
                          </Button>
                          <Button 
                            onClick={copyCode}
                            className="w-full justify-start"
                            variant="outline"
                            disabled={!codeEditorData.code.trim()}
                          >
                            <Copy className="h-4 w-4 mr-2" />
                            نسخ الكود
                          </Button>
                          <Button 
                            onClick={clearCode}
                            className="w-full justify-start"
                            variant="outline"
                          >
                            <RotateCcw className="h-4 w-4 mr-2" />
                            مسح الكود
                          </Button>
                          <Button 
                            onClick={downloadCode}
                            className="w-full justify-start"
                            variant="outline"
                            disabled={!codeEditorData.code.trim()}
                          >
                            <Download className="h-4 w-4 mr-2" />
                            تحميل الكود
                          </Button>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Main Editor Area */}
                    <div className="xl:col-span-9 flex flex-col space-y-4">
                      {/* Code Editor */}
                      <Card className="flex-1">
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-lg">محرر الأكواد</CardTitle>
                            <Badge variant="secondary">
                              {codeEditorData.language.toUpperCase()}
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardContent className="p-0">
                          <Textarea
                            value={codeEditorData.code}
                            onChange={(e) => setCodeEditorData(prev => ({ ...prev, code: e.target.value }))}
                            placeholder={`اكتب كود ${codeEditorData.language.toUpperCase()} هنا...`}
                            className="w-full h-64 border-0 rounded-none font-mono text-sm resize-none focus:outline-none"
                            dir="ltr"
                          />
                        </CardContent>
                      </Card>

                      {/* AI Suggestion */}
                      {codeEditorData.aiSuggestion && (
                        <Card>
                          <CardHeader className="pb-3">
                            <div className="flex items-center justify-between">
                              <CardTitle className="text-lg flex items-center gap-2">
                                <CheckCircle className="h-5 w-5 text-green-500" />
                                اقتراح الذكاء الاصطناعي
                              </CardTitle>
                              <Button 
                                onClick={applySuggestion}
                                size="sm"
                              >
                                تطبيق الاقتراح
                              </Button>
                            </div>
                          </CardHeader>
                          <CardContent>
                            <pre className="whitespace-pre-wrap text-sm bg-muted p-3 rounded-lg">
                              {codeEditorData.aiSuggestion}
                            </pre>
                          </CardContent>
                        </Card>
                      )}

                      {/* Output Console */}
                      <Card>
                        <CardHeader className="pb-3">
                          <CardTitle className="text-lg flex items-center gap-2">
                            {codeEditorData.error ? (
                              <AlertCircle className="h-5 w-5 text-red-500" />
                            ) : (
                              <CheckCircle className="h-5 w-5 text-green-500" />
                            )}
                            نتيجة التنفيذ
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          {codeEditorData.error ? (
                            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                              <p className="text-red-800 text-sm font-mono">{codeEditorData.error}</p>
                            </div>
                          ) : codeEditorData.output ? (
                            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                              <pre className="text-green-800 text-sm font-mono whitespace-pre-wrap">
                                {codeEditorData.output}
                              </pre>
                            </div>
                          ) : (
                            <p className="text-muted-foreground text-sm">
                              اضغط على "تشغيل الكود" لرؤية النتيجة هنا
                            </p>
                          )}
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="document" className="h-full m-0">
              {/* Document Editor */}
              <div className="flex flex-col h-full p-4">
                <div className="max-w-4xl mx-auto w-full">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <Input
                        value={documentData.title}
                        onChange={(e) => setDocumentData(prev => ({ ...prev, title: e.target.value }))}
                        className="text-xl font-semibold border-none p-0 focus-visible:ring-0"
                        placeholder="عنوان المستند"
                      />
                    </div>
                    <Button onClick={downloadDocument}>
                      <Download className="h-4 w-4 mr-2" />
                      تحميل المستند
                    </Button>
                  </div>
                  <div className="flex-1">
                    <textarea
                      value={documentData.content}
                      onChange={(e) => setDocumentData(prev => ({ ...prev, content: e.target.value }))}
                      placeholder="ابدأ كتابة مستندك هنا..."
                      className="w-full h-full p-4 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}