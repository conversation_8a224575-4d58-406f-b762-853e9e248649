{"version": 3, "sources": ["../../../../src/server/typescript/rules/metadata.ts"], "sourcesContent": ["import { NEXT_TS_ERRORS } from '../constant'\nimport {\n  getSource,\n  getSourceFromVirtualTsEnv,\n  getTs,\n  getType<PERSON><PERSON><PERSON>,\n  isPositionInsideNode,\n  log,\n  virtualTsEnv,\n} from '../utils'\n\nimport type tsModule from 'typescript/lib/tsserverlibrary'\n\nconst TYPE_ANNOTATION = ': Metadata | null'\nconst TYPE_ANNOTATION_ASYNC = ': Promise<Metadata | null>'\nconst TYPE_IMPORT = `\\n\\nimport type { Metadata } from 'next'`\n\n// Find the `export const metadata = ...` node.\nfunction getMetadataExport(fileName: string, position: number) {\n  const source = getSource(fileName)\n  let metadataExport: tsModule.VariableDeclaration | undefined\n\n  if (source) {\n    const ts = getTs()\n    ts.forEachChild(source, function visit(node) {\n      if (metadataExport) return\n\n      // Covered by this node\n      if (isPositionInsideNode(position, node)) {\n        // Export variable\n        if (\n          ts.isVariableStatement(node) &&\n          node.modifiers?.some((m) => m.kind === ts.SyntaxKind.ExportKeyword)\n        ) {\n          if (ts.isVariableDeclarationList(node.declarationList)) {\n            for (const declaration of node.declarationList.declarations) {\n              if (\n                isPositionInsideNode(position, declaration) &&\n                declaration.name.getText() === 'metadata'\n              ) {\n                // `export const metadata = ...`\n                metadataExport = declaration\n                return\n              }\n            }\n          }\n        }\n      }\n    })\n  }\n  return metadataExport\n}\n\nfunction updateVirtualFileWithType(\n  fileName: string,\n  node: tsModule.VariableDeclaration | tsModule.FunctionDeclaration,\n  isGenerateMetadata?: boolean\n) {\n  const source = getSource(fileName)\n  if (!source) return\n\n  // We annotate with the type in a virtual language service\n  const sourceText = source.getFullText()\n  let nodeEnd: number\n  let annotation: string\n\n  const ts = getTs()\n  if (ts.isFunctionDeclaration(node)) {\n    if (isGenerateMetadata) {\n      nodeEnd = node.body!.getFullStart()\n      const isAsync = node.modifiers?.some(\n        (m) => m.kind === ts.SyntaxKind.AsyncKeyword\n      )\n      annotation = isAsync ? TYPE_ANNOTATION_ASYNC : TYPE_ANNOTATION\n    } else {\n      return\n    }\n  } else {\n    nodeEnd = node.name.getFullStart() + node.name.getFullWidth()\n    annotation = TYPE_ANNOTATION\n  }\n\n  const newSource =\n    sourceText.slice(0, nodeEnd) +\n    annotation +\n    sourceText.slice(nodeEnd) +\n    TYPE_IMPORT\n\n  if (virtualTsEnv.getSourceFile(fileName)) {\n    log('Updating file: ' + fileName)\n    virtualTsEnv.updateFile(fileName, newSource)\n  } else {\n    log('Creating file: ' + fileName)\n    virtualTsEnv.createFile(fileName, newSource)\n  }\n\n  return [nodeEnd, annotation.length]\n}\n\nfunction isTyped(\n  node: tsModule.VariableDeclaration | tsModule.FunctionDeclaration\n) {\n  return node.type !== undefined\n}\n\nfunction proxyDiagnostics(\n  fileName: string,\n  pos: number[],\n  n: tsModule.VariableDeclaration | tsModule.FunctionDeclaration\n) {\n  // Get diagnostics\n  const diagnostics =\n    virtualTsEnv.languageService.getSemanticDiagnostics(fileName)\n  const source = getSourceFromVirtualTsEnv(fileName)\n\n  // Filter and map the results\n  return diagnostics\n    .filter((d) => {\n      if (d.start === undefined || d.length === undefined) return false\n      if (d.start < n.getFullStart()) return false\n      if (d.start + d.length >= n.getFullStart() + n.getFullWidth() + pos[1])\n        return false\n      return true\n    })\n    .map((d) => {\n      return {\n        file: source,\n        category: d.category,\n        code: d.code,\n        messageText: d.messageText,\n        start: d.start! < pos[0] ? d.start : d.start! - pos[1],\n        length: d.length,\n      }\n    })\n}\n\nconst metadata = {\n  filterCompletionsAtPosition(\n    fileName: string,\n    position: number,\n    _options: any,\n    prior: tsModule.WithMetadata<tsModule.CompletionInfo>\n  ) {\n    const node = getMetadataExport(fileName, position)\n    if (!node) return prior\n    if (isTyped(node)) return prior\n\n    // We annotate with the type in a virtual language service\n    const pos = updateVirtualFileWithType(fileName, node)\n    if (pos === undefined) return prior\n\n    // Get completions\n    const newPos = position <= pos[0] ? position : position + pos[1]\n    const completions = virtualTsEnv.languageService.getCompletionsAtPosition(\n      fileName,\n      newPos,\n      undefined\n    )\n\n    if (completions) {\n      const ts = getTs()\n      completions.isIncomplete = true\n      // https://github.com/microsoft/TypeScript/blob/4dc677b292354f4b9162452b2e00f4d7dd118221/src/services/types.ts#L1428-L1433\n      if (completions.optionalReplacementSpan) {\n        // Adjust the start position of the text span to original source.\n        completions.optionalReplacementSpan.start -= newPos - position\n      }\n      completions.entries = completions.entries\n        .filter((e) => {\n          return [\n            ts.ScriptElementKind.memberVariableElement,\n            ts.ScriptElementKind.typeElement,\n            ts.ScriptElementKind.string,\n          ].includes(e.kind)\n        })\n        .map((e) => {\n          const insertText =\n            e.kind === ts.ScriptElementKind.memberVariableElement &&\n            /^[a-zA-Z0-9_]+$/.test(e.name)\n              ? e.name + ': '\n              : e.name\n\n          return {\n            name: e.name,\n            insertText,\n            kind: e.kind,\n            kindModifiers: e.kindModifiers,\n            sortText: '!' + e.name,\n            labelDetails: {\n              description: `Next.js metadata`,\n            },\n            data: e.data,\n          }\n        })\n\n      return completions\n    }\n\n    return prior\n  },\n\n  getSemanticDiagnosticsForExportVariableStatementInClientEntry(\n    fileName: string,\n    node: tsModule.VariableStatement | tsModule.FunctionDeclaration\n  ) {\n    const source = getSource(fileName)\n    const ts = getTs()\n\n    // It is not allowed to export `metadata` or `generateMetadata` in client entry\n    if (ts.isFunctionDeclaration(node)) {\n      if (node.name?.getText() === 'generateMetadata') {\n        return [\n          {\n            file: source,\n            category: ts.DiagnosticCategory.Error,\n            code: NEXT_TS_ERRORS.INVALID_METADATA_EXPORT,\n            messageText: `The Next.js 'generateMetadata' API is not allowed in a client component.`,\n            start: node.name.getStart(),\n            length: node.name.getWidth(),\n          },\n        ]\n      }\n    } else {\n      for (const declaration of node.declarationList.declarations) {\n        const name = declaration.name.getText()\n        if (name === 'metadata') {\n          return [\n            {\n              file: source,\n              category: ts.DiagnosticCategory.Error,\n              code: NEXT_TS_ERRORS.INVALID_METADATA_EXPORT,\n              messageText: `The Next.js 'metadata' API is not allowed in a client component.`,\n              start: declaration.name.getStart(),\n              length: declaration.name.getWidth(),\n            },\n          ]\n        }\n      }\n    }\n    return []\n  },\n\n  getSemanticDiagnosticsForExportVariableStatement(\n    fileName: string,\n    node: tsModule.VariableStatement | tsModule.FunctionDeclaration\n  ) {\n    const ts = getTs()\n\n    if (ts.isFunctionDeclaration(node)) {\n      if (node.name?.getText() === 'generateMetadata') {\n        if (isTyped(node)) return []\n\n        // We annotate with the type in a virtual language service\n        const pos = updateVirtualFileWithType(fileName, node, true)\n        if (!pos) return []\n\n        return proxyDiagnostics(fileName, pos, node)\n      }\n    } else {\n      for (const declaration of node.declarationList.declarations) {\n        if (declaration.name.getText() === 'metadata') {\n          if (isTyped(declaration)) break\n\n          // We annotate with the type in a virtual language service\n          const pos = updateVirtualFileWithType(fileName, declaration)\n          if (!pos) break\n\n          return proxyDiagnostics(fileName, pos, declaration)\n        }\n      }\n    }\n    return []\n  },\n\n  getSemanticDiagnosticsForExportDeclarationInClientEntry(\n    fileName: string,\n    node: tsModule.ExportDeclaration\n  ) {\n    const ts = getTs()\n    const source = getSource(fileName)\n    const diagnostics: tsModule.Diagnostic[] = []\n\n    const exportClause = node.exportClause\n    if (exportClause && ts.isNamedExports(exportClause)) {\n      for (const e of exportClause.elements) {\n        if (['generateMetadata', 'metadata'].includes(e.name.getText())) {\n          diagnostics.push({\n            file: source,\n            category: ts.DiagnosticCategory.Error,\n            code: NEXT_TS_ERRORS.INVALID_METADATA_EXPORT,\n            messageText: `The Next.js '${e.name.getText()}' API is not allowed in a client component.`,\n            start: e.name.getStart(),\n            length: e.name.getWidth(),\n          })\n        }\n      }\n    }\n\n    return diagnostics\n  },\n\n  getSemanticDiagnosticsForExportDeclaration(\n    fileName: string,\n    node: tsModule.ExportDeclaration\n  ) {\n    const ts = getTs()\n\n    const exportClause = node.exportClause\n    if (exportClause && ts.isNamedExports(exportClause)) {\n      for (const e of exportClause.elements) {\n        if (e.name.getText() === 'metadata') {\n          // Get the original declaration node of element\n          const typeChecker = getTypeChecker()\n          if (typeChecker) {\n            const symbol = typeChecker.getSymbolAtLocation(e.name)\n            if (symbol) {\n              const metadataSymbol = typeChecker.getAliasedSymbol(symbol)\n              if (metadataSymbol && metadataSymbol.declarations) {\n                const declaration = metadataSymbol.declarations[0]\n                if (declaration && ts.isVariableDeclaration(declaration)) {\n                  if (isTyped(declaration)) break\n\n                  const declarationFileName =\n                    declaration.getSourceFile().fileName\n                  const isSameFile = declarationFileName === fileName\n\n                  // We annotate with the type in a virtual language service\n                  const pos = updateVirtualFileWithType(\n                    declarationFileName,\n                    declaration\n                  )\n                  if (!pos) break\n\n                  const diagnostics = proxyDiagnostics(\n                    declarationFileName,\n                    pos,\n                    declaration\n                  )\n                  if (diagnostics.length) {\n                    if (isSameFile) {\n                      return diagnostics\n                    } else {\n                      return [\n                        {\n                          file: getSource(fileName),\n                          category: ts.DiagnosticCategory.Error,\n                          code: NEXT_TS_ERRORS.INVALID_METADATA_EXPORT,\n                          messageText: `The 'metadata' export value is not typed correctly, please make sure it is typed as 'Metadata':\\nhttps://nextjs.org/docs/app/building-your-application/optimizing/metadata#static-metadata`,\n                          start: e.name.getStart(),\n                          length: e.name.getWidth(),\n                        },\n                      ]\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    return []\n  },\n\n  getCompletionEntryDetails(\n    fileName: string,\n    position: number,\n    entryName: string,\n    formatOptions: tsModule.FormatCodeOptions,\n    source: string,\n    preferences: tsModule.UserPreferences,\n    data: tsModule.CompletionEntryData\n  ) {\n    const node = getMetadataExport(fileName, position)\n    if (!node) return\n    if (isTyped(node)) return\n\n    // We annotate with the type in a virtual language service\n    const pos = updateVirtualFileWithType(fileName, node)\n    if (pos === undefined) return\n\n    const newPos = position <= pos[0] ? position : position + pos[1]\n\n    const details = virtualTsEnv.languageService.getCompletionEntryDetails(\n      fileName,\n      newPos,\n      entryName,\n      formatOptions,\n      source,\n      preferences,\n      data\n    )\n    return details\n  },\n\n  getQuickInfoAtPosition(fileName: string, position: number) {\n    const node = getMetadataExport(fileName, position)\n    if (!node) return\n    if (isTyped(node)) return\n\n    // We annotate with the type in a virtual language service\n    const pos = updateVirtualFileWithType(fileName, node)\n    if (pos === undefined) return\n\n    const newPos = position <= pos[0] ? position : position + pos[1]\n    const insight = virtualTsEnv.languageService.getQuickInfoAtPosition(\n      fileName,\n      newPos\n    )\n    return insight\n  },\n\n  getDefinitionAndBoundSpan(fileName: string, position: number) {\n    const node = getMetadataExport(fileName, position)\n    if (!node) return\n    if (isTyped(node)) return\n    if (!isPositionInsideNode(position, node)) return\n    // We annotate with the type in a virtual language service\n    const pos = updateVirtualFileWithType(fileName, node)\n    if (pos === undefined) return\n    const newPos = position <= pos[0] ? position : position + pos[1]\n\n    const definitionInfoAndBoundSpan =\n      virtualTsEnv.languageService.getDefinitionAndBoundSpan(fileName, newPos)\n\n    if (definitionInfoAndBoundSpan) {\n      // Adjust the start position of the text span\n      if (definitionInfoAndBoundSpan.textSpan.start > pos[0]) {\n        definitionInfoAndBoundSpan.textSpan.start -= pos[1]\n      }\n    }\n    return definitionInfoAndBoundSpan\n  },\n}\n\nexport default metadata\n"], "names": ["TYPE_ANNOTATION", "TYPE_ANNOTATION_ASYNC", "TYPE_IMPORT", "getMetadataExport", "fileName", "position", "source", "getSource", "metadataExport", "ts", "getTs", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "visit", "node", "isPositionInsideNode", "isVariableStatement", "modifiers", "some", "m", "kind", "SyntaxKind", "ExportKeyword", "isVariableDeclarationList", "declarationList", "declaration", "declarations", "name", "getText", "updateVirtualFileWithType", "isGenerateMetadata", "sourceText", "getFullText", "nodeEnd", "annotation", "isFunctionDeclaration", "body", "getFullStart", "isAsync", "AsyncKeyword", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "newSource", "slice", "virtualTsEnv", "getSourceFile", "log", "updateFile", "createFile", "length", "isTyped", "type", "undefined", "proxyDiagnostics", "pos", "n", "diagnostics", "languageService", "getSemanticDiagnostics", "getSourceFromVirtualTsEnv", "filter", "d", "start", "map", "file", "category", "code", "messageText", "metadata", "filterCompletionsAtPosition", "_options", "prior", "newPos", "completions", "getCompletionsAtPosition", "isIncomplete", "optionalReplacementSpan", "entries", "e", "ScriptElementKind", "memberVariableElement", "typeElement", "string", "includes", "insertText", "test", "kindModifiers", "sortText", "labelDetails", "description", "data", "getSemanticDiagnosticsForExportVariableStatementInClientEntry", "DiagnosticCategory", "Error", "NEXT_TS_ERRORS", "INVALID_METADATA_EXPORT", "getStart", "getWidth", "getSemanticDiagnosticsForExportVariableStatement", "getSemanticDiagnosticsForExportDeclarationInClientEntry", "exportClause", "isNamedExports", "elements", "push", "getSemanticDiagnosticsForExportDeclaration", "typeC<PERSON>cker", "getType<PERSON><PERSON>cker", "symbol", "getSymbolAtLocation", "metadataSymbol", "getAliasedSymbol", "isVariableDeclaration", "declarationFileName", "isSameFile", "getCompletionEntryDetails", "entryName", "formatOptions", "preferences", "details", "getQuickInfoAtPosition", "insight", "getDefinitionAndBoundSpan", "definitionInfoAndBoundSpan", "textSpan"], "mappings": ";;;;+BAobA;;;eAAA;;;0BApb+B;uBASxB;AAIP,MAAMA,kBAAkB;AACxB,MAAMC,wBAAwB;AAC9B,MAAMC,cAAc,CAAC,wCAAwC,CAAC;AAE9D,+CAA+C;AAC/C,SAASC,kBAAkBC,QAAgB,EAAEC,QAAgB;IAC3D,MAAMC,SAASC,IAAAA,gBAAS,EAACH;IACzB,IAAII;IAEJ,IAAIF,QAAQ;QACV,MAAMG,KAAKC,IAAAA,YAAK;QAChBD,GAAGE,YAAY,CAACL,QAAQ,SAASM,MAAMC,IAAI;YACzC,IAAIL,gBAAgB;YAEpB,uBAAuB;YACvB,IAAIM,IAAAA,2BAAoB,EAACT,UAAUQ,OAAO;oBAItCA;gBAHF,kBAAkB;gBAClB,IACEJ,GAAGM,mBAAmB,CAACF,WACvBA,kBAAAA,KAAKG,SAAS,qBAAdH,gBAAgBI,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAKV,GAAGW,UAAU,CAACC,aAAa,IAClE;oBACA,IAAIZ,GAAGa,yBAAyB,CAACT,KAAKU,eAAe,GAAG;wBACtD,KAAK,MAAMC,eAAeX,KAAKU,eAAe,CAACE,YAAY,CAAE;4BAC3D,IACEX,IAAAA,2BAAoB,EAACT,UAAUmB,gBAC/BA,YAAYE,IAAI,CAACC,OAAO,OAAO,YAC/B;gCACA,gCAAgC;gCAChCnB,iBAAiBgB;gCACjB;4BACF;wBACF;oBACF;gBACF;YACF;QACF;IACF;IACA,OAAOhB;AACT;AAEA,SAASoB,0BACPxB,QAAgB,EAChBS,IAAiE,EACjEgB,kBAA4B;IAE5B,MAAMvB,SAASC,IAAAA,gBAAS,EAACH;IACzB,IAAI,CAACE,QAAQ;IAEb,0DAA0D;IAC1D,MAAMwB,aAAaxB,OAAOyB,WAAW;IACrC,IAAIC;IACJ,IAAIC;IAEJ,MAAMxB,KAAKC,IAAAA,YAAK;IAChB,IAAID,GAAGyB,qBAAqB,CAACrB,OAAO;QAClC,IAAIgB,oBAAoB;gBAENhB;YADhBmB,UAAUnB,KAAKsB,IAAI,CAAEC,YAAY;YACjC,MAAMC,WAAUxB,kBAAAA,KAAKG,SAAS,qBAAdH,gBAAgBI,IAAI,CAClC,CAACC,IAAMA,EAAEC,IAAI,KAAKV,GAAGW,UAAU,CAACkB,YAAY;YAE9CL,aAAaI,UAAUpC,wBAAwBD;QACjD,OAAO;YACL;QACF;IACF,OAAO;QACLgC,UAAUnB,KAAKa,IAAI,CAACU,YAAY,KAAKvB,KAAKa,IAAI,CAACa,YAAY;QAC3DN,aAAajC;IACf;IAEA,MAAMwC,YACJV,WAAWW,KAAK,CAAC,GAAGT,WACpBC,aACAH,WAAWW,KAAK,CAACT,WACjB9B;IAEF,IAAIwC,mBAAY,CAACC,aAAa,CAACvC,WAAW;QACxCwC,IAAAA,UAAG,EAAC,oBAAoBxC;QACxBsC,mBAAY,CAACG,UAAU,CAACzC,UAAUoC;IACpC,OAAO;QACLI,IAAAA,UAAG,EAAC,oBAAoBxC;QACxBsC,mBAAY,CAACI,UAAU,CAAC1C,UAAUoC;IACpC;IAEA,OAAO;QAACR;QAASC,WAAWc,MAAM;KAAC;AACrC;AAEA,SAASC,QACPnC,IAAiE;IAEjE,OAAOA,KAAKoC,IAAI,KAAKC;AACvB;AAEA,SAASC,iBACP/C,QAAgB,EAChBgD,GAAa,EACbC,CAA8D;IAE9D,kBAAkB;IAClB,MAAMC,cACJZ,mBAAY,CAACa,eAAe,CAACC,sBAAsB,CAACpD;IACtD,MAAME,SAASmD,IAAAA,gCAAyB,EAACrD;IAEzC,6BAA6B;IAC7B,OAAOkD,YACJI,MAAM,CAAC,CAACC;QACP,IAAIA,EAAEC,KAAK,KAAKV,aAAaS,EAAEZ,MAAM,KAAKG,WAAW,OAAO;QAC5D,IAAIS,EAAEC,KAAK,GAAGP,EAAEjB,YAAY,IAAI,OAAO;QACvC,IAAIuB,EAAEC,KAAK,GAAGD,EAAEZ,MAAM,IAAIM,EAAEjB,YAAY,KAAKiB,EAAEd,YAAY,KAAKa,GAAG,CAAC,EAAE,EACpE,OAAO;QACT,OAAO;IACT,GACCS,GAAG,CAAC,CAACF;QACJ,OAAO;YACLG,MAAMxD;YACNyD,UAAUJ,EAAEI,QAAQ;YACpBC,MAAML,EAAEK,IAAI;YACZC,aAAaN,EAAEM,WAAW;YAC1BL,OAAOD,EAAEC,KAAK,GAAIR,GAAG,CAAC,EAAE,GAAGO,EAAEC,KAAK,GAAGD,EAAEC,KAAK,GAAIR,GAAG,CAAC,EAAE;YACtDL,QAAQY,EAAEZ,MAAM;QAClB;IACF;AACJ;AAEA,MAAMmB,WAAW;IACfC,6BACE/D,QAAgB,EAChBC,QAAgB,EAChB+D,QAAa,EACbC,KAAqD;QAErD,MAAMxD,OAAOV,kBAAkBC,UAAUC;QACzC,IAAI,CAACQ,MAAM,OAAOwD;QAClB,IAAIrB,QAAQnC,OAAO,OAAOwD;QAE1B,0DAA0D;QAC1D,MAAMjB,MAAMxB,0BAA0BxB,UAAUS;QAChD,IAAIuC,QAAQF,WAAW,OAAOmB;QAE9B,kBAAkB;QAClB,MAAMC,SAASjE,YAAY+C,GAAG,CAAC,EAAE,GAAG/C,WAAWA,WAAW+C,GAAG,CAAC,EAAE;QAChE,MAAMmB,cAAc7B,mBAAY,CAACa,eAAe,CAACiB,wBAAwB,CACvEpE,UACAkE,QACApB;QAGF,IAAIqB,aAAa;YACf,MAAM9D,KAAKC,IAAAA,YAAK;YAChB6D,YAAYE,YAAY,GAAG;YAC3B,0HAA0H;YAC1H,IAAIF,YAAYG,uBAAuB,EAAE;gBACvC,iEAAiE;gBACjEH,YAAYG,uBAAuB,CAACd,KAAK,IAAIU,SAASjE;YACxD;YACAkE,YAAYI,OAAO,GAAGJ,YAAYI,OAAO,CACtCjB,MAAM,CAAC,CAACkB;gBACP,OAAO;oBACLnE,GAAGoE,iBAAiB,CAACC,qBAAqB;oBAC1CrE,GAAGoE,iBAAiB,CAACE,WAAW;oBAChCtE,GAAGoE,iBAAiB,CAACG,MAAM;iBAC5B,CAACC,QAAQ,CAACL,EAAEzD,IAAI;YACnB,GACC0C,GAAG,CAAC,CAACe;gBACJ,MAAMM,aACJN,EAAEzD,IAAI,KAAKV,GAAGoE,iBAAiB,CAACC,qBAAqB,IACrD,kBAAkBK,IAAI,CAACP,EAAElD,IAAI,IACzBkD,EAAElD,IAAI,GAAG,OACTkD,EAAElD,IAAI;gBAEZ,OAAO;oBACLA,MAAMkD,EAAElD,IAAI;oBACZwD;oBACA/D,MAAMyD,EAAEzD,IAAI;oBACZiE,eAAeR,EAAEQ,aAAa;oBAC9BC,UAAU,MAAMT,EAAElD,IAAI;oBACtB4D,cAAc;wBACZC,aAAa,CAAC,gBAAgB,CAAC;oBACjC;oBACAC,MAAMZ,EAAEY,IAAI;gBACd;YACF;YAEF,OAAOjB;QACT;QAEA,OAAOF;IACT;IAEAoB,+DACErF,QAAgB,EAChBS,IAA+D;QAE/D,MAAMP,SAASC,IAAAA,gBAAS,EAACH;QACzB,MAAMK,KAAKC,IAAAA,YAAK;QAEhB,+EAA+E;QAC/E,IAAID,GAAGyB,qBAAqB,CAACrB,OAAO;gBAC9BA;YAAJ,IAAIA,EAAAA,aAAAA,KAAKa,IAAI,qBAATb,WAAWc,OAAO,QAAO,oBAAoB;gBAC/C,OAAO;oBACL;wBACEmC,MAAMxD;wBACNyD,UAAUtD,GAAGiF,kBAAkB,CAACC,KAAK;wBACrC3B,MAAM4B,wBAAc,CAACC,uBAAuB;wBAC5C5B,aAAa,CAAC,wEAAwE,CAAC;wBACvFL,OAAO/C,KAAKa,IAAI,CAACoE,QAAQ;wBACzB/C,QAAQlC,KAAKa,IAAI,CAACqE,QAAQ;oBAC5B;iBACD;YACH;QACF,OAAO;YACL,KAAK,MAAMvE,eAAeX,KAAKU,eAAe,CAACE,YAAY,CAAE;gBAC3D,MAAMC,OAAOF,YAAYE,IAAI,CAACC,OAAO;gBACrC,IAAID,SAAS,YAAY;oBACvB,OAAO;wBACL;4BACEoC,MAAMxD;4BACNyD,UAAUtD,GAAGiF,kBAAkB,CAACC,KAAK;4BACrC3B,MAAM4B,wBAAc,CAACC,uBAAuB;4BAC5C5B,aAAa,CAAC,gEAAgE,CAAC;4BAC/EL,OAAOpC,YAAYE,IAAI,CAACoE,QAAQ;4BAChC/C,QAAQvB,YAAYE,IAAI,CAACqE,QAAQ;wBACnC;qBACD;gBACH;YACF;QACF;QACA,OAAO,EAAE;IACX;IAEAC,kDACE5F,QAAgB,EAChBS,IAA+D;QAE/D,MAAMJ,KAAKC,IAAAA,YAAK;QAEhB,IAAID,GAAGyB,qBAAqB,CAACrB,OAAO;gBAC9BA;YAAJ,IAAIA,EAAAA,aAAAA,KAAKa,IAAI,qBAATb,WAAWc,OAAO,QAAO,oBAAoB;gBAC/C,IAAIqB,QAAQnC,OAAO,OAAO,EAAE;gBAE5B,0DAA0D;gBAC1D,MAAMuC,MAAMxB,0BAA0BxB,UAAUS,MAAM;gBACtD,IAAI,CAACuC,KAAK,OAAO,EAAE;gBAEnB,OAAOD,iBAAiB/C,UAAUgD,KAAKvC;YACzC;QACF,OAAO;YACL,KAAK,MAAMW,eAAeX,KAAKU,eAAe,CAACE,YAAY,CAAE;gBAC3D,IAAID,YAAYE,IAAI,CAACC,OAAO,OAAO,YAAY;oBAC7C,IAAIqB,QAAQxB,cAAc;oBAE1B,0DAA0D;oBAC1D,MAAM4B,MAAMxB,0BAA0BxB,UAAUoB;oBAChD,IAAI,CAAC4B,KAAK;oBAEV,OAAOD,iBAAiB/C,UAAUgD,KAAK5B;gBACzC;YACF;QACF;QACA,OAAO,EAAE;IACX;IAEAyE,yDACE7F,QAAgB,EAChBS,IAAgC;QAEhC,MAAMJ,KAAKC,IAAAA,YAAK;QAChB,MAAMJ,SAASC,IAAAA,gBAAS,EAACH;QACzB,MAAMkD,cAAqC,EAAE;QAE7C,MAAM4C,eAAerF,KAAKqF,YAAY;QACtC,IAAIA,gBAAgBzF,GAAG0F,cAAc,CAACD,eAAe;YACnD,KAAK,MAAMtB,KAAKsB,aAAaE,QAAQ,CAAE;gBACrC,IAAI;oBAAC;oBAAoB;iBAAW,CAACnB,QAAQ,CAACL,EAAElD,IAAI,CAACC,OAAO,KAAK;oBAC/D2B,YAAY+C,IAAI,CAAC;wBACfvC,MAAMxD;wBACNyD,UAAUtD,GAAGiF,kBAAkB,CAACC,KAAK;wBACrC3B,MAAM4B,wBAAc,CAACC,uBAAuB;wBAC5C5B,aAAa,CAAC,aAAa,EAAEW,EAAElD,IAAI,CAACC,OAAO,GAAG,2CAA2C,CAAC;wBAC1FiC,OAAOgB,EAAElD,IAAI,CAACoE,QAAQ;wBACtB/C,QAAQ6B,EAAElD,IAAI,CAACqE,QAAQ;oBACzB;gBACF;YACF;QACF;QAEA,OAAOzC;IACT;IAEAgD,4CACElG,QAAgB,EAChBS,IAAgC;QAEhC,MAAMJ,KAAKC,IAAAA,YAAK;QAEhB,MAAMwF,eAAerF,KAAKqF,YAAY;QACtC,IAAIA,gBAAgBzF,GAAG0F,cAAc,CAACD,eAAe;YACnD,KAAK,MAAMtB,KAAKsB,aAAaE,QAAQ,CAAE;gBACrC,IAAIxB,EAAElD,IAAI,CAACC,OAAO,OAAO,YAAY;oBACnC,+CAA+C;oBAC/C,MAAM4E,cAAcC,IAAAA,qBAAc;oBAClC,IAAID,aAAa;wBACf,MAAME,SAASF,YAAYG,mBAAmB,CAAC9B,EAAElD,IAAI;wBACrD,IAAI+E,QAAQ;4BACV,MAAME,iBAAiBJ,YAAYK,gBAAgB,CAACH;4BACpD,IAAIE,kBAAkBA,eAAelF,YAAY,EAAE;gCACjD,MAAMD,cAAcmF,eAAelF,YAAY,CAAC,EAAE;gCAClD,IAAID,eAAef,GAAGoG,qBAAqB,CAACrF,cAAc;oCACxD,IAAIwB,QAAQxB,cAAc;oCAE1B,MAAMsF,sBACJtF,YAAYmB,aAAa,GAAGvC,QAAQ;oCACtC,MAAM2G,aAAaD,wBAAwB1G;oCAE3C,0DAA0D;oCAC1D,MAAMgD,MAAMxB,0BACVkF,qBACAtF;oCAEF,IAAI,CAAC4B,KAAK;oCAEV,MAAME,cAAcH,iBAClB2D,qBACA1D,KACA5B;oCAEF,IAAI8B,YAAYP,MAAM,EAAE;wCACtB,IAAIgE,YAAY;4CACd,OAAOzD;wCACT,OAAO;4CACL,OAAO;gDACL;oDACEQ,MAAMvD,IAAAA,gBAAS,EAACH;oDAChB2D,UAAUtD,GAAGiF,kBAAkB,CAACC,KAAK;oDACrC3B,MAAM4B,wBAAc,CAACC,uBAAuB;oDAC5C5B,aAAa,CAAC,0LAA0L,CAAC;oDACzML,OAAOgB,EAAElD,IAAI,CAACoE,QAAQ;oDACtB/C,QAAQ6B,EAAElD,IAAI,CAACqE,QAAQ;gDACzB;6CACD;wCACH;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO,EAAE;IACX;IAEAiB,2BACE5G,QAAgB,EAChBC,QAAgB,EAChB4G,SAAiB,EACjBC,aAAyC,EACzC5G,MAAc,EACd6G,WAAqC,EACrC3B,IAAkC;QAElC,MAAM3E,OAAOV,kBAAkBC,UAAUC;QACzC,IAAI,CAACQ,MAAM;QACX,IAAImC,QAAQnC,OAAO;QAEnB,0DAA0D;QAC1D,MAAMuC,MAAMxB,0BAA0BxB,UAAUS;QAChD,IAAIuC,QAAQF,WAAW;QAEvB,MAAMoB,SAASjE,YAAY+C,GAAG,CAAC,EAAE,GAAG/C,WAAWA,WAAW+C,GAAG,CAAC,EAAE;QAEhE,MAAMgE,UAAU1E,mBAAY,CAACa,eAAe,CAACyD,yBAAyB,CACpE5G,UACAkE,QACA2C,WACAC,eACA5G,QACA6G,aACA3B;QAEF,OAAO4B;IACT;IAEAC,wBAAuBjH,QAAgB,EAAEC,QAAgB;QACvD,MAAMQ,OAAOV,kBAAkBC,UAAUC;QACzC,IAAI,CAACQ,MAAM;QACX,IAAImC,QAAQnC,OAAO;QAEnB,0DAA0D;QAC1D,MAAMuC,MAAMxB,0BAA0BxB,UAAUS;QAChD,IAAIuC,QAAQF,WAAW;QAEvB,MAAMoB,SAASjE,YAAY+C,GAAG,CAAC,EAAE,GAAG/C,WAAWA,WAAW+C,GAAG,CAAC,EAAE;QAChE,MAAMkE,UAAU5E,mBAAY,CAACa,eAAe,CAAC8D,sBAAsB,CACjEjH,UACAkE;QAEF,OAAOgD;IACT;IAEAC,2BAA0BnH,QAAgB,EAAEC,QAAgB;QAC1D,MAAMQ,OAAOV,kBAAkBC,UAAUC;QACzC,IAAI,CAACQ,MAAM;QACX,IAAImC,QAAQnC,OAAO;QACnB,IAAI,CAACC,IAAAA,2BAAoB,EAACT,UAAUQ,OAAO;QAC3C,0DAA0D;QAC1D,MAAMuC,MAAMxB,0BAA0BxB,UAAUS;QAChD,IAAIuC,QAAQF,WAAW;QACvB,MAAMoB,SAASjE,YAAY+C,GAAG,CAAC,EAAE,GAAG/C,WAAWA,WAAW+C,GAAG,CAAC,EAAE;QAEhE,MAAMoE,6BACJ9E,mBAAY,CAACa,eAAe,CAACgE,yBAAyB,CAACnH,UAAUkE;QAEnE,IAAIkD,4BAA4B;YAC9B,6CAA6C;YAC7C,IAAIA,2BAA2BC,QAAQ,CAAC7D,KAAK,GAAGR,GAAG,CAAC,EAAE,EAAE;gBACtDoE,2BAA2BC,QAAQ,CAAC7D,KAAK,IAAIR,GAAG,CAAC,EAAE;YACrD;QACF;QACA,OAAOoE;IACT;AACF;MAEA,WAAetD"}