!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.BrowserFS=e():t.BrowserFS=e()}(this,(function(){return function(t){var e={};function n(r){if(e[r])return e[r].exports;var i=e[r]={i:r,l:!1,exports:{}};return t[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)n.d(r,i,function(e){return t[e]}.bind(null,i));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=19)}([function(t,e,n){"use strict";var r=n(7),i=Object.keys||function(t){var e=[];for(var n in t)e.push(n);return e};t.exports=h;var o=Object.create(n(5));o.inherits=n(1);var s=n(14),a=n(11);o.inherits(h,s);for(var c=i(a.prototype),u=0;u<c.length;u++){var f=c[u];h.prototype[f]||(h.prototype[f]=a.prototype[f])}function h(t){if(!(this instanceof h))return new h(t);s.call(this,t),a.call(this,t),t&&!1===t.readable&&(this.readable=!1),t&&!1===t.writable&&(this.writable=!1),this.allowHalfOpen=!0,t&&!1===t.allowHalfOpen&&(this.allowHalfOpen=!1),this.once("end",l)}function l(){this.allowHalfOpen||this._writableState.ended||r.nextTick(p,this)}function p(t){t.end()}Object.defineProperty(h.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(h.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(t){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=t,this._writableState.destroyed=t)}}),h.prototype._destroy=function(t,e){this.push(null),this.end(),r.nextTick(e,t)}},function(t,e){"function"==typeof Object.create?t.exports=function(t,e){e&&(t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:t.exports=function(t,e){if(e){t.super_=e;var n=function(){};n.prototype=e.prototype,t.prototype=new n,t.prototype.constructor=t}}},function(t,e,n){"use strict";
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */var r=n(20),i=n(21);e.Buffer=s,e.SlowBuffer=function(t){+t!=t&&(t=0);return s.alloc(+t)},e.INSPECT_MAX_BYTES=50;function o(t){if(t>**********)throw new RangeError("Invalid typed array length");var e=new Uint8Array(t);return e.__proto__=s.prototype,e}function s(t,e,n){if("number"==typeof t){if("string"==typeof e)throw new Error("If encoding is specified then the first argument must be a string");return u(t)}return a(t,e,n)}function a(t,e,n){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return j(t)||t&&j(t.buffer)?function(t,e,n){if(e<0||t.byteLength<e)throw new RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(n||0))throw new RangeError('"length" is outside of buffer bounds');var r;r=void 0===e&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,e):new Uint8Array(t,e,n);return r.__proto__=s.prototype,r}(t,e,n):"string"==typeof t?function(t,e){"string"==typeof e&&""!==e||(e="utf8");if(!s.isEncoding(e))throw new TypeError("Unknown encoding: "+e);var n=0|l(t,e),r=o(n),i=r.write(t,e);i!==n&&(r=r.slice(0,i));return r}(t,e):function(t){if(s.isBuffer(t)){var e=0|h(t.length),n=o(e);return 0===n.length||t.copy(n,0,0,e),n}if(t){if(ArrayBuffer.isView(t)||"length"in t)return"number"!=typeof t.length||B(t.length)?o(0):f(t);if("Buffer"===t.type&&Array.isArray(t.data))return f(t.data)}throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object.")}(t)}function c(t){if("number"!=typeof t)throw new TypeError('"size" argument must be of type number');if(t<0)throw new RangeError('"size" argument must not be negative')}function u(t){return c(t),o(t<0?0:0|h(t))}function f(t){for(var e=t.length<0?0:0|h(t.length),n=o(e),r=0;r<e;r+=1)n[r]=255&t[r];return n}function h(t){if(t>=**********)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+**********..toString(16)+" bytes");return 0|t}function l(t,e){if(s.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||j(t))return t.byteLength;"string"!=typeof t&&(t=""+t);var n=t.length;if(0===n)return 0;for(var r=!1;;)switch(e){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return C(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return M(t).length;default:if(r)return C(t).length;e=(""+e).toLowerCase(),r=!0}}function p(t,e,n){var r=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if((n>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return O(this,e,n);case"utf8":case"utf-8":return k(this,e,n);case"ascii":return I(this,e,n);case"latin1":case"binary":return N(this,e,n);case"base64":return S(this,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return T(this,e,n);default:if(r)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),r=!0}}function d(t,e,n){var r=t[e];t[e]=t[n],t[n]=r}function y(t,e,n,r,i){if(0===t.length)return-1;if("string"==typeof n?(r=n,n=0):n>**********?n=**********:n<-2147483648&&(n=-2147483648),B(n=+n)&&(n=i?0:t.length-1),n<0&&(n=t.length+n),n>=t.length){if(i)return-1;n=t.length-1}else if(n<0){if(!i)return-1;n=0}if("string"==typeof e&&(e=s.from(e,r)),s.isBuffer(e))return 0===e.length?-1:g(t,e,n,r,i);if("number"==typeof e)return e&=255,"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,e,n):Uint8Array.prototype.lastIndexOf.call(t,e,n):g(t,[e],n,r,i);throw new TypeError("val must be string, number or Buffer")}function g(t,e,n,r,i){var o,s=1,a=t.length,c=e.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(t.length<2||e.length<2)return-1;s=2,a/=2,c/=2,n/=2}function u(t,e){return 1===s?t[e]:t.readUInt16BE(e*s)}if(i){var f=-1;for(o=n;o<a;o++)if(u(t,o)===u(e,-1===f?0:o-f)){if(-1===f&&(f=o),o-f+1===c)return f*s}else-1!==f&&(o-=o-f),f=-1}else for(n+c>a&&(n=a-c),o=n;o>=0;o--){for(var h=!0,l=0;l<c;l++)if(u(t,o+l)!==u(e,l)){h=!1;break}if(h)return o}return-1}function w(t,e,n,r){n=Number(n)||0;var i=t.length-n;r?(r=Number(r))>i&&(r=i):r=i;var o=e.length;r>o/2&&(r=o/2);for(var s=0;s<r;++s){var a=parseInt(e.substr(2*s,2),16);if(B(a))return s;t[n+s]=a}return s}function m(t,e,n,r){return U(C(e,t.length-n),t,n,r)}function v(t,e,n,r){return U(function(t){for(var e=[],n=0;n<t.length;++n)e.push(255&t.charCodeAt(n));return e}(e),t,n,r)}function _(t,e,n,r){return v(t,e,n,r)}function b(t,e,n,r){return U(M(e),t,n,r)}function E(t,e,n,r){return U(function(t,e){for(var n,r,i,o=[],s=0;s<t.length&&!((e-=2)<0);++s)n=t.charCodeAt(s),r=n>>8,i=n%256,o.push(i),o.push(r);return o}(e,t.length-n),t,n,r)}function S(t,e,n){return 0===e&&n===t.length?r.fromByteArray(t):r.fromByteArray(t.slice(e,n))}function k(t,e,n){n=Math.min(t.length,n);for(var r=[],i=e;i<n;){var o,s,a,c,u=t[i],f=null,h=u>239?4:u>223?3:u>191?2:1;if(i+h<=n)switch(h){case 1:u<128&&(f=u);break;case 2:128==(192&(o=t[i+1]))&&(c=(31&u)<<6|63&o)>127&&(f=c);break;case 3:o=t[i+1],s=t[i+2],128==(192&o)&&128==(192&s)&&(c=(15&u)<<12|(63&o)<<6|63&s)>2047&&(c<55296||c>57343)&&(f=c);break;case 4:o=t[i+1],s=t[i+2],a=t[i+3],128==(192&o)&&128==(192&s)&&128==(192&a)&&(c=(15&u)<<18|(63&o)<<12|(63&s)<<6|63&a)>65535&&c<1114112&&(f=c)}null===f?(f=65533,h=1):f>65535&&(f-=65536,r.push(f>>>10&1023|55296),f=56320|1023&f),r.push(f),i+=h}return function(t){var e=t.length;if(e<=4096)return String.fromCharCode.apply(String,t);var n="",r=0;for(;r<e;)n+=String.fromCharCode.apply(String,t.slice(r,r+=4096));return n}(r)}e.kMaxLength=**********,s.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()}catch(t){return!1}}(),s.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(s.prototype,"parent",{get:function(){if(this instanceof s)return this.buffer}}),Object.defineProperty(s.prototype,"offset",{get:function(){if(this instanceof s)return this.byteOffset}}),"undefined"!=typeof Symbol&&Symbol.species&&s[Symbol.species]===s&&Object.defineProperty(s,Symbol.species,{value:null,configurable:!0,enumerable:!1,writable:!1}),s.poolSize=8192,s.from=function(t,e,n){return a(t,e,n)},s.prototype.__proto__=Uint8Array.prototype,s.__proto__=Uint8Array,s.alloc=function(t,e,n){return function(t,e,n){return c(t),t<=0?o(t):void 0!==e?"string"==typeof n?o(t).fill(e,n):o(t).fill(e):o(t)}(t,e,n)},s.allocUnsafe=function(t){return u(t)},s.allocUnsafeSlow=function(t){return u(t)},s.isBuffer=function(t){return null!=t&&!0===t._isBuffer},s.compare=function(t,e){if(!s.isBuffer(t)||!s.isBuffer(e))throw new TypeError("Arguments must be Buffers");if(t===e)return 0;for(var n=t.length,r=e.length,i=0,o=Math.min(n,r);i<o;++i)if(t[i]!==e[i]){n=t[i],r=e[i];break}return n<r?-1:r<n?1:0},s.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},s.concat=function(t,e){if(!Array.isArray(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return s.alloc(0);var n;if(void 0===e)for(e=0,n=0;n<t.length;++n)e+=t[n].length;var r=s.allocUnsafe(e),i=0;for(n=0;n<t.length;++n){var o=t[n];if(ArrayBuffer.isView(o)&&(o=s.from(o)),!s.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(r,i),i+=o.length}return r},s.byteLength=l,s.prototype._isBuffer=!0,s.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)d(this,e,e+1);return this},s.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)d(this,e,e+3),d(this,e+1,e+2);return this},s.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)d(this,e,e+7),d(this,e+1,e+6),d(this,e+2,e+5),d(this,e+3,e+4);return this},s.prototype.toString=function(){var t=this.length;return 0===t?"":0===arguments.length?k(this,0,t):p.apply(this,arguments)},s.prototype.toLocaleString=s.prototype.toString,s.prototype.equals=function(t){if(!s.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===s.compare(this,t)},s.prototype.inspect=function(){var t="",n=e.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,n).match(/.{2}/g).join(" "),this.length>n&&(t+=" ... ")),"<Buffer "+t+">"},s.prototype.compare=function(t,e,n,r,i){if(!s.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===e&&(e=0),void 0===n&&(n=t?t.length:0),void 0===r&&(r=0),void 0===i&&(i=this.length),e<0||n>t.length||r<0||i>this.length)throw new RangeError("out of range index");if(r>=i&&e>=n)return 0;if(r>=i)return-1;if(e>=n)return 1;if(this===t)return 0;for(var o=(i>>>=0)-(r>>>=0),a=(n>>>=0)-(e>>>=0),c=Math.min(o,a),u=this.slice(r,i),f=t.slice(e,n),h=0;h<c;++h)if(u[h]!==f[h]){o=u[h],a=f[h];break}return o<a?-1:a<o?1:0},s.prototype.includes=function(t,e,n){return-1!==this.indexOf(t,e,n)},s.prototype.indexOf=function(t,e,n){return y(this,t,e,n,!0)},s.prototype.lastIndexOf=function(t,e,n){return y(this,t,e,n,!1)},s.prototype.write=function(t,e,n,r){if(void 0===e)r="utf8",n=this.length,e=0;else if(void 0===n&&"string"==typeof e)r=e,n=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e>>>=0,isFinite(n)?(n>>>=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var i=this.length-e;if((void 0===n||n>i)&&(n=i),t.length>0&&(n<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var o=!1;;)switch(r){case"hex":return w(this,t,e,n);case"utf8":case"utf-8":return m(this,t,e,n);case"ascii":return v(this,t,e,n);case"latin1":case"binary":return _(this,t,e,n);case"base64":return b(this,t,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return E(this,t,e,n);default:if(o)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),o=!0}},s.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function I(t,e,n){var r="";n=Math.min(t.length,n);for(var i=e;i<n;++i)r+=String.fromCharCode(127&t[i]);return r}function N(t,e,n){var r="";n=Math.min(t.length,n);for(var i=e;i<n;++i)r+=String.fromCharCode(t[i]);return r}function O(t,e,n){var r=t.length;(!e||e<0)&&(e=0),(!n||n<0||n>r)&&(n=r);for(var i="",o=e;o<n;++o)i+=P(t[o]);return i}function T(t,e,n){for(var r=t.slice(e,n),i="",o=0;o<r.length;o+=2)i+=String.fromCharCode(r[o]+256*r[o+1]);return i}function F(t,e,n){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>n)throw new RangeError("Trying to access beyond buffer length")}function x(t,e,n,r,i,o){if(!s.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw new RangeError('"value" argument is out of bounds');if(n+r>t.length)throw new RangeError("Index out of range")}function R(t,e,n,r,i,o){if(n+r>t.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function A(t,e,n,r,o){return e=+e,n>>>=0,o||R(t,0,n,4),i.write(t,e,n,r,23,4),n+4}function L(t,e,n,r,o){return e=+e,n>>>=0,o||R(t,0,n,8),i.write(t,e,n,r,52,8),n+8}s.prototype.slice=function(t,e){var n=this.length;(t=~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),(e=void 0===e?n:~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),e<t&&(e=t);var r=this.subarray(t,e);return r.__proto__=s.prototype,r},s.prototype.readUIntLE=function(t,e,n){t>>>=0,e>>>=0,n||F(t,e,this.length);for(var r=this[t],i=1,o=0;++o<e&&(i*=256);)r+=this[t+o]*i;return r},s.prototype.readUIntBE=function(t,e,n){t>>>=0,e>>>=0,n||F(t,e,this.length);for(var r=this[t+--e],i=1;e>0&&(i*=256);)r+=this[t+--e]*i;return r},s.prototype.readUInt8=function(t,e){return t>>>=0,e||F(t,1,this.length),this[t]},s.prototype.readUInt16LE=function(t,e){return t>>>=0,e||F(t,2,this.length),this[t]|this[t+1]<<8},s.prototype.readUInt16BE=function(t,e){return t>>>=0,e||F(t,2,this.length),this[t]<<8|this[t+1]},s.prototype.readUInt32LE=function(t,e){return t>>>=0,e||F(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},s.prototype.readUInt32BE=function(t,e){return t>>>=0,e||F(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},s.prototype.readIntLE=function(t,e,n){t>>>=0,e>>>=0,n||F(t,e,this.length);for(var r=this[t],i=1,o=0;++o<e&&(i*=256);)r+=this[t+o]*i;return r>=(i*=128)&&(r-=Math.pow(2,8*e)),r},s.prototype.readIntBE=function(t,e,n){t>>>=0,e>>>=0,n||F(t,e,this.length);for(var r=e,i=1,o=this[t+--r];r>0&&(i*=256);)o+=this[t+--r]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*e)),o},s.prototype.readInt8=function(t,e){return t>>>=0,e||F(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},s.prototype.readInt16LE=function(t,e){t>>>=0,e||F(t,2,this.length);var n=this[t]|this[t+1]<<8;return 32768&n?4294901760|n:n},s.prototype.readInt16BE=function(t,e){t>>>=0,e||F(t,2,this.length);var n=this[t+1]|this[t]<<8;return 32768&n?4294901760|n:n},s.prototype.readInt32LE=function(t,e){return t>>>=0,e||F(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},s.prototype.readInt32BE=function(t,e){return t>>>=0,e||F(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},s.prototype.readFloatLE=function(t,e){return t>>>=0,e||F(t,4,this.length),i.read(this,t,!0,23,4)},s.prototype.readFloatBE=function(t,e){return t>>>=0,e||F(t,4,this.length),i.read(this,t,!1,23,4)},s.prototype.readDoubleLE=function(t,e){return t>>>=0,e||F(t,8,this.length),i.read(this,t,!0,52,8)},s.prototype.readDoubleBE=function(t,e){return t>>>=0,e||F(t,8,this.length),i.read(this,t,!1,52,8)},s.prototype.writeUIntLE=function(t,e,n,r){(t=+t,e>>>=0,n>>>=0,r)||x(this,t,e,n,Math.pow(2,8*n)-1,0);var i=1,o=0;for(this[e]=255&t;++o<n&&(i*=256);)this[e+o]=t/i&255;return e+n},s.prototype.writeUIntBE=function(t,e,n,r){(t=+t,e>>>=0,n>>>=0,r)||x(this,t,e,n,Math.pow(2,8*n)-1,0);var i=n-1,o=1;for(this[e+i]=255&t;--i>=0&&(o*=256);)this[e+i]=t/o&255;return e+n},s.prototype.writeUInt8=function(t,e,n){return t=+t,e>>>=0,n||x(this,t,e,1,255,0),this[e]=255&t,e+1},s.prototype.writeUInt16LE=function(t,e,n){return t=+t,e>>>=0,n||x(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},s.prototype.writeUInt16BE=function(t,e,n){return t=+t,e>>>=0,n||x(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},s.prototype.writeUInt32LE=function(t,e,n){return t=+t,e>>>=0,n||x(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},s.prototype.writeUInt32BE=function(t,e,n){return t=+t,e>>>=0,n||x(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},s.prototype.writeIntLE=function(t,e,n,r){if(t=+t,e>>>=0,!r){var i=Math.pow(2,8*n-1);x(this,t,e,n,i-1,-i)}var o=0,s=1,a=0;for(this[e]=255&t;++o<n&&(s*=256);)t<0&&0===a&&0!==this[e+o-1]&&(a=1),this[e+o]=(t/s>>0)-a&255;return e+n},s.prototype.writeIntBE=function(t,e,n,r){if(t=+t,e>>>=0,!r){var i=Math.pow(2,8*n-1);x(this,t,e,n,i-1,-i)}var o=n-1,s=1,a=0;for(this[e+o]=255&t;--o>=0&&(s*=256);)t<0&&0===a&&0!==this[e+o+1]&&(a=1),this[e+o]=(t/s>>0)-a&255;return e+n},s.prototype.writeInt8=function(t,e,n){return t=+t,e>>>=0,n||x(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},s.prototype.writeInt16LE=function(t,e,n){return t=+t,e>>>=0,n||x(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},s.prototype.writeInt16BE=function(t,e,n){return t=+t,e>>>=0,n||x(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},s.prototype.writeInt32LE=function(t,e,n){return t=+t,e>>>=0,n||x(this,t,e,4,**********,-2147483648),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},s.prototype.writeInt32BE=function(t,e,n){return t=+t,e>>>=0,n||x(this,t,e,4,**********,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},s.prototype.writeFloatLE=function(t,e,n){return A(this,t,e,!0,n)},s.prototype.writeFloatBE=function(t,e,n){return A(this,t,e,!1,n)},s.prototype.writeDoubleLE=function(t,e,n){return L(this,t,e,!0,n)},s.prototype.writeDoubleBE=function(t,e,n){return L(this,t,e,!1,n)},s.prototype.copy=function(t,e,n,r){if(!s.isBuffer(t))throw new TypeError("argument should be a Buffer");if(n||(n=0),r||0===r||(r=this.length),e>=t.length&&(e=t.length),e||(e=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-e<r-n&&(r=t.length-e+n);var i=r-n;if(this===t&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(e,n,r);else if(this===t&&n<e&&e<r)for(var o=i-1;o>=0;--o)t[o+e]=this[o+n];else Uint8Array.prototype.set.call(t,this.subarray(n,r),e);return i},s.prototype.fill=function(t,e,n,r){if("string"==typeof t){if("string"==typeof e?(r=e,e=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!s.isEncoding(r))throw new TypeError("Unknown encoding: "+r);if(1===t.length){var i=t.charCodeAt(0);("utf8"===r&&i<128||"latin1"===r)&&(t=i)}}else"number"==typeof t&&(t&=255);if(e<0||this.length<e||this.length<n)throw new RangeError("Out of range index");if(n<=e)return this;var o;if(e>>>=0,n=void 0===n?this.length:n>>>0,t||(t=0),"number"==typeof t)for(o=e;o<n;++o)this[o]=t;else{var a=s.isBuffer(t)?t:new s(t,r),c=a.length;if(0===c)throw new TypeError('The value "'+t+'" is invalid for argument "value"');for(o=0;o<n-e;++o)this[o+e]=a[o%c]}return this};var D=/[^+/0-9A-Za-z-_]/g;function P(t){return t<16?"0"+t.toString(16):t.toString(16)}function C(t,e){var n;e=e||1/0;for(var r=t.length,i=null,o=[],s=0;s<r;++s){if((n=t.charCodeAt(s))>55295&&n<57344){if(!i){if(n>56319){(e-=3)>-1&&o.push(239,191,189);continue}if(s+1===r){(e-=3)>-1&&o.push(239,191,189);continue}i=n;continue}if(n<56320){(e-=3)>-1&&o.push(239,191,189),i=n;continue}n=65536+(i-55296<<10|n-56320)}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,n<128){if((e-=1)<0)break;o.push(n)}else if(n<2048){if((e-=2)<0)break;o.push(n>>6|192,63&n|128)}else if(n<65536){if((e-=3)<0)break;o.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;o.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return o}function M(t){return r.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(D,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function U(t,e,n,r){for(var i=0;i<r&&!(i+n>=e.length||i>=t.length);++i)e[i+n]=t[i];return i}function j(t){return t instanceof ArrayBuffer||null!=t&&null!=t.constructor&&"ArrayBuffer"===t.constructor.name&&"number"==typeof t.byteLength}function B(t){return t!=t}},function(t,e,n){"use strict";var r=new(n(22)),i={};function o(t){i[t]||("function"==typeof r[t]?i[t]=function(){return r[t].apply(r,arguments)}:i[t]=r[t])}for(var s in r)o(s);i.initializeTTYs=function(){null===r.stdin&&(r.initializeTTYs(),i.stdin=r.stdin,i.stdout=r.stdout,i.stderr=r.stderr)},r.nextTick((function(){i.initializeTTYs()})),t.exports=i},function(t,e,n){"use strict";var r,i="object"==typeof Reflect?Reflect:null,o=i&&"function"==typeof i.apply?i.apply:function(t,e,n){return Function.prototype.apply.call(t,e,n)};r=i&&"function"==typeof i.ownKeys?i.ownKeys:Object.getOwnPropertySymbols?function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:function(t){return Object.getOwnPropertyNames(t)};var s=Number.isNaN||function(t){return t!=t};function a(){a.init.call(this)}t.exports=a,t.exports.once=function(t,e){return new Promise((function(n,r){function i(n){t.removeListener(e,o),r(n)}function o(){"function"==typeof t.removeListener&&t.removeListener("error",i),n([].slice.call(arguments))}w(t,e,o,{once:!0}),"error"!==e&&function(t,e,n){"function"==typeof t.on&&w(t,"error",e,n)}(t,i,{once:!0})}))},a.EventEmitter=a,a.prototype._events=void 0,a.prototype._eventsCount=0,a.prototype._maxListeners=void 0;var c=10;function u(t){if("function"!=typeof t)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t)}function f(t){return void 0===t._maxListeners?a.defaultMaxListeners:t._maxListeners}function h(t,e,n,r){var i,o,s,a;if(u(n),void 0===(o=t._events)?(o=t._events=Object.create(null),t._eventsCount=0):(void 0!==o.newListener&&(t.emit("newListener",e,n.listener?n.listener:n),o=t._events),s=o[e]),void 0===s)s=o[e]=n,++t._eventsCount;else if("function"==typeof s?s=o[e]=r?[n,s]:[s,n]:r?s.unshift(n):s.push(n),(i=f(t))>0&&s.length>i&&!s.warned){s.warned=!0;var c=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(e)+" listeners added. Use emitter.setMaxListeners() to increase limit");c.name="MaxListenersExceededWarning",c.emitter=t,c.type=e,c.count=s.length,a=c,console&&console.warn&&console.warn(a)}return t}function l(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function p(t,e,n){var r={fired:!1,wrapFn:void 0,target:t,type:e,listener:n},i=l.bind(r);return i.listener=n,r.wrapFn=i,i}function d(t,e,n){var r=t._events;if(void 0===r)return[];var i=r[e];return void 0===i?[]:"function"==typeof i?n?[i.listener||i]:[i]:n?function(t){for(var e=new Array(t.length),n=0;n<e.length;++n)e[n]=t[n].listener||t[n];return e}(i):g(i,i.length)}function y(t){var e=this._events;if(void 0!==e){var n=e[t];if("function"==typeof n)return 1;if(void 0!==n)return n.length}return 0}function g(t,e){for(var n=new Array(e),r=0;r<e;++r)n[r]=t[r];return n}function w(t,e,n,r){if("function"==typeof t.on)r.once?t.once(e,n):t.on(e,n);else{if("function"!=typeof t.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof t);t.addEventListener(e,(function i(o){r.once&&t.removeEventListener(e,i),n(o)}))}}Object.defineProperty(a,"defaultMaxListeners",{enumerable:!0,get:function(){return c},set:function(t){if("number"!=typeof t||t<0||s(t))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+t+".");c=t}}),a.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},a.prototype.setMaxListeners=function(t){if("number"!=typeof t||t<0||s(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this},a.prototype.getMaxListeners=function(){return f(this)},a.prototype.emit=function(t){for(var e=[],n=1;n<arguments.length;n++)e.push(arguments[n]);var r="error"===t,i=this._events;if(void 0!==i)r=r&&void 0===i.error;else if(!r)return!1;if(r){var s;if(e.length>0&&(s=e[0]),s instanceof Error)throw s;var a=new Error("Unhandled error."+(s?" ("+s.message+")":""));throw a.context=s,a}var c=i[t];if(void 0===c)return!1;if("function"==typeof c)o(c,this,e);else{var u=c.length,f=g(c,u);for(n=0;n<u;++n)o(f[n],this,e)}return!0},a.prototype.addListener=function(t,e){return h(this,t,e,!1)},a.prototype.on=a.prototype.addListener,a.prototype.prependListener=function(t,e){return h(this,t,e,!0)},a.prototype.once=function(t,e){return u(e),this.on(t,p(this,t,e)),this},a.prototype.prependOnceListener=function(t,e){return u(e),this.prependListener(t,p(this,t,e)),this},a.prototype.removeListener=function(t,e){var n,r,i,o,s;if(u(e),void 0===(r=this._events))return this;if(void 0===(n=r[t]))return this;if(n===e||n.listener===e)0==--this._eventsCount?this._events=Object.create(null):(delete r[t],r.removeListener&&this.emit("removeListener",t,n.listener||e));else if("function"!=typeof n){for(i=-1,o=n.length-1;o>=0;o--)if(n[o]===e||n[o].listener===e){s=n[o].listener,i=o;break}if(i<0)return this;0===i?n.shift():function(t,e){for(;e+1<t.length;e++)t[e]=t[e+1];t.pop()}(n,i),1===n.length&&(r[t]=n[0]),void 0!==r.removeListener&&this.emit("removeListener",t,s||e)}return this},a.prototype.off=a.prototype.removeListener,a.prototype.removeAllListeners=function(t){var e,n,r;if(void 0===(n=this._events))return this;if(void 0===n.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==n[t]&&(0==--this._eventsCount?this._events=Object.create(null):delete n[t]),this;if(0===arguments.length){var i,o=Object.keys(n);for(r=0;r<o.length;++r)"removeListener"!==(i=o[r])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(e=n[t]))this.removeListener(t,e);else if(void 0!==e)for(r=e.length-1;r>=0;r--)this.removeListener(t,e[r]);return this},a.prototype.listeners=function(t){return d(this,t,!0)},a.prototype.rawListeners=function(t){return d(this,t,!1)},a.listenerCount=function(t,e){return"function"==typeof t.listenerCount?t.listenerCount(e):y.call(t,e)},a.prototype.listenerCount=y,a.prototype.eventNames=function(){return this._eventsCount>0?r(this._events):[]}},function(t,e,n){function r(t){return Object.prototype.toString.call(t)}e.isArray=function(t){return Array.isArray?Array.isArray(t):"[object Array]"===r(t)},e.isBoolean=function(t){return"boolean"==typeof t},e.isNull=function(t){return null===t},e.isNullOrUndefined=function(t){return null==t},e.isNumber=function(t){return"number"==typeof t},e.isString=function(t){return"string"==typeof t},e.isSymbol=function(t){return"symbol"==typeof t},e.isUndefined=function(t){return void 0===t},e.isRegExp=function(t){return"[object RegExp]"===r(t)},e.isObject=function(t){return"object"==typeof t&&null!==t},e.isDate=function(t){return"[object Date]"===r(t)},e.isError=function(t){return"[object Error]"===r(t)||t instanceof Error},e.isFunction=function(t){return"function"==typeof t},e.isPrimitive=function(t){return null===t||"boolean"==typeof t||"number"==typeof t||"string"==typeof t||"symbol"==typeof t||void 0===t},e.isBuffer=n(2).Buffer.isBuffer},function(t,e){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(t){"object"==typeof window&&(n=window)}t.exports=n},function(t,e,n){"use strict";(function(e){void 0===e||!e.version||0===e.version.indexOf("v0.")||0===e.version.indexOf("v1.")&&0!==e.version.indexOf("v1.8.")?t.exports={nextTick:function(t,n,r,i){if("function"!=typeof t)throw new TypeError('"callback" argument must be a function');var o,s,a=arguments.length;switch(a){case 0:case 1:return e.nextTick(t);case 2:return e.nextTick((function(){t.call(null,n)}));case 3:return e.nextTick((function(){t.call(null,n,r)}));case 4:return e.nextTick((function(){t.call(null,n,r,i)}));default:for(o=new Array(a-1),s=0;s<o.length;)o[s++]=arguments[s];return e.nextTick((function(){t.apply(null,o)}))}}}:t.exports=e}).call(this,n(3))},function(t,e,n){"use strict";var r="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;function i(t,e){return Object.prototype.hasOwnProperty.call(t,e)}e.assign=function(t){for(var e=Array.prototype.slice.call(arguments,1);e.length;){var n=e.shift();if(n){if("object"!=typeof n)throw new TypeError(n+"must be non-object");for(var r in n)i(n,r)&&(t[r]=n[r])}}return t},e.shrinkBuf=function(t,e){return t.length===e?t:t.subarray?t.subarray(0,e):(t.length=e,t)};var o={arraySet:function(t,e,n,r,i){if(e.subarray&&t.subarray)t.set(e.subarray(n,n+r),i);else for(var o=0;o<r;o++)t[i+o]=e[n+o]},flattenChunks:function(t){var e,n,r,i,o,s;for(r=0,e=0,n=t.length;e<n;e++)r+=t[e].length;for(s=new Uint8Array(r),i=0,e=0,n=t.length;e<n;e++)o=t[e],s.set(o,i),i+=o.length;return s}},s={arraySet:function(t,e,n,r,i){for(var o=0;o<r;o++)t[i+o]=e[n+o]},flattenChunks:function(t){return[].concat.apply([],t)}};e.setTyped=function(t){t?(e.Buf8=Uint8Array,e.Buf16=Uint16Array,e.Buf32=Int32Array,e.assign(e,o)):(e.Buf8=Array,e.Buf16=Array,e.Buf32=Array,e.assign(e,s))},e.setTyped(r)},function(t,e,n){(e=t.exports=n(14)).Stream=e,e.Readable=e,e.Writable=n(11),e.Duplex=n(0),e.Transform=n(18),e.PassThrough=n(31)},function(t,e,n){var r=n(2),i=r.Buffer;function o(t,e){for(var n in t)e[n]=t[n]}function s(t,e,n){return i(t,e,n)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?t.exports=r:(o(r,e),e.Buffer=s),o(i,s),s.from=function(t,e,n){if("number"==typeof t)throw new TypeError("Argument must not be a number");return i(t,e,n)},s.alloc=function(t,e,n){if("number"!=typeof t)throw new TypeError("Argument must be a number");var r=i(t);return void 0!==e?"string"==typeof n?r.fill(e,n):r.fill(e):r.fill(0),r},s.allocUnsafe=function(t){if("number"!=typeof t)throw new TypeError("Argument must be a number");return i(t)},s.allocUnsafeSlow=function(t){if("number"!=typeof t)throw new TypeError("Argument must be a number");return r.SlowBuffer(t)}},function(t,e,n){"use strict";(function(e,r){var i=n(7);function o(t){var e=this;this.next=null,this.entry=null,this.finish=function(){!function(t,e,n){var r=t.entry;t.entry=null;for(;r;){var i=r.callback;e.pendingcb--,i(n),r=r.next}e.corkedRequestsFree.next=t}(e,t)}}t.exports=w;var s,a=!e.browser&&["v0.10","v0.9."].indexOf(e.version.slice(0,5))>-1?setImmediate:i.nextTick;w.WritableState=g;var c=Object.create(n(5));c.inherits=n(1);var u={deprecate:n(29)},f=n(15),h=n(10).Buffer,l=(void 0!==r?r:"undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).Uint8Array||function(){};var p,d=n(16);function y(){}function g(t,e){s=s||n(0),t=t||{};var r=e instanceof s;this.objectMode=!!t.objectMode,r&&(this.objectMode=this.objectMode||!!t.writableObjectMode);var c=t.highWaterMark,u=t.writableHighWaterMark,f=this.objectMode?16:16384;this.highWaterMark=c||0===c?c:r&&(u||0===u)?u:f,this.highWaterMark=Math.floor(this.highWaterMark),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var h=!1===t.decodeStrings;this.decodeStrings=!h,this.defaultEncoding=t.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(t){!function(t,e){var n=t._writableState,r=n.sync,o=n.writecb;if(function(t){t.writing=!1,t.writecb=null,t.length-=t.writelen,t.writelen=0}(n),e)!function(t,e,n,r,o){--e.pendingcb,n?(i.nextTick(o,r),i.nextTick(S,t,e),t._writableState.errorEmitted=!0,t.emit("error",r)):(o(r),t._writableState.errorEmitted=!0,t.emit("error",r),S(t,e))}(t,n,r,e,o);else{var s=b(n);s||n.corked||n.bufferProcessing||!n.bufferedRequest||_(t,n),r?a(v,t,n,s,o):v(t,n,s,o)}}(e,t)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new o(this)}function w(t){if(s=s||n(0),!(p.call(w,this)||this instanceof s))return new w(t);this._writableState=new g(t,this),this.writable=!0,t&&("function"==typeof t.write&&(this._write=t.write),"function"==typeof t.writev&&(this._writev=t.writev),"function"==typeof t.destroy&&(this._destroy=t.destroy),"function"==typeof t.final&&(this._final=t.final)),f.call(this)}function m(t,e,n,r,i,o,s){e.writelen=r,e.writecb=s,e.writing=!0,e.sync=!0,n?t._writev(i,e.onwrite):t._write(i,o,e.onwrite),e.sync=!1}function v(t,e,n,r){n||function(t,e){0===e.length&&e.needDrain&&(e.needDrain=!1,t.emit("drain"))}(t,e),e.pendingcb--,r(),S(t,e)}function _(t,e){e.bufferProcessing=!0;var n=e.bufferedRequest;if(t._writev&&n&&n.next){var r=e.bufferedRequestCount,i=new Array(r),s=e.corkedRequestsFree;s.entry=n;for(var a=0,c=!0;n;)i[a]=n,n.isBuf||(c=!1),n=n.next,a+=1;i.allBuffers=c,m(t,e,!0,e.length,i,"",s.finish),e.pendingcb++,e.lastBufferedRequest=null,s.next?(e.corkedRequestsFree=s.next,s.next=null):e.corkedRequestsFree=new o(e),e.bufferedRequestCount=0}else{for(;n;){var u=n.chunk,f=n.encoding,h=n.callback;if(m(t,e,!1,e.objectMode?1:u.length,u,f,h),n=n.next,e.bufferedRequestCount--,e.writing)break}null===n&&(e.lastBufferedRequest=null)}e.bufferedRequest=n,e.bufferProcessing=!1}function b(t){return t.ending&&0===t.length&&null===t.bufferedRequest&&!t.finished&&!t.writing}function E(t,e){t._final((function(n){e.pendingcb--,n&&t.emit("error",n),e.prefinished=!0,t.emit("prefinish"),S(t,e)}))}function S(t,e){var n=b(e);return n&&(!function(t,e){e.prefinished||e.finalCalled||("function"==typeof t._final?(e.pendingcb++,e.finalCalled=!0,i.nextTick(E,t,e)):(e.prefinished=!0,t.emit("prefinish")))}(t,e),0===e.pendingcb&&(e.finished=!0,t.emit("finish"))),n}c.inherits(w,f),g.prototype.getBuffer=function(){for(var t=this.bufferedRequest,e=[];t;)e.push(t),t=t.next;return e},function(){try{Object.defineProperty(g.prototype,"buffer",{get:u.deprecate((function(){return this.getBuffer()}),"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(t){}}(),"function"==typeof Symbol&&Symbol.hasInstance&&"function"==typeof Function.prototype[Symbol.hasInstance]?(p=Function.prototype[Symbol.hasInstance],Object.defineProperty(w,Symbol.hasInstance,{value:function(t){return!!p.call(this,t)||this===w&&(t&&t._writableState instanceof g)}})):p=function(t){return t instanceof this},w.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe, not readable"))},w.prototype.write=function(t,e,n){var r,o=this._writableState,s=!1,a=!o.objectMode&&(r=t,h.isBuffer(r)||r instanceof l);return a&&!h.isBuffer(t)&&(t=function(t){return h.from(t)}(t)),"function"==typeof e&&(n=e,e=null),a?e="buffer":e||(e=o.defaultEncoding),"function"!=typeof n&&(n=y),o.ended?function(t,e){var n=new Error("write after end");t.emit("error",n),i.nextTick(e,n)}(this,n):(a||function(t,e,n,r){var o=!0,s=!1;return null===n?s=new TypeError("May not write null values to stream"):"string"==typeof n||void 0===n||e.objectMode||(s=new TypeError("Invalid non-string/buffer chunk")),s&&(t.emit("error",s),i.nextTick(r,s),o=!1),o}(this,o,t,n))&&(o.pendingcb++,s=function(t,e,n,r,i,o){if(!n){var s=function(t,e,n){t.objectMode||!1===t.decodeStrings||"string"!=typeof e||(e=h.from(e,n));return e}(e,r,i);r!==s&&(n=!0,i="buffer",r=s)}var a=e.objectMode?1:r.length;e.length+=a;var c=e.length<e.highWaterMark;c||(e.needDrain=!0);if(e.writing||e.corked){var u=e.lastBufferedRequest;e.lastBufferedRequest={chunk:r,encoding:i,isBuf:n,callback:o,next:null},u?u.next=e.lastBufferedRequest:e.bufferedRequest=e.lastBufferedRequest,e.bufferedRequestCount+=1}else m(t,e,!1,a,r,i,o);return c}(this,o,a,t,e,n)),s},w.prototype.cork=function(){this._writableState.corked++},w.prototype.uncork=function(){var t=this._writableState;t.corked&&(t.corked--,t.writing||t.corked||t.bufferProcessing||!t.bufferedRequest||_(this,t))},w.prototype.setDefaultEncoding=function(t){if("string"==typeof t&&(t=t.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((t+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+t);return this._writableState.defaultEncoding=t,this},Object.defineProperty(w.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),w.prototype._write=function(t,e,n){n(new Error("_write() is not implemented"))},w.prototype._writev=null,w.prototype.end=function(t,e,n){var r=this._writableState;"function"==typeof t?(n=t,t=null,e=null):"function"==typeof e&&(n=e,e=null),null!=t&&this.write(t,e),r.corked&&(r.corked=1,this.uncork()),r.ending||function(t,e,n){e.ending=!0,S(t,e),n&&(e.finished?i.nextTick(n):t.once("finish",n));e.ended=!0,t.writable=!1}(this,r,n)},Object.defineProperty(w.prototype,"destroyed",{get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(t){this._writableState&&(this._writableState.destroyed=t)}}),w.prototype.destroy=d.destroy,w.prototype._undestroy=d.undestroy,w.prototype._destroy=function(t,e){this.end(),e(t)}}).call(this,n(3),n(6))},function(t,e,n){t.exports=n(2).Buffer},function(t,e,n){"use strict";(function(e){var n=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/;var r=function(){function t(){}return t.normalize=function(e){""===e&&(e=".");for(var n=e.charAt(0)===t.sep,r=(e=t._removeDuplicateSeps(e)).split(t.sep),i=[],o=0;o<r.length;o++){var s=r[o];"."!==s&&(".."===s&&(n||!n&&i.length>0&&".."!==i[0])?i.pop():i.push(s))}if(!n&&i.length<2)switch(i.length){case 1:""===i[0]&&i.unshift(".");break;default:i.push(".")}return e=i.join(t.sep),n&&e.charAt(0)!==t.sep&&(e=t.sep+e),e},t.join=function(){for(var e=[],n=0;n<arguments.length;n++)e[n-0]=arguments[n];for(var r=[],i=0;i<e.length;i++){var o=e[i];if("string"!=typeof o)throw new TypeError("Invalid argument type to path.join: "+typeof o);""!==o&&r.push(o)}return t.normalize(r.join(t.sep))},t.resolve=function(){for(var n=[],r=0;r<arguments.length;r++)n[r-0]=arguments[r];for(var i=[],o=0;o<n.length;o++){var s=n[o];if("string"!=typeof s)throw new TypeError("Invalid argument type to path.join: "+typeof s);""!==s&&(s.charAt(0)===t.sep&&(i=[]),i.push(s))}var a=t.normalize(i.join(t.sep));if(a.length>1&&a.charAt(a.length-1)===t.sep)return a.substr(0,a.length-1);if(a.charAt(0)!==t.sep){"."!==a.charAt(0)||1!==a.length&&a.charAt(1)!==t.sep||(a=1===a.length?"":a.substr(2));var c=e.cwd();a=""!==a?this.normalize(c+("/"!==c?t.sep:"")+a):c}return a},t.relative=function(e,n){var r;e=t.resolve(e),n=t.resolve(n);var i=e.split(t.sep),o=n.split(t.sep);o.shift(),i.shift();var s=0,a=[];for(r=0;r<i.length;r++){if(i[r]!==o[r]){s=i.length-r;break}}a=o.slice(r),1===i.length&&""===i[0]&&(s=0),s>i.length&&(s=i.length);var c="";for(r=0;r<s;r++)c+="../";return(c+=a.join(t.sep)).length>1&&c.charAt(c.length-1)===t.sep&&(c=c.substr(0,c.length-1)),c},t.dirname=function(e){var n=(e=t._removeDuplicateSeps(e)).charAt(0)===t.sep,r=e.split(t.sep);return""===r.pop()&&r.length>0&&r.pop(),r.length>1||1===r.length&&!n?r.join(t.sep):n?t.sep:"."},t.basename=function(e,n){if(void 0===n&&(n=""),""===e)return e;var r=(e=t.normalize(e)).split(t.sep),i=r[r.length-1];if(""===i&&r.length>1)return r[r.length-2];if(n.length>0&&i.substr(i.length-n.length)===n)return i.substr(0,i.length-n.length);return i},t.extname=function(e){var n=(e=t.normalize(e)).split(t.sep);if(""===(e=n.pop())&&n.length>0&&(e=n.pop()),".."===e)return"";var r=e.lastIndexOf(".");return-1===r||0===r?"":e.substr(r)},t.isAbsolute=function(e){return e.length>0&&e.charAt(0)===t.sep},t._makeLong=function(t){return t},t.parse=function(t){var e,r,i=(e=t,(r=n.exec(e)).shift(),r);return{root:i[0],dir:i[0]+i[1].slice(0,-1),base:i[2],ext:i[3],name:i[2].slice(0,i[2].length-i[3].length)}},t.format=function(e){if(null===e||"object"!=typeof e)throw new TypeError("Parameter 'pathObject' must be an object, not "+typeof e);if("string"!=typeof(e.root||""))throw new TypeError("'pathObject.root' must be a string or undefined, not "+typeof e.root);return(e.dir?e.dir+t.sep:"")+(e.base||"")},t._removeDuplicateSeps=function(t){return t=t.replace(this._replaceRegex,this.sep)},t.sep="/",t._replaceRegex=new RegExp("//+","g"),t.delimiter=":",t.posix=t,t.win32=t,t}();t.exports=r}).call(this,n(3))},function(t,e,n){"use strict";(function(e,r){var i=n(7);t.exports=v;var o,s=n(25);v.ReadableState=m;n(4).EventEmitter;var a=function(t,e){return t.listeners(e).length},c=n(15),u=n(10).Buffer,f=(void 0!==e?e:"undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).Uint8Array||function(){};var h=Object.create(n(5));h.inherits=n(1);var l=n(26),p=void 0;p=l&&l.debuglog?l.debuglog("stream"):function(){};var d,y=n(27),g=n(16);h.inherits(v,c);var w=["error","close","destroy","pause","resume"];function m(t,e){t=t||{};var r=e instanceof(o=o||n(0));this.objectMode=!!t.objectMode,r&&(this.objectMode=this.objectMode||!!t.readableObjectMode);var i=t.highWaterMark,s=t.readableHighWaterMark,a=this.objectMode?16:16384;this.highWaterMark=i||0===i?i:r&&(s||0===s)?s:a,this.highWaterMark=Math.floor(this.highWaterMark),this.buffer=new y,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.destroyed=!1,this.defaultEncoding=t.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,t.encoding&&(d||(d=n(17).StringDecoder),this.decoder=new d(t.encoding),this.encoding=t.encoding)}function v(t){if(o=o||n(0),!(this instanceof v))return new v(t);this._readableState=new m(t,this),this.readable=!0,t&&("function"==typeof t.read&&(this._read=t.read),"function"==typeof t.destroy&&(this._destroy=t.destroy)),c.call(this)}function _(t,e,n,r,i){var o,s=t._readableState;null===e?(s.reading=!1,function(t,e){if(e.ended)return;if(e.decoder){var n=e.decoder.end();n&&n.length&&(e.buffer.push(n),e.length+=e.objectMode?1:n.length)}e.ended=!0,S(t)}(t,s)):(i||(o=function(t,e){var n;r=e,u.isBuffer(r)||r instanceof f||"string"==typeof e||void 0===e||t.objectMode||(n=new TypeError("Invalid non-string/buffer chunk"));var r;return n}(s,e)),o?t.emit("error",o):s.objectMode||e&&e.length>0?("string"==typeof e||s.objectMode||Object.getPrototypeOf(e)===u.prototype||(e=function(t){return u.from(t)}(e)),r?s.endEmitted?t.emit("error",new Error("stream.unshift() after end event")):b(t,s,e,!0):s.ended?t.emit("error",new Error("stream.push() after EOF")):(s.reading=!1,s.decoder&&!n?(e=s.decoder.write(e),s.objectMode||0!==e.length?b(t,s,e,!1):I(t,s)):b(t,s,e,!1))):r||(s.reading=!1));return function(t){return!t.ended&&(t.needReadable||t.length<t.highWaterMark||0===t.length)}(s)}function b(t,e,n,r){e.flowing&&0===e.length&&!e.sync?(t.emit("data",n),t.read(0)):(e.length+=e.objectMode?1:n.length,r?e.buffer.unshift(n):e.buffer.push(n),e.needReadable&&S(t)),I(t,e)}Object.defineProperty(v.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(t){this._readableState&&(this._readableState.destroyed=t)}}),v.prototype.destroy=g.destroy,v.prototype._undestroy=g.undestroy,v.prototype._destroy=function(t,e){this.push(null),e(t)},v.prototype.push=function(t,e){var n,r=this._readableState;return r.objectMode?n=!0:"string"==typeof t&&((e=e||r.defaultEncoding)!==r.encoding&&(t=u.from(t,e),e=""),n=!0),_(this,t,e,!1,n)},v.prototype.unshift=function(t){return _(this,t,null,!0,!1)},v.prototype.isPaused=function(){return!1===this._readableState.flowing},v.prototype.setEncoding=function(t){return d||(d=n(17).StringDecoder),this._readableState.decoder=new d(t),this._readableState.encoding=t,this};function E(t,e){return t<=0||0===e.length&&e.ended?0:e.objectMode?1:t!=t?e.flowing&&e.length?e.buffer.head.data.length:e.length:(t>e.highWaterMark&&(e.highWaterMark=function(t){return t>=8388608?t=8388608:(t--,t|=t>>>1,t|=t>>>2,t|=t>>>4,t|=t>>>8,t|=t>>>16,t++),t}(t)),t<=e.length?t:e.ended?e.length:(e.needReadable=!0,0))}function S(t){var e=t._readableState;e.needReadable=!1,e.emittedReadable||(p("emitReadable",e.flowing),e.emittedReadable=!0,e.sync?i.nextTick(k,t):k(t))}function k(t){p("emit readable"),t.emit("readable"),F(t)}function I(t,e){e.readingMore||(e.readingMore=!0,i.nextTick(N,t,e))}function N(t,e){for(var n=e.length;!e.reading&&!e.flowing&&!e.ended&&e.length<e.highWaterMark&&(p("maybeReadMore read 0"),t.read(0),n!==e.length);)n=e.length;e.readingMore=!1}function O(t){p("readable nexttick read 0"),t.read(0)}function T(t,e){e.reading||(p("resume read 0"),t.read(0)),e.resumeScheduled=!1,e.awaitDrain=0,t.emit("resume"),F(t),e.flowing&&!e.reading&&t.read(0)}function F(t){var e=t._readableState;for(p("flow",e.flowing);e.flowing&&null!==t.read(););}function x(t,e){return 0===e.length?null:(e.objectMode?n=e.buffer.shift():!t||t>=e.length?(n=e.decoder?e.buffer.join(""):1===e.buffer.length?e.buffer.head.data:e.buffer.concat(e.length),e.buffer.clear()):n=function(t,e,n){var r;t<e.head.data.length?(r=e.head.data.slice(0,t),e.head.data=e.head.data.slice(t)):r=t===e.head.data.length?e.shift():n?function(t,e){var n=e.head,r=1,i=n.data;t-=i.length;for(;n=n.next;){var o=n.data,s=t>o.length?o.length:t;if(s===o.length?i+=o:i+=o.slice(0,t),0===(t-=s)){s===o.length?(++r,n.next?e.head=n.next:e.head=e.tail=null):(e.head=n,n.data=o.slice(s));break}++r}return e.length-=r,i}(t,e):function(t,e){var n=u.allocUnsafe(t),r=e.head,i=1;r.data.copy(n),t-=r.data.length;for(;r=r.next;){var o=r.data,s=t>o.length?o.length:t;if(o.copy(n,n.length-t,0,s),0===(t-=s)){s===o.length?(++i,r.next?e.head=r.next:e.head=e.tail=null):(e.head=r,r.data=o.slice(s));break}++i}return e.length-=i,n}(t,e);return r}(t,e.buffer,e.decoder),n);var n}function R(t){var e=t._readableState;if(e.length>0)throw new Error('"endReadable()" called on non-empty stream');e.endEmitted||(e.ended=!0,i.nextTick(A,e,t))}function A(t,e){t.endEmitted||0!==t.length||(t.endEmitted=!0,e.readable=!1,e.emit("end"))}function L(t,e){for(var n=0,r=t.length;n<r;n++)if(t[n]===e)return n;return-1}v.prototype.read=function(t){p("read",t),t=parseInt(t,10);var e=this._readableState,n=t;if(0!==t&&(e.emittedReadable=!1),0===t&&e.needReadable&&(e.length>=e.highWaterMark||e.ended))return p("read: emitReadable",e.length,e.ended),0===e.length&&e.ended?R(this):S(this),null;if(0===(t=E(t,e))&&e.ended)return 0===e.length&&R(this),null;var r,i=e.needReadable;return p("need readable",i),(0===e.length||e.length-t<e.highWaterMark)&&p("length less than watermark",i=!0),e.ended||e.reading?p("reading or ended",i=!1):i&&(p("do read"),e.reading=!0,e.sync=!0,0===e.length&&(e.needReadable=!0),this._read(e.highWaterMark),e.sync=!1,e.reading||(t=E(n,e))),null===(r=t>0?x(t,e):null)?(e.needReadable=!0,t=0):e.length-=t,0===e.length&&(e.ended||(e.needReadable=!0),n!==t&&e.ended&&R(this)),null!==r&&this.emit("data",r),r},v.prototype._read=function(t){this.emit("error",new Error("_read() is not implemented"))},v.prototype.pipe=function(t,e){var n=this,o=this._readableState;switch(o.pipesCount){case 0:o.pipes=t;break;case 1:o.pipes=[o.pipes,t];break;default:o.pipes.push(t)}o.pipesCount+=1,p("pipe count=%d opts=%j",o.pipesCount,e);var c=(!e||!1!==e.end)&&t!==r.stdout&&t!==r.stderr?f:v;function u(e,r){p("onunpipe"),e===n&&r&&!1===r.hasUnpiped&&(r.hasUnpiped=!0,p("cleanup"),t.removeListener("close",w),t.removeListener("finish",m),t.removeListener("drain",h),t.removeListener("error",g),t.removeListener("unpipe",u),n.removeListener("end",f),n.removeListener("end",v),n.removeListener("data",y),l=!0,!o.awaitDrain||t._writableState&&!t._writableState.needDrain||h())}function f(){p("onend"),t.end()}o.endEmitted?i.nextTick(c):n.once("end",c),t.on("unpipe",u);var h=function(t){return function(){var e=t._readableState;p("pipeOnDrain",e.awaitDrain),e.awaitDrain&&e.awaitDrain--,0===e.awaitDrain&&a(t,"data")&&(e.flowing=!0,F(t))}}(n);t.on("drain",h);var l=!1;var d=!1;function y(e){p("ondata"),d=!1,!1!==t.write(e)||d||((1===o.pipesCount&&o.pipes===t||o.pipesCount>1&&-1!==L(o.pipes,t))&&!l&&(p("false write response, pause",o.awaitDrain),o.awaitDrain++,d=!0),n.pause())}function g(e){p("onerror",e),v(),t.removeListener("error",g),0===a(t,"error")&&t.emit("error",e)}function w(){t.removeListener("finish",m),v()}function m(){p("onfinish"),t.removeListener("close",w),v()}function v(){p("unpipe"),n.unpipe(t)}return n.on("data",y),function(t,e,n){if("function"==typeof t.prependListener)return t.prependListener(e,n);t._events&&t._events[e]?s(t._events[e])?t._events[e].unshift(n):t._events[e]=[n,t._events[e]]:t.on(e,n)}(t,"error",g),t.once("close",w),t.once("finish",m),t.emit("pipe",n),o.flowing||(p("pipe resume"),n.resume()),t},v.prototype.unpipe=function(t){var e=this._readableState,n={hasUnpiped:!1};if(0===e.pipesCount)return this;if(1===e.pipesCount)return t&&t!==e.pipes||(t||(t=e.pipes),e.pipes=null,e.pipesCount=0,e.flowing=!1,t&&t.emit("unpipe",this,n)),this;if(!t){var r=e.pipes,i=e.pipesCount;e.pipes=null,e.pipesCount=0,e.flowing=!1;for(var o=0;o<i;o++)r[o].emit("unpipe",this,{hasUnpiped:!1});return this}var s=L(e.pipes,t);return-1===s||(e.pipes.splice(s,1),e.pipesCount-=1,1===e.pipesCount&&(e.pipes=e.pipes[0]),t.emit("unpipe",this,n)),this},v.prototype.on=function(t,e){var n=c.prototype.on.call(this,t,e);if("data"===t)!1!==this._readableState.flowing&&this.resume();else if("readable"===t){var r=this._readableState;r.endEmitted||r.readableListening||(r.readableListening=r.needReadable=!0,r.emittedReadable=!1,r.reading?r.length&&S(this):i.nextTick(O,this))}return n},v.prototype.addListener=v.prototype.on,v.prototype.resume=function(){var t=this._readableState;return t.flowing||(p("resume"),t.flowing=!0,function(t,e){e.resumeScheduled||(e.resumeScheduled=!0,i.nextTick(T,t,e))}(this,t)),this},v.prototype.pause=function(){return p("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(p("pause"),this._readableState.flowing=!1,this.emit("pause")),this},v.prototype.wrap=function(t){var e=this,n=this._readableState,r=!1;for(var i in t.on("end",(function(){if(p("wrapped end"),n.decoder&&!n.ended){var t=n.decoder.end();t&&t.length&&e.push(t)}e.push(null)})),t.on("data",(function(i){(p("wrapped data"),n.decoder&&(i=n.decoder.write(i)),n.objectMode&&null==i)||(n.objectMode||i&&i.length)&&(e.push(i)||(r=!0,t.pause()))})),t)void 0===this[i]&&"function"==typeof t[i]&&(this[i]=function(e){return function(){return t[e].apply(t,arguments)}}(i));for(var o=0;o<w.length;o++)t.on(w[o],this.emit.bind(this,w[o]));return this._read=function(e){p("wrapped _read",e),r&&(r=!1,t.resume())},this},Object.defineProperty(v.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),v._fromList=x}).call(this,n(6),n(3))},function(t,e,n){t.exports=n(4).EventEmitter},function(t,e,n){"use strict";var r=n(7);function i(t,e){t.emit("error",e)}t.exports={destroy:function(t,e){var n=this,o=this._readableState&&this._readableState.destroyed,s=this._writableState&&this._writableState.destroyed;return o||s?(e?e(t):t&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,r.nextTick(i,this,t)):r.nextTick(i,this,t)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(t||null,(function(t){!e&&t?n._writableState?n._writableState.errorEmitted||(n._writableState.errorEmitted=!0,r.nextTick(i,n,t)):r.nextTick(i,n,t):e&&e(t)})),this)},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}}},function(t,e,n){"use strict";var r=n(30).Buffer,i=r.isEncoding||function(t){switch((t=""+t)&&t.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function o(t){var e;switch(this.encoding=function(t){var e=function(t){if(!t)return"utf8";for(var e;;)switch(t){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return t;default:if(e)return;t=(""+t).toLowerCase(),e=!0}}(t);if("string"!=typeof e&&(r.isEncoding===i||!i(t)))throw new Error("Unknown encoding: "+t);return e||t}(t),this.encoding){case"utf16le":this.text=c,this.end=u,e=4;break;case"utf8":this.fillLast=a,e=4;break;case"base64":this.text=f,this.end=h,e=3;break;default:return this.write=l,void(this.end=p)}this.lastNeed=0,this.lastTotal=0,this.lastChar=r.allocUnsafe(e)}function s(t){return t<=127?0:t>>5==6?2:t>>4==14?3:t>>3==30?4:t>>6==2?-1:-2}function a(t){var e=this.lastTotal-this.lastNeed,n=function(t,e,n){if(128!=(192&e[0]))return t.lastNeed=0,"�";if(t.lastNeed>1&&e.length>1){if(128!=(192&e[1]))return t.lastNeed=1,"�";if(t.lastNeed>2&&e.length>2&&128!=(192&e[2]))return t.lastNeed=2,"�"}}(this,t);return void 0!==n?n:this.lastNeed<=t.length?(t.copy(this.lastChar,e,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(t.copy(this.lastChar,e,0,t.length),void(this.lastNeed-=t.length))}function c(t,e){if((t.length-e)%2==0){var n=t.toString("utf16le",e);if(n){var r=n.charCodeAt(n.length-1);if(r>=55296&&r<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1],n.slice(0,-1)}return n}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=t[t.length-1],t.toString("utf16le",e,t.length-1)}function u(t){var e=t&&t.length?this.write(t):"";if(this.lastNeed){var n=this.lastTotal-this.lastNeed;return e+this.lastChar.toString("utf16le",0,n)}return e}function f(t,e){var n=(t.length-e)%3;return 0===n?t.toString("base64",e):(this.lastNeed=3-n,this.lastTotal=3,1===n?this.lastChar[0]=t[t.length-1]:(this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1]),t.toString("base64",e,t.length-n))}function h(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+this.lastChar.toString("base64",0,3-this.lastNeed):e}function l(t){return t.toString(this.encoding)}function p(t){return t&&t.length?this.write(t):""}e.StringDecoder=o,o.prototype.write=function(t){if(0===t.length)return"";var e,n;if(this.lastNeed){if(void 0===(e=this.fillLast(t)))return"";n=this.lastNeed,this.lastNeed=0}else n=0;return n<t.length?e?e+this.text(t,n):this.text(t,n):e||""},o.prototype.end=function(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+"�":e},o.prototype.text=function(t,e){var n=function(t,e,n){var r=e.length-1;if(r<n)return 0;var i=s(e[r]);if(i>=0)return i>0&&(t.lastNeed=i-1),i;if(--r<n||-2===i)return 0;if((i=s(e[r]))>=0)return i>0&&(t.lastNeed=i-2),i;if(--r<n||-2===i)return 0;if((i=s(e[r]))>=0)return i>0&&(2===i?i=0:t.lastNeed=i-3),i;return 0}(this,t,e);if(!this.lastNeed)return t.toString("utf8",e);this.lastTotal=n;var r=t.length-(n-this.lastNeed);return t.copy(this.lastChar,0,r),t.toString("utf8",e,r)},o.prototype.fillLast=function(t){if(this.lastNeed<=t.length)return t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,t.length),this.lastNeed-=t.length}},function(t,e,n){"use strict";t.exports=s;var r=n(0),i=Object.create(n(5));function o(t,e){var n=this._transformState;n.transforming=!1;var r=n.writecb;if(!r)return this.emit("error",new Error("write callback called multiple times"));n.writechunk=null,n.writecb=null,null!=e&&this.push(e),r(t);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}function s(t){if(!(this instanceof s))return new s(t);r.call(this,t),this._transformState={afterTransform:o.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,t&&("function"==typeof t.transform&&(this._transform=t.transform),"function"==typeof t.flush&&(this._flush=t.flush)),this.on("prefinish",a)}function a(){var t=this;"function"==typeof this._flush?this._flush((function(e,n){c(t,e,n)})):c(this,null,null)}function c(t,e,n){if(e)return t.emit("error",e);if(null!=n&&t.push(n),t._writableState.length)throw new Error("Calling transform done when ws.length != 0");if(t._transformState.transforming)throw new Error("Calling transform done when still transforming");return t.push(null)}i.inherits=n(1),i.inherits(s,r),s.prototype.push=function(t,e){return this._transformState.needTransform=!1,r.prototype.push.call(this,t,e)},s.prototype._transform=function(t,e,n){throw new Error("_transform() is not implemented")},s.prototype._write=function(t,e,n){var r=this._transformState;if(r.writecb=n,r.writechunk=t,r.writeencoding=e,!r.transforming){var i=this._readableState;(r.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},s.prototype._read=function(t){var e=this._transformState;null!==e.writechunk&&e.writecb&&!e.transforming?(e.transforming=!0,this._transform(e.writechunk,e.writeencoding,e.afterTransform)):e.needTransform=!0},s.prototype._destroy=function(t,e){var n=this;r.prototype._destroy.call(this,t,(function(t){e(t),n.emit("close")}))}},function(t,e,n){"use strict";(function(t,r,i){Object.defineProperty(e,"__esModule",{value:!0});var o,s=n(2),a=n(13);!function(t){t[t.EPERM=1]="EPERM",t[t.ENOENT=2]="ENOENT",t[t.EIO=5]="EIO",t[t.EBADF=9]="EBADF",t[t.EACCES=13]="EACCES",t[t.EBUSY=16]="EBUSY",t[t.EEXIST=17]="EEXIST",t[t.ENOTDIR=20]="ENOTDIR",t[t.EISDIR=21]="EISDIR",t[t.EINVAL=22]="EINVAL",t[t.EFBIG=27]="EFBIG",t[t.ENOSPC=28]="ENOSPC",t[t.EROFS=30]="EROFS",t[t.ENOTEMPTY=39]="ENOTEMPTY",t[t.ENOTSUP=95]="ENOTSUP"}(o||(o={}));var c={};c[o.EPERM]="Operation not permitted.",c[o.ENOENT]="No such file or directory.",c[o.EIO]="Input/output error.",c[o.EBADF]="Bad file descriptor.",c[o.EACCES]="Permission denied.",c[o.EBUSY]="Resource busy or locked.",c[o.EEXIST]="File exists.",c[o.ENOTDIR]="File is not a directory.",c[o.EISDIR]="File is a directory.",c[o.EINVAL]="Invalid argument.",c[o.EFBIG]="File is too big.",c[o.ENOSPC]="No space left on disk.",c[o.EROFS]="Cannot modify a read-only file system.",c[o.ENOTEMPTY]="Directory is not empty.",c[o.ENOTSUP]="Operation is not supported.";var u,f=function(e){function n(t,n,r){void 0===n&&(n=c[t]),e.call(this,n),this.syscall="",this.errno=t,this.code=o[t],this.path=r,this.stack=(new e).stack,this.message="Error: "+this.code+": "+n+(this.path?", '"+this.path+"'":"")}return e&&(n.__proto__=e),n.prototype=Object.create(e&&e.prototype),n.prototype.constructor=n,n.fromJSON=function(t){var e=new n(0);return e.errno=t.errno,e.code=t.code,e.path=t.path,e.stack=t.stack,e.message=t.message,e},n.fromBuffer=function(t,e){return void 0===e&&(e=0),n.fromJSON(JSON.parse(t.toString("utf8",e+4,e+4+t.readUInt32LE(e))))},n.FileError=function(t,e){return new n(t,c[t],e)},n.ENOENT=function(t){return this.FileError(o.ENOENT,t)},n.EEXIST=function(t){return this.FileError(o.EEXIST,t)},n.EISDIR=function(t){return this.FileError(o.EISDIR,t)},n.ENOTDIR=function(t){return this.FileError(o.ENOTDIR,t)},n.EPERM=function(t){return this.FileError(o.EPERM,t)},n.ENOTEMPTY=function(t){return this.FileError(o.ENOTEMPTY,t)},n.prototype.toString=function(){return this.message},n.prototype.toJSON=function(){return{errno:this.errno,code:this.code,path:this.path,stack:this.stack,message:this.message}},n.prototype.writeToBuffer=function(e,n){void 0===e&&(e=t.alloc(this.bufferSize())),void 0===n&&(n=0);var r=e.write(JSON.stringify(this.toJSON()),n+4);return e.writeUInt32LE(r,n),e},n.prototype.bufferSize=function(){return 4+t.byteLength(JSON.stringify(this.toJSON()))},n}(Error),h=Object.freeze({get ErrorCode(){return o},ErrorStrings:c,ApiError:f});!function(t){t[t.NOP=0]="NOP",t[t.THROW_EXCEPTION=1]="THROW_EXCEPTION",t[t.TRUNCATE_FILE=2]="TRUNCATE_FILE",t[t.CREATE_FILE=3]="CREATE_FILE"}(u||(u={}));var l,p=function t(e){if(this.flagStr=e,t.validFlagStrs.indexOf(e)<0)throw new f(o.EINVAL,"Invalid flag: "+e)};p.getFileFlag=function(t){return p.flagCache.hasOwnProperty(t)?p.flagCache[t]:p.flagCache[t]=new p(t)},p.prototype.getFlagString=function(){return this.flagStr},p.prototype.isReadable=function(){return-1!==this.flagStr.indexOf("r")||-1!==this.flagStr.indexOf("+")},p.prototype.isWriteable=function(){return-1!==this.flagStr.indexOf("w")||-1!==this.flagStr.indexOf("a")||-1!==this.flagStr.indexOf("+")},p.prototype.isTruncating=function(){return-1!==this.flagStr.indexOf("w")},p.prototype.isAppendable=function(){return-1!==this.flagStr.indexOf("a")},p.prototype.isSynchronous=function(){return-1!==this.flagStr.indexOf("s")},p.prototype.isExclusive=function(){return-1!==this.flagStr.indexOf("x")},p.prototype.pathExistsAction=function(){return this.isExclusive()?u.THROW_EXCEPTION:this.isTruncating()?u.TRUNCATE_FILE:u.NOP},p.prototype.pathNotExistsAction=function(){return(this.isWriteable()||this.isAppendable())&&"r+"!==this.flagStr?u.CREATE_FILE:u.THROW_EXCEPTION},p.flagCache={},p.validFlagStrs=["r","r+","rs","rs+","w","wx","w+","wx+","a","ax","a+","ax+"],function(t){t[t.FILE=32768]="FILE",t[t.DIRECTORY=16384]="DIRECTORY",t[t.SYMLINK=40960]="SYMLINK"}(l||(l={}));var d=function(t,e,n,r,i,o,s){this.dev=0,this.ino=0,this.rdev=0,this.nlink=1,this.blksize=4096,this.uid=0,this.gid=0,this.fileData=null,this.size=e;var a=0;if("number"!=typeof r&&(r=a=Date.now()),"number"!=typeof i&&(a||(a=Date.now()),i=a),"number"!=typeof o&&(a||(a=Date.now()),o=a),"number"!=typeof s&&(a||(a=Date.now()),s=a),this.atimeMs=r,this.ctimeMs=o,this.mtimeMs=i,this.birthtimeMs=s,n)this.mode=n;else switch(t){case l.FILE:this.mode=420;break;case l.DIRECTORY:default:this.mode=511}this.blocks=Math.ceil(e/512),this.mode<4096&&(this.mode|=t)},y={atime:{configurable:!0},mtime:{configurable:!0},ctime:{configurable:!0},birthtime:{configurable:!0}};d.fromBuffer=function(t){var e=t.readUInt32LE(0),n=t.readUInt32LE(4),r=t.readDoubleLE(8),i=t.readDoubleLE(16),o=t.readDoubleLE(24);return new d(61440&n,e,4095&n,r,i,o)},d.clone=function(t){return new d(61440&t.mode,t.size,4095&t.mode,t.atimeMs,t.mtimeMs,t.ctimeMs,t.birthtimeMs)},y.atime.get=function(){return new Date(this.atimeMs)},y.mtime.get=function(){return new Date(this.mtimeMs)},y.ctime.get=function(){return new Date(this.ctimeMs)},y.birthtime.get=function(){return new Date(this.birthtimeMs)},d.prototype.toBuffer=function(){var e=t.alloc(32);return e.writeUInt32LE(this.size,0),e.writeUInt32LE(this.mode,4),e.writeDoubleLE(this.atime.getTime(),8),e.writeDoubleLE(this.mtime.getTime(),16),e.writeDoubleLE(this.ctime.getTime(),24),e},d.prototype.isFile=function(){return(61440&this.mode)===l.FILE},d.prototype.isDirectory=function(){return(61440&this.mode)===l.DIRECTORY},d.prototype.isSymbolicLink=function(){return(61440&this.mode)===l.SYMLINK},d.prototype.chmod=function(t){this.mode=61440&this.mode|t},d.prototype.isSocket=function(){return!1},d.prototype.isBlockDevice=function(){return!1},d.prototype.isCharacterDevice=function(){return!1},d.prototype.isFIFO=function(){return!1},Object.defineProperties(d.prototype,y);var g,w="undefined"!=typeof window?window:"undefined"!=typeof self?self:r;if("undefined"!=typeof setImmediate)g=setImmediate;else{var m=w,v=[];if(function(){if(void 0!==m.importScripts||!m.postMessage)return!1;var t=!0,e=m.onmessage;return m.onmessage=function(){t=!1},m.postMessage("","*"),m.onmessage=e,t}()){g=function(t){for(var e=[],n=arguments.length-1;n-- >0;)e[n]=arguments[n+1];v.push({fn:t,args:e}),m.postMessage("zero-timeout-message","*")};var _=function(t){if(t.source===self&&"zero-timeout-message"===t.data&&(t.stopPropagation?t.stopPropagation():t.cancelBubble=!0,v.length>0)){var e=v.shift(),n=e.fn,r=e.args;return n.apply(void 0,r)}};m.addEventListener?m.addEventListener("message",_,!0):m.attachEvent("onmessage",_)}else if(m.MessageChannel){var b=new m.MessageChannel;b.port1.onmessage=function(t){if(v.length>0){var e=v.shift(),n=e.fn,r=e.args;return n.apply(void 0,r)}},g=function(t){for(var e=[],n=arguments.length-1;n-- >0;)e[n]=arguments[n+1];v.push({fn:t,args:e}),b.port2.postMessage("")}}else g=function(t){for(var e=[],n=arguments.length-1;n-- >0;)e[n]=arguments[n+1];return setTimeout.apply(void 0,[t,0].concat(e))}}var E=g,S=n(4),k=function(){this.watchEntries=[]};k.prototype.triggerWatch=function(t,e,n){var r=this;this.watchEntries.filter((function(e){return e.filename===t||!(!e.recursive||!t.startsWith(e.filename))})).forEach((function(i){i.callback&&i.callback(e,t);var o=n||i.curr,s=i.curr||n;o&&s&&i.fileCallback&&(i.fileCallback(o,s),i.curr=o),i.watcher.emit(e),i.persistent||r.removeEntry(i)}))},k.prototype.watch=function(t,e,n){var r=this;void 0===n&&(n=function(){});var i=new S,o={filename:t,watcher:i};return i.close=function(){r.removeEntry(o)},"object"==typeof e?(o.recursive=e.recursive,o.persistent=void 0===e.persistent||e.persistent,o.callback=n):"function"==typeof e&&(o.callback=e),this.watchEntries.push(o),o.watcher},k.prototype.watchFile=function(t,e,n,r){var i=this;void 0===r&&(r=function(){});var o=new S,s={filename:e,watcher:o,curr:t};return o.close=function(){i.removeEntry(s)},"object"==typeof n?(s.recursive=n.recursive,s.persistent=void 0===n.persistent||n.persistent,s.fileCallback=r):"function"==typeof n&&(s.fileCallback=n),this.watchEntries.push(s),s.watcher},k.prototype.unwatchFile=function(t,e){this.watchEntries=this.watchEntries.filter((function(n){return n.filename!==t&&n.fileCallback!==e}))},k.prototype.removeEntry=function(t){this.watchEntries=this.watchEntries.filter((function(e){return e!==t}))};var I=function(t,e){return t};function N(t,e){if("function"!=typeof t)throw new Error("Callback must be a function.");var n=I(t,e);switch(e){case 1:return function(t){E((function(){return n(t)}))};case 2:return function(t,e){E((function(){return n(t,e)}))};case 3:return function(t,e,r){E((function(){return n(t,e,r)}))};default:throw new Error("Invalid invocation of wrapCb.")}}function O(t){if(t)return t;throw new f(o.EIO,"Initialize BrowserFS with a file system using BrowserFS.initialize(filesystem)")}function T(t,e){switch(typeof t){case"number":return t;case"string":var n=parseInt(t,8);return isNaN(n)?e:n;default:return e}}function F(t){if(t instanceof Date)return t;if("number"==typeof t)return new Date(1e3*t);throw new f(o.EINVAL,"Invalid time.")}function x(t){if(t.indexOf("\0")>=0)throw new f(o.EINVAL,"Path must be a string without null bytes.");if(""===t)throw new f(o.EINVAL,"Path must not be empty.");return a.resolve(t)}function R(t,e,n,r){switch(null===t?"null":typeof t){case"object":return{encoding:void 0!==t.encoding?t.encoding:e,flag:void 0!==t.flag?t.flag:n,mode:T(t.mode,r)};case"string":return{encoding:t,flag:n,mode:r};case"null":case"undefined":case"function":return{encoding:e,flag:n,mode:r};default:throw new TypeError('"options" must be a string or an object, got '+typeof t+" instead.")}}function A(){}var L=function(){this.root=null,this.fdMap={},this.nextFd=100,this.fileWatcher=new k};L.prototype.initialize=function(t){if(!t.constructor.isAvailable())throw new f(o.EINVAL,"Tried to instantiate BrowserFS with an unavailable file system.");return this.root=t},L.prototype._toUnixTimestamp=function(t){if("number"==typeof t)return t;if(t instanceof Date)return t.getTime()/1e3;throw new Error("Cannot parse time: "+t)},L.prototype.getRootFS=function(){return this.root?this.root:null},L.prototype.rename=function(t,e,n){var r=this;void 0===n&&(n=A);var i=N(n,1);try{E((function(){r.fileWatcher.triggerWatch(t,"rename"),r.stat(e,(function(t,n){t||r.fileWatcher.triggerWatch(e,"rename",n)}))})),O(this.root).rename(x(t),x(e),i)}catch(t){i(t)}},L.prototype.renameSync=function(t,e){var n=this;E((function(){n.fileWatcher.triggerWatch(t,"rename"),n.fileWatcher.triggerWatch(e,"rename")})),O(this.root).renameSync(x(t),x(e))},L.prototype.exists=function(t,e){void 0===e&&(e=A);var n=N(e,1);try{return O(this.root).exists(x(t),n)}catch(t){return n(!1)}},L.prototype.existsSync=function(t){try{return O(this.root).existsSync(x(t))}catch(t){return!1}},L.prototype.stat=function(t,e){void 0===e&&(e=A);var n=N(e,2);try{return O(this.root).stat(x(t),!1,n)}catch(t){return n(t)}},L.prototype.statSync=function(t){return O(this.root).statSync(x(t),!1)},L.prototype.lstat=function(t,e){void 0===e&&(e=A);var n=N(e,2);try{return O(this.root).stat(x(t),!0,n)}catch(t){return n(t)}},L.prototype.lstatSync=function(t){return O(this.root).statSync(x(t),!0)},L.prototype.truncate=function(t,e,n){var r=this;void 0===e&&(e=0),void 0===n&&(n=A);var i=0;"function"==typeof e?n=e:"number"==typeof e&&(i=e);var s=N(n,1);try{if(i<0)throw new f(o.EINVAL);return E((function(){r.stat(t,(function(e,n){r.fileWatcher.triggerWatch(t,"change",n)}))})),O(this.root).truncate(x(t),i,s)}catch(t){return s(t)}},L.prototype.truncateSync=function(t,e){var n=this;if(void 0===e&&(e=0),e<0)throw new f(o.EINVAL);return E((function(){n.stat(t,(function(e,r){n.fileWatcher.triggerWatch(t,"change",r)}))})),O(this.root).truncateSync(x(t),e)},L.prototype.unlink=function(t,e){var n=this;void 0===e&&(e=A);var r=N(e,1);try{return E((function(){n.fileWatcher.triggerWatch(t,"rename",new d(l.FILE,0,void 0,0,0,0,0))})),O(this.root).unlink(x(t),r)}catch(t){return r(t)}},L.prototype.unlinkSync=function(t){var e=this;return E((function(){e.fileWatcher.triggerWatch(t,"rename",new d(l.FILE,0,void 0,0,0,0,0))})),O(this.root).unlinkSync(x(t))},L.prototype.open=function(t,e,n,r){var i=this;void 0===r&&(r=A);var o=T(n,420),s=N(r="function"==typeof n?n:r,2);try{O(this.root).open(x(t),p.getFileFlag(e),o,(function(t,e){e?s(t,i.getFdForFile(e)):s(t)}))}catch(t){s(t)}},L.prototype.openSync=function(t,e,n){return void 0===n&&(n=420),this.getFdForFile(O(this.root).openSync(x(t),p.getFileFlag(e),T(n,420)))},L.prototype.readFile=function(t,e,n){void 0===e&&(e={}),void 0===n&&(n=A);var r=R(e,null,"r",null),i=N(n="function"==typeof e?e:n,2);try{var s=p.getFileFlag(r.flag);return s.isReadable()?O(this.root).readFile(x(t),r.encoding,s,i):i(new f(o.EINVAL,"Flag passed to readFile must allow for reading."))}catch(t){return i(t)}},L.prototype.readFileSync=function(t,e){void 0===e&&(e={});var n=R(e,null,"r",null),r=p.getFileFlag(n.flag);if(!r.isReadable())throw new f(o.EINVAL,"Flag passed to readFile must allow for reading.");return O(this.root).readFileSync(x(t),n.encoding,r)},L.prototype.writeFile=function(t,e,n,r){var i=this;void 0===n&&(n={}),void 0===r&&(r=A);var s=R(n,"utf8","w",420),a=N(r="function"==typeof n?n:r,1);try{var c=p.getFileFlag(s.flag);if(!c.isWriteable())return a(new f(o.EINVAL,"Flag passed to writeFile must allow for writing."));O(this.root).writeFile(x(t),e,s.encoding,c,s.mode,(function(){for(var e=[],n=arguments.length;n--;)e[n]=arguments[n];E((function(){i.stat(t,(function(e,n){i.fileWatcher.triggerWatch(t,"change",n)}))})),a.apply(void 0,e)}))}catch(t){return a(t)}},L.prototype.writeFileSync=function(t,e,n){var r=this,i=R(n,"utf8","w",420),s=p.getFileFlag(i.flag);if(!s.isWriteable())throw new f(o.EINVAL,"Flag passed to writeFile must allow for writing.");return E((function(){r.stat(t,(function(e,n){r.fileWatcher.triggerWatch(t,"change",n)}))})),O(this.root).writeFileSync(x(t),e,i.encoding,s,i.mode)},L.prototype.appendFile=function(t,e,n,r){var i=this;void 0===r&&(r=A);var s=R(n,"utf8","a",420),a=N(r="function"==typeof n?n:r,1);try{var c=p.getFileFlag(s.flag);if(!c.isAppendable())return a(new f(o.EINVAL,"Flag passed to appendFile must allow for appending."));E((function(){i.stat(t,(function(e,n){i.fileWatcher.triggerWatch(t,"rename",n)}))})),O(this.root).appendFile(x(t),e,s.encoding,c,s.mode,a)}catch(t){a(t)}},L.prototype.appendFileSync=function(t,e,n){var r=this,i=R(n,"utf8","a",420),s=p.getFileFlag(i.flag);if(!s.isAppendable())throw new f(o.EINVAL,"Flag passed to appendFile must allow for appending.");return E((function(){r.stat(t,(function(e,n){r.fileWatcher.triggerWatch(t,"change",n)}))})),O(this.root).appendFileSync(x(t),e,i.encoding,s,i.mode)},L.prototype.fstat=function(t,e){void 0===e&&(e=A);var n=N(e,2);try{this.fd2file(t).stat(n)}catch(t){n(t)}},L.prototype.fstatSync=function(t){return this.fd2file(t).statSync()},L.prototype.close=function(t,e){var n=this;void 0===e&&(e=A);var r=N(e,1);try{this.fd2file(t).close((function(e){e||n.closeFd(t),r(e)}))}catch(t){r(t)}},L.prototype.closeSync=function(t){this.fd2file(t).closeSync(),this.closeFd(t)},L.prototype.ftruncate=function(t,e,n){void 0===n&&(n=A);var r="number"==typeof e?e:0,i=N(n="function"==typeof e?e:n,1);try{var s=this.fd2file(t);if(r<0)throw new f(o.EINVAL);s.truncate(r,i)}catch(t){i(t)}},L.prototype.ftruncateSync=function(t,e){void 0===e&&(e=0);var n=this.fd2file(t);if(e<0)throw new f(o.EINVAL);n.truncateSync(e)},L.prototype.fsync=function(t,e){void 0===e&&(e=A);var n=N(e,1);try{this.fd2file(t).sync(n)}catch(t){n(t)}},L.prototype.fsyncSync=function(t){this.fd2file(t).syncSync()},L.prototype.fdatasync=function(t,e){void 0===e&&(e=A);var n=N(e,1);try{this.fd2file(t).datasync(n)}catch(t){n(t)}},L.prototype.fdatasyncSync=function(t){this.fd2file(t).datasyncSync()},L.prototype.write=function(e,n,r,i,s,a){var c,u,h;void 0===a&&(a=A);var l=null;if("string"==typeof n){var p="utf8";switch(typeof r){case"function":a=r;break;case"number":l=r,p="string"==typeof i?i:"utf8",a="function"==typeof s?s:a;break;default:return(a="function"==typeof i?i:"function"==typeof s?s:a)(new f(o.EINVAL,"Invalid arguments."))}u=0,h=(c=t.from(n,p)).length}else c=n,u=r,h=i,l="number"==typeof s?s:null,a="function"==typeof s?s:a;var d=N(a,3);try{var y=this.fd2file(e);null==l&&(l=y.getPos()),y.write(c,u,h,l,d)}catch(t){d(t)}},L.prototype.writeSync=function(e,n,r,i,o){var s,a,c,u=0;if("string"==typeof n){c="number"==typeof r?r:null;var f="string"==typeof i?i:"utf8";u=0,a=(s=t.from(n,f)).length}else s=n,u=r,a=i,c="number"==typeof o?o:null;var h=this.fd2file(e);return null==c&&(c=h.getPos()),h.writeSync(s,u,a,c)},L.prototype.read=function(e,n,r,i,o,s){var a,c,u,f,h;if(void 0===s&&(s=A),"number"==typeof n){u=n,a=r;var l=i;s="function"==typeof o?o:s,c=0,f=t.alloc(u),h=N((function(t,e,n){if(t)return s(t);s(t,n.toString(l),e)}),3)}else f=n,c=r,u=i,a=o,h=N(s,3);try{var p=this.fd2file(e);null==a&&(a=p.getPos()),p.read(f,c,u,a,h)}catch(t){h(t)}},L.prototype.readSync=function(e,n,r,i,o){var s,a,c,u,f=!1,h="utf8";"number"==typeof n?(c=n,u=r,h=i,a=0,s=t.alloc(c),f=!0):(s=n,a=r,c=i,u=o);var l=this.fd2file(e);null==u&&(u=l.getPos());var p=l.readSync(s,a,c,u);return f?[s.toString(h),p]:p},L.prototype.fchown=function(t,e,n,r){void 0===r&&(r=A);var i=N(r,1);try{this.fd2file(t).chown(e,n,i)}catch(t){i(t)}},L.prototype.fchownSync=function(t,e,n){this.fd2file(t).chownSync(e,n)},L.prototype.fchmod=function(t,e,n){var r=N(n,1);try{var i="string"==typeof e?parseInt(e,8):e;this.fd2file(t).chmod(i,r)}catch(t){r(t)}},L.prototype.fchmodSync=function(t,e){var n="string"==typeof e?parseInt(e,8):e;this.fd2file(t).chmodSync(n)},L.prototype.futimes=function(t,e,n,r){void 0===r&&(r=A);var i=N(r,1);try{var o=this.fd2file(t);"number"==typeof e&&(e=new Date(1e3*e)),"number"==typeof n&&(n=new Date(1e3*n)),o.utimes(e,n,i)}catch(t){i(t)}},L.prototype.futimesSync=function(t,e,n){this.fd2file(t).utimesSync(F(e),F(n))},L.prototype.rmdir=function(t,e){var n=this;void 0===e&&(e=A);var r=N(e,1);try{t=x(t),E((function(){n.fileWatcher.triggerWatch(t,"rename")})),O(this.root).rmdir(t,r)}catch(t){r(t)}},L.prototype.rmdirSync=function(t){var e=this;return t=x(t),E((function(){e.fileWatcher.triggerWatch(t,"rename")})),O(this.root).rmdirSync(t)},L.prototype.mkdir=function(t,e,n){var r=this;void 0===n&&(n=A),"function"==typeof e&&(n=e,e=511);var i=N(n,1);try{t=x(t),E((function(){r.fileWatcher.triggerWatch(t,"rename")})),O(this.root).mkdir(t,e,i)}catch(t){i(t)}},L.prototype.mkdirSync=function(t,e){var n=this;E((function(){n.fileWatcher.triggerWatch(t,"rename")})),O(this.root).mkdirSync(x(t),T(e,511))},L.prototype.readdir=function(t,e){void 0===e&&(e=A);var n=N(e,2);try{t=x(t),O(this.root).readdir(t,n)}catch(t){n(t)}},L.prototype.readdirSync=function(t){return t=x(t),O(this.root).readdirSync(t)},L.prototype.link=function(t,e,n){void 0===n&&(n=A);var r=N(n,1);try{t=x(t),e=x(e),O(this.root).link(t,e,r)}catch(t){r(t)}},L.prototype.linkSync=function(t,e){return t=x(t),e=x(e),O(this.root).linkSync(t,e)},L.prototype.symlink=function(t,e,n,r){void 0===r&&(r=A);var i="string"==typeof n?n:"file",s=N(r="function"==typeof n?n:r,1);try{if("file"!==i&&"dir"!==i)return s(new f(o.EINVAL,"Invalid type: "+i));t=x(t),e=x(e),O(this.root).symlink(t,e,i,s)}catch(t){s(t)}},L.prototype.symlinkSync=function(t,e,n){if(n){if("file"!==n&&"dir"!==n)throw new f(o.EINVAL,"Invalid type: "+n)}else n="file";return t=x(t),e=x(e),O(this.root).symlinkSync(t,e,n)},L.prototype.readlink=function(t,e){void 0===e&&(e=A);var n=N(e,2);try{t=x(t),O(this.root).readlink(t,n)}catch(t){n(t)}},L.prototype.readlinkSync=function(t){return t=x(t),O(this.root).readlinkSync(t)},L.prototype.chown=function(t,e,n,r){void 0===r&&(r=A);var i=N(r,1);try{t=x(t),O(this.root).chown(t,!1,e,n,i)}catch(t){i(t)}},L.prototype.chownSync=function(t,e,n){t=x(t),O(this.root).chownSync(t,!1,e,n)},L.prototype.lchown=function(t,e,n,r){void 0===r&&(r=A);var i=N(r,1);try{t=x(t),O(this.root).chown(t,!0,e,n,i)}catch(t){i(t)}},L.prototype.lchownSync=function(t,e,n){t=x(t),O(this.root).chownSync(t,!0,e,n)},L.prototype.chmod=function(t,e,n){void 0===n&&(n=A);var r=N(n,1);try{var i=T(e,-1);if(i<0)throw new f(o.EINVAL,"Invalid mode.");O(this.root).chmod(x(t),!1,i,r)}catch(t){r(t)}},L.prototype.chmodSync=function(t,e){var n=T(e,-1);if(n<0)throw new f(o.EINVAL,"Invalid mode.");t=x(t),O(this.root).chmodSync(t,!1,n)},L.prototype.lchmod=function(t,e,n){void 0===n&&(n=A);var r=N(n,1);try{var i=T(e,-1);if(i<0)throw new f(o.EINVAL,"Invalid mode.");O(this.root).chmod(x(t),!0,i,r)}catch(t){r(t)}},L.prototype.lchmodSync=function(t,e){var n=T(e,-1);if(n<1)throw new f(o.EINVAL,"Invalid mode.");O(this.root).chmodSync(x(t),!0,n)},L.prototype.utimes=function(t,e,n,r){void 0===r&&(r=A);var i=N(r,1);try{O(this.root).utimes(x(t),F(e),F(n),i)}catch(t){i(t)}},L.prototype.utimesSync=function(t,e,n){O(this.root).utimesSync(x(t),F(e),F(n))},L.prototype.realpath=function(t,e,n){void 0===n&&(n=A);var r="object"==typeof e?e:{},i=N(n="function"==typeof e?e:A,2);try{t=x(t),O(this.root).realpath(t,r,i)}catch(t){i(t)}},L.prototype.realpathSync=function(t,e){return void 0===e&&(e={}),t=x(t),O(this.root).realpathSync(t,e)},L.prototype.watchFile=function(t,e,n){var r=this;void 0===n&&(n=A),this.stat(t,(function(i,o){var s=o;i&&(s=new d(l.FILE,0,void 0,0,0,0,0)),r.fileWatcher.watchFile(s,t,e,n)}))},L.prototype.unwatchFile=function(t,e){void 0===e&&(e=A),this.fileWatcher.unwatchFile(t,e)},L.prototype.watch=function(t,e,n){return void 0===n&&(n=A),this.fileWatcher.watch(t,e,n)},L.prototype.access=function(t,e,n){throw void 0===n&&(n=A),new f(o.ENOTSUP)},L.prototype.accessSync=function(t,e){throw new f(o.ENOTSUP)},L.prototype.createReadStream=function(t,e){throw new f(o.ENOTSUP)},L.prototype.createWriteStream=function(t,e){throw new f(o.ENOTSUP)},L.prototype.wrapCallbacks=function(t){I=t},L.prototype.getFdForFile=function(t){var e=this.nextFd++;return this.fdMap[e]=t,e},L.prototype.fd2file=function(t){var e=this.fdMap[t];if(e)return e;throw new f(o.EBADF,"Invalid file descriptor.")},L.prototype.closeFd=function(t){delete this.fdMap[t]},L.Stats=d,L.F_OK=0,L.R_OK=4,L.W_OK=2,L.X_OK=1;var D=new L,P={},C=L.prototype;function M(t,e,n,r,i){return t<e||n<e?t>n?n+1:t+1:r===i?e:e+1}function U(t,e){if(t===e)return 0;if(t.length>e.length){var n=t;t=e,e=n}for(var r=t.length,i=e.length;r>0&&t.charCodeAt(r-1)===e.charCodeAt(i-1);)r--,i--;for(var o=0;o<r&&t.charCodeAt(o)===e.charCodeAt(o);)o++;if(i-=o,0===(r-=o)||1===i)return i;for(var s,a,c,u,f,h=new Array(r<<1),l=0;l<r;)h[r+l]=t.charCodeAt(o+l),h[l]=++l;for(s=0;s+3<i;)for(var p=e.charCodeAt(o+(a=s)),d=e.charCodeAt(o+(c=s+1)),y=e.charCodeAt(o+(u=s+2)),g=e.charCodeAt(o+(f=s+3)),w=s+=4,m=0;m<r;){var v=h[r+m],_=h[m];a=M(_,a,c,p,v),c=M(a,c,u,d,v),u=M(c,u,f,y,v),w=M(u,f,w,g,v),h[m++]=w,f=u,u=c,c=a,a=_}for(var b=0;s<i;){var E=e.charCodeAt(o+(a=s));b=++s;for(var S=0;S<r;S++){var k=h[S];h[S]=b=k<a||b<a?k>b?b+1:k+1:E===h[r+S]?a:a+1,a=k}}return b}Object.keys(C).forEach((function(t){"function"==typeof D[t]?P[t]=function(){return D[t].apply(D,arguments)}:P[t]=D[t]})),P.changeFSModule=function(t){D=t},P.getFSModule=function(){return D},P.FS=L,P.Stats=L.Stats,P.F_OK=0,P.R_OK=4,P.W_OK=2,P.X_OK=1;var j="undefined"!=typeof navigator&&Boolean(/(msie) ([\w.]+)/.exec(navigator.userAgent.toLowerCase())||-1!==navigator.userAgent.indexOf("Trident")),B="undefined"==typeof window;function z(){throw new Error("BFS has reached an impossible code path; please file a bug.")}function q(t,e,n){n.existsSync(t)||(q(a.dirname(t),e,n),n.mkdirSync(t,e))}function W(t){var e=H(t),n=e.byteOffset,r=e.byteLength;return 0===n&&r===e.buffer.byteLength?e.buffer:e.buffer.slice(n,n+r)}function H(t){return t instanceof Uint8Array?t:new Uint8Array(t)}function V(e){return e instanceof t?e:e instanceof Uint8Array?X(e):t.from(e)}function X(e){return e instanceof t?e:0===e.byteOffset&&e.byteLength===e.buffer.byteLength?Z(e.buffer):t.from(e.buffer,e.byteOffset,e.byteLength)}function Z(e){return t.from(e)}function Y(t,e,n){if(void 0===e&&(e=0),void 0===n&&(n=t.length),e<0||n<0||n>t.length||e>n)throw new TypeError("Invalid slice bounds on buffer of length "+t.length+": ["+e+", "+n+"]");if(0===t.length)return K();var r=H(t),i=t[0],o=(i+1)%255;return t[0]=o,r[0]===o?(r[0]=i,X(r.slice(e,n))):(t[0]=i,X(r.subarray(e,n)))}var J=null;function K(){return J||(J=t.alloc(0))}function G(e,n){t.isBuffer(e)?n():n(new f(o.EINVAL,"option must be a Buffer."))}function Q(t,e,n){var r=t.Options,i=t.Name,s=0,a=!1,c=!1;function u(t){a||(t&&(a=!0,n(t)),0===--s&&c&&n())}var h=function(t){if(r.hasOwnProperty(t)){var c=r[t],h=e[t];if(null==h){if(!c.optional){var l=Object.keys(e).filter((function(t){return!(t in r)})).map((function(e){return{str:e,distance:U(t,e)}})).filter((function(t){return t.distance<5})).sort((function(t,e){return t.distance-e.distance}));return a?{}:(a=!0,{v:n(new f(o.EINVAL,"["+i+"] Required option '"+t+"' not provided."+(l.length>0?" You provided unrecognized option '"+l[0].str+"'; perhaps you meant to type '"+t+"'.":"")+"\nOption description: "+c.description))})}}else{if(!(Array.isArray(c.type)?-1!==c.type.indexOf(typeof h):typeof h===c.type))return a?{}:(a=!0,{v:n(new f(o.EINVAL,"["+i+"] Value provided for option "+t+" is not the proper type. Expected "+(Array.isArray(c.type)?"one of {"+c.type.join(", ")+"}":c.type)+", but received "+typeof h+"\nOption description: "+c.description))});c.validator&&(s++,c.validator(h,u))}}};for(var l in r){var p=h(l);if(p)return p.v}c=!0,0!==s||a||n()}var $=Object.freeze({deprecationMessage:function(t,e,n){t&&console.warn("["+e+"] Direct file system constructor usage is deprecated for this file system, and will be removed in the next major version. Please use the '"+e+".Create("+JSON.stringify(n)+", callback)' method instead. See https://github.com/jvilk/BrowserFS/issues/176 for more details.")},isIE:j,isWebWorker:B,fail:z,mkdirpSync:q,buffer2ArrayBuffer:W,buffer2Uint8array:H,arrayish2Buffer:V,uint8Array2Buffer:X,arrayBuffer2Buffer:Z,copyingSlice:Y,emptyBuffer:K,bufferValidator:G,checkOptions:Q}),tt=function(t){this.fs=t,this.nodefs=t.getNodeFS(),this.FS=t.getFS(),this.PATH=t.getPATH(),this.ERRNO_CODES=t.getERRNO_CODES()};tt.prototype.open=function(t){var e=this.fs.realPath(t.node),n=this.FS;try{n.isFile(t.node.mode)&&(t.nfd=this.nodefs.openSync(e,this.fs.flagsToPermissionString(t.flags)))}catch(t){if(!t.code)throw t;throw new n.ErrnoError(this.ERRNO_CODES[t.code])}},tt.prototype.close=function(t){var e=this.FS;try{e.isFile(t.node.mode)&&t.nfd&&this.nodefs.closeSync(t.nfd)}catch(t){if(!t.code)throw t;throw new e.ErrnoError(this.ERRNO_CODES[t.code])}},tt.prototype.read=function(t,e,n,r,i){try{return this.nodefs.readSync(t.nfd,X(e),n,r,i)}catch(t){throw new this.FS.ErrnoError(this.ERRNO_CODES[t.code])}},tt.prototype.write=function(t,e,n,r,i){try{return this.nodefs.writeSync(t.nfd,X(e),n,r,i)}catch(t){throw new this.FS.ErrnoError(this.ERRNO_CODES[t.code])}},tt.prototype.llseek=function(t,e,n){var r=e;if(1===n)r+=t.position;else if(2===n&&this.FS.isFile(t.node.mode))try{r+=this.nodefs.fstatSync(t.nfd).size}catch(t){throw new this.FS.ErrnoError(this.ERRNO_CODES[t.code])}if(r<0)throw new this.FS.ErrnoError(this.ERRNO_CODES.EINVAL);return t.position=r,r};var et=function(t){this.fs=t,this.nodefs=t.getNodeFS(),this.FS=t.getFS(),this.PATH=t.getPATH(),this.ERRNO_CODES=t.getERRNO_CODES()};et.prototype.getattr=function(t){var e,n=this.fs.realPath(t);try{e=this.nodefs.lstatSync(n)}catch(t){if(!t.code)throw t;throw new this.FS.ErrnoError(this.ERRNO_CODES[t.code])}return{dev:e.dev,ino:e.ino,mode:e.mode,nlink:e.nlink,uid:e.uid,gid:e.gid,rdev:e.rdev,size:e.size,atime:e.atime,mtime:e.mtime,ctime:e.ctime,blksize:e.blksize,blocks:e.blocks}},et.prototype.setattr=function(t,e){var n=this.fs.realPath(t);try{if(void 0!==e.mode&&(this.nodefs.chmodSync(n,e.mode),t.mode=e.mode),void 0!==e.timestamp){var r=new Date(e.timestamp);this.nodefs.utimesSync(n,r,r)}}catch(t){if(!t.code)throw t;if("ENOTSUP"!==t.code)throw new this.FS.ErrnoError(this.ERRNO_CODES[t.code])}if(void 0!==e.size)try{this.nodefs.truncateSync(n,e.size)}catch(t){if(!t.code)throw t;throw new this.FS.ErrnoError(this.ERRNO_CODES[t.code])}},et.prototype.lookup=function(t,e){var n=this.PATH.join2(this.fs.realPath(t),e),r=this.fs.getMode(n);return this.fs.createNode(t,e,r)},et.prototype.mknod=function(t,e,n,r){var i=this.fs.createNode(t,e,n,r),o=this.fs.realPath(i);try{this.FS.isDir(i.mode)?this.nodefs.mkdirSync(o,i.mode):this.nodefs.writeFileSync(o,"",{mode:i.mode})}catch(t){if(!t.code)throw t;throw new this.FS.ErrnoError(this.ERRNO_CODES[t.code])}return i},et.prototype.rename=function(t,e,n){var r=this.fs.realPath(t),i=this.PATH.join2(this.fs.realPath(e),n);try{this.nodefs.renameSync(r,i),t.name=n,t.parent=e}catch(t){if(!t.code)throw t;throw new this.FS.ErrnoError(this.ERRNO_CODES[t.code])}},et.prototype.unlink=function(t,e){var n=this.PATH.join2(this.fs.realPath(t),e);try{this.nodefs.unlinkSync(n)}catch(t){if(!t.code)throw t;throw new this.FS.ErrnoError(this.ERRNO_CODES[t.code])}},et.prototype.rmdir=function(t,e){var n=this.PATH.join2(this.fs.realPath(t),e);try{this.nodefs.rmdirSync(n)}catch(t){if(!t.code)throw t;throw new this.FS.ErrnoError(this.ERRNO_CODES[t.code])}},et.prototype.readdir=function(t){var e=this.fs.realPath(t);try{var n=this.nodefs.readdirSync(e);return n.push(".",".."),n}catch(t){if(!t.code)throw t;throw new this.FS.ErrnoError(this.ERRNO_CODES[t.code])}},et.prototype.symlink=function(t,e,n){var r=this.PATH.join2(this.fs.realPath(t),e);try{this.nodefs.symlinkSync(n,r)}catch(t){if(!t.code)throw t;throw new this.FS.ErrnoError(this.ERRNO_CODES[t.code])}},et.prototype.readlink=function(t){var e=this.fs.realPath(t);try{return this.nodefs.readlinkSync(e)}catch(t){if(!t.code)throw t;throw new this.FS.ErrnoError(this.ERRNO_CODES[t.code])}};var nt=function(t,e,n,r){void 0===t&&(t=self.FS),void 0===e&&(e=self.PATH),void 0===n&&(n=self.ERRNO_CODES),void 0===r&&(r=P),this.flagsToPermissionStringMap={0:"r",1:"r+",2:"r+",64:"r",65:"r+",66:"r+",129:"rx+",193:"rx+",514:"w+",577:"w",578:"w+",705:"wx",706:"wx+",1024:"a",1025:"a",1026:"a+",1089:"a",1090:"a+",1153:"ax",1154:"ax+",1217:"ax",1218:"ax+",4096:"rs",4098:"rs+"},this.nodefs=r,this.FS=t,this.PATH=e,this.ERRNO_CODES=n,this.node_ops=new et(this),this.stream_ops=new tt(this)};nt.prototype.mount=function(t){return this.createNode(null,"/",this.getMode(t.opts.root),0)},nt.prototype.createNode=function(t,e,n,r){var i=this.FS;if(!i.isDir(n)&&!i.isFile(n)&&!i.isLink(n))throw new i.ErrnoError(this.ERRNO_CODES.EINVAL);var o=i.createNode(t,e,n);return o.node_ops=this.node_ops,o.stream_ops=this.stream_ops,o},nt.prototype.getMode=function(t){var e;try{e=this.nodefs.lstatSync(t)}catch(t){if(!t.code)throw t;throw new this.FS.ErrnoError(this.ERRNO_CODES[t.code])}return e.mode},nt.prototype.realPath=function(t){for(var e=[];t.parent!==t;)e.push(t.name),t=t.parent;return e.push(t.mount.opts.root),e.reverse(),this.PATH.join.apply(null,e)},nt.prototype.flagsToPermissionString=function(t){var e="string"==typeof t?parseInt(t,10):t;return(e&=8191)in this.flagsToPermissionStringMap?this.flagsToPermissionStringMap[e]:t},nt.prototype.getNodeFS=function(){return this.nodefs},nt.prototype.getFS=function(){return this.FS},nt.prototype.getPATH=function(){return this.PATH},nt.prototype.getERRNO_CODES=function(){return this.ERRNO_CODES};var rt=function(){};rt.prototype.supportsLinks=function(){return!1},rt.prototype.diskSpace=function(t,e){e(0,0)},rt.prototype.openFile=function(t,e,n){throw new f(o.ENOTSUP)},rt.prototype.createFile=function(t,e,n,r){throw new f(o.ENOTSUP)},rt.prototype.open=function(t,e,n,r){var i=this;this.stat(t,!1,(function(s,c){if(s)switch(e.pathNotExistsAction()){case u.CREATE_FILE:return i.stat(a.dirname(t),!1,(function(o,s){o?r(o):s&&!s.isDirectory()?r(f.ENOTDIR(a.dirname(t))):i.createFile(t,e,n,r)}));case u.THROW_EXCEPTION:return r(f.ENOENT(t));default:return r(new f(o.EINVAL,"Invalid FileFlag object."))}else{if(c&&c.isDirectory())return r(f.EISDIR(t));switch(e.pathExistsAction()){case u.THROW_EXCEPTION:return r(f.EEXIST(t));case u.TRUNCATE_FILE:return i.openFile(t,e,(function(t,e){t?r(t):e?e.truncate(0,(function(){e.sync((function(){r(null,e)}))})):z()}));case u.NOP:return i.openFile(t,e,r);default:return r(new f(o.EINVAL,"Invalid FileFlag object."))}}}))},rt.prototype.rename=function(t,e,n){n(new f(o.ENOTSUP))},rt.prototype.renameSync=function(t,e){throw new f(o.ENOTSUP)},rt.prototype.stat=function(t,e,n){n(new f(o.ENOTSUP))},rt.prototype.statSync=function(t,e){throw new f(o.ENOTSUP)},rt.prototype.openFileSync=function(t,e,n){throw new f(o.ENOTSUP)},rt.prototype.createFileSync=function(t,e,n){throw new f(o.ENOTSUP)},rt.prototype.openSync=function(t,e,n){var r;try{r=this.statSync(t,!1)}catch(r){switch(e.pathNotExistsAction()){case u.CREATE_FILE:if(!this.statSync(a.dirname(t),!1).isDirectory())throw f.ENOTDIR(a.dirname(t));return this.createFileSync(t,e,n);case u.THROW_EXCEPTION:throw f.ENOENT(t);default:throw new f(o.EINVAL,"Invalid FileFlag object.")}}if(r.isDirectory())throw f.EISDIR(t);switch(e.pathExistsAction()){case u.THROW_EXCEPTION:throw f.EEXIST(t);case u.TRUNCATE_FILE:return this.unlinkSync(t),this.createFileSync(t,e,r.mode);case u.NOP:return this.openFileSync(t,e,n);default:throw new f(o.EINVAL,"Invalid FileFlag object.")}},rt.prototype.unlink=function(t,e){e(new f(o.ENOTSUP))},rt.prototype.unlinkSync=function(t){throw new f(o.ENOTSUP)},rt.prototype.rmdir=function(t,e){e(new f(o.ENOTSUP))},rt.prototype.rmdirSync=function(t){throw new f(o.ENOTSUP)},rt.prototype.mkdir=function(t,e,n){n(new f(o.ENOTSUP))},rt.prototype.mkdirSync=function(t,e){throw new f(o.ENOTSUP)},rt.prototype.readdir=function(t,e){e(new f(o.ENOTSUP))},rt.prototype.readdirSync=function(t){throw new f(o.ENOTSUP)},rt.prototype.exists=function(t,e){this.stat(t,null,(function(t){e(!t)}))},rt.prototype.existsSync=function(t){try{return this.statSync(t,!0),!0}catch(t){return!1}},rt.prototype.realpath=function(t,e,n){if(this.supportsLinks())for(var r=t.split(a.sep),i=0;i<r.length;i++){var o=r.slice(0,i+1);r[i]=a.join.apply(null,o)}else this.exists(t,(function(e){e?n(null,t):n(f.ENOENT(t))}))},rt.prototype.realpathSync=function(t,e){if(this.supportsLinks()){for(var n=t.split(a.sep),r=0;r<n.length;r++){var i=n.slice(0,r+1);n[r]=a.join.apply(a,i)}return n.join(a.sep)}if(this.existsSync(t))return t;throw f.ENOENT(t)},rt.prototype.truncate=function(t,e,n){this.open(t,p.getFileFlag("r+"),420,(function(t,r){if(t)return n(t);r.truncate(e,(function(t){r.close((function(e){n(t||e)}))}))}))},rt.prototype.truncateSync=function(t,e){var n=this.openSync(t,p.getFileFlag("r+"),420);try{n.truncateSync(e)}catch(t){throw t}finally{n.closeSync()}},rt.prototype.readFile=function(e,n,r,i){var o=i;this.open(e,r,420,(function(e,r){if(e)return i(e);i=function(t,e){r.close((function(n){return t||(t=n),o(t,e)}))},r.stat((function(e,o){if(e)return i(e);var s=t.alloc(o.size);r.read(s,0,o.size,0,(function(t){if(t)return i(t);if(null===n)return i(t,s);try{i(null,s.toString(n))}catch(t){i(t)}}))}))}))},rt.prototype.readFileSync=function(e,n,r){var i=this.openSync(e,r,420);try{var o=i.statSync(),s=t.alloc(o.size);return i.readSync(s,0,o.size,0),i.closeSync(),null===n?s:s.toString(n)}finally{i.closeSync()}},rt.prototype.writeFile=function(e,n,r,i,o,s){var a=s;this.open(e,i,420,(function(e,i){if(e)return s(e);s=function(t){i.close((function(e){a(t||e)}))};try{"string"==typeof n&&(n=t.from(n,r))}catch(t){return s(t)}i.write(n,0,n.length,0,s)}))},rt.prototype.writeFileSync=function(e,n,r,i,o){var s=this.openSync(e,i,o);try{"string"==typeof n&&(n=t.from(n,r)),s.writeSync(n,0,n.length,0)}finally{s.closeSync()}},rt.prototype.appendFile=function(e,n,r,i,o,s){var a=s;this.open(e,i,o,(function(e,i){if(e)return s(e);s=function(t){i.close((function(e){a(t||e)}))},"string"==typeof n&&(n=t.from(n,r)),i.write(n,0,n.length,null,s)}))},rt.prototype.appendFileSync=function(e,n,r,i,o){var s=this.openSync(e,i,o);try{"string"==typeof n&&(n=t.from(n,r)),s.writeSync(n,0,n.length,null)}finally{s.closeSync()}},rt.prototype.chmod=function(t,e,n,r){r(new f(o.ENOTSUP))},rt.prototype.chmodSync=function(t,e,n){throw new f(o.ENOTSUP)},rt.prototype.chown=function(t,e,n,r,i){i(new f(o.ENOTSUP))},rt.prototype.chownSync=function(t,e,n,r){throw new f(o.ENOTSUP)},rt.prototype.utimes=function(t,e,n,r){r(new f(o.ENOTSUP))},rt.prototype.utimesSync=function(t,e,n){throw new f(o.ENOTSUP)},rt.prototype.link=function(t,e,n){n(new f(o.ENOTSUP))},rt.prototype.linkSync=function(t,e){throw new f(o.ENOTSUP)},rt.prototype.symlink=function(t,e,n,r){r(new f(o.ENOTSUP))},rt.prototype.symlinkSync=function(t,e,n){throw new f(o.ENOTSUP)},rt.prototype.readlink=function(t,e){e(new f(o.ENOTSUP))},rt.prototype.readlinkSync=function(t){throw new f(o.ENOTSUP)};var it=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.supportsSynch=function(){return!0},e.prototype.rename=function(t,e,n){try{this.renameSync(t,e),n()}catch(t){n(t)}},e.prototype.stat=function(t,e,n){try{n(null,this.statSync(t,e))}catch(t){n(t)}},e.prototype.open=function(t,e,n,r){try{r(null,this.openSync(t,e,n))}catch(t){r(t)}},e.prototype.unlink=function(t,e){try{this.unlinkSync(t),e()}catch(t){e(t)}},e.prototype.rmdir=function(t,e){try{this.rmdirSync(t),e()}catch(t){e(t)}},e.prototype.mkdir=function(t,e,n){try{this.mkdirSync(t,e),n()}catch(t){n(t)}},e.prototype.readdir=function(t,e){try{e(null,this.readdirSync(t))}catch(t){e(t)}},e.prototype.chmod=function(t,e,n,r){try{this.chmodSync(t,e,n),r()}catch(t){r(t)}},e.prototype.chown=function(t,e,n,r,i){try{this.chownSync(t,e,n,r),i()}catch(t){i(t)}},e.prototype.utimes=function(t,e,n,r){try{this.utimesSync(t,e,n),r()}catch(t){r(t)}},e.prototype.link=function(t,e,n){try{this.linkSync(t,e),n()}catch(t){n(t)}},e.prototype.symlink=function(t,e,n,r){try{this.symlinkSync(t,e,n),r()}catch(t){r(t)}},e.prototype.readlink=function(t,e){try{e(null,this.readlinkSync(t))}catch(t){e(t)}},e}(rt),ot=function(){};ot.prototype.sync=function(t){t(new f(o.ENOTSUP))},ot.prototype.syncSync=function(){throw new f(o.ENOTSUP)},ot.prototype.datasync=function(t){this.sync(t)},ot.prototype.datasyncSync=function(){return this.syncSync()},ot.prototype.chown=function(t,e,n){n(new f(o.ENOTSUP))},ot.prototype.chownSync=function(t,e){throw new f(o.ENOTSUP)},ot.prototype.chmod=function(t,e){e(new f(o.ENOTSUP))},ot.prototype.chmodSync=function(t){throw new f(o.ENOTSUP)},ot.prototype.utimes=function(t,e,n){n(new f(o.ENOTSUP))},ot.prototype.utimesSync=function(t,e){throw new f(o.ENOTSUP)};var st=function(e){function n(t,n,r,i,o){if(e.call(this),this._pos=0,this._dirty=!1,this._fs=t,this._path=n,this._flag=r,this._stat=i,this._buffer=o||K(),this._stat.size!==this._buffer.length&&this._flag.isReadable())throw new Error("Invalid buffer: Buffer is "+this._buffer.length+" long, yet Stats object specifies that file is "+this._stat.size+" long.")}return e&&(n.__proto__=e),n.prototype=Object.create(e&&e.prototype),n.prototype.constructor=n,n.prototype.getBuffer=function(){return this._buffer},n.prototype.getStats=function(){return this._stat},n.prototype.getFlag=function(){return this._flag},n.prototype.getPath=function(){return this._path},n.prototype.getPos=function(){return this._flag.isAppendable()?this._stat.size:this._pos},n.prototype.advancePos=function(t){return this._pos+=t},n.prototype.setPos=function(t){return this._pos=t},n.prototype.sync=function(t){try{this.syncSync(),t()}catch(e){t(e)}},n.prototype.syncSync=function(){throw new f(o.ENOTSUP)},n.prototype.close=function(t){try{this.closeSync(),t()}catch(e){t(e)}},n.prototype.closeSync=function(){throw new f(o.ENOTSUP)},n.prototype.stat=function(t){try{t(null,d.clone(this._stat))}catch(e){t(e)}},n.prototype.statSync=function(){return d.clone(this._stat)},n.prototype.truncate=function(t,e){try{this.truncateSync(t),this._flag.isSynchronous()&&!P.getRootFS().supportsSynch()&&this.sync(e),e()}catch(t){return e(t)}},n.prototype.truncateSync=function(e){if(this._dirty=!0,!this._flag.isWriteable())throw new f(o.EPERM,"File not opened with a writeable mode.");if(this._stat.mtimeMs=Date.now(),e>this._buffer.length){var n=t.alloc(e-this._buffer.length,0);return this.writeSync(n,0,n.length,this._buffer.length),void(this._flag.isSynchronous()&&P.getRootFS().supportsSynch()&&this.syncSync())}this._stat.size=e;var r=t.alloc(e);this._buffer.copy(r,0,0,e),this._buffer=r,this._flag.isSynchronous()&&P.getRootFS().supportsSynch()&&this.syncSync()},n.prototype.write=function(t,e,n,r,i){try{i(null,this.writeSync(t,e,n,r),t)}catch(t){i(t)}},n.prototype.writeSync=function(e,n,r,i){if(this._dirty=!0,null==i&&(i=this.getPos()),!this._flag.isWriteable())throw new f(o.EPERM,"File not opened with a writeable mode.");var s=i+r;if(s>this._stat.size&&(this._stat.size=s,s>this._buffer.length)){var a=t.alloc(s);this._buffer.copy(a),this._buffer=a}var c=e.copy(this._buffer,i,n,n+r);return this._stat.mtimeMs=Date.now(),this._flag.isSynchronous()?(this.syncSync(),c):(this.setPos(i+c),c)},n.prototype.read=function(t,e,n,r,i){try{i(null,this.readSync(t,e,n,r),t)}catch(t){i(t)}},n.prototype.readSync=function(t,e,n,r){if(!this._flag.isReadable())throw new f(o.EPERM,"File not opened with a readable mode.");null==r&&(r=this.getPos()),r+n>this._stat.size&&(n=this._stat.size-r);var i=this._buffer.copy(t,e,r,r+n);return this._stat.atimeMs=Date.now(),this._pos=r+n,i},n.prototype.chmod=function(t,e){try{this.chmodSync(t),e()}catch(t){e(t)}},n.prototype.chmodSync=function(t){if(!this._fs.supportsProps())throw new f(o.ENOTSUP);this._dirty=!0,this._stat.chmod(t),this.syncSync()},n.prototype.isDirty=function(){return this._dirty},n.prototype.resetDirty=function(){this._dirty=!1},n}(ot),at=function(t){function e(e,n,r,i,o){t.call(this,e,n,r,i,o)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.sync=function(t){t()},e.prototype.syncSync=function(){},e.prototype.close=function(t){t()},e.prototype.closeSync=function(){},e}(st),ct=function(t){function e(e,n,r,i,o){t.call(this,e,n,r,i,o)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.syncSync=function(){this.isDirty()&&(this._fs._syncSync(this),this.resetDirty())},e.prototype.closeSync=function(){this.syncSync()},e}(st),ut=function(t){function e(e,n){t.call(this),this._queue=[],this._queueRunning=!1,this._isInitialized=!1,this._initializeCallbacks=[],this._sync=e,this._async=n}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.Create=function(t,n){try{var r=new e(t.sync,t.async);r._initialize((function(t){t?n(t):n(null,r)}))}catch(t){n(t)}},e.isAvailable=function(){return!0},e.prototype.getName=function(){return e.Name},e.prototype._syncSync=function(t){this._sync.writeFileSync(t.getPath(),t.getBuffer(),null,p.getFileFlag("w"),t.getStats().mode),this.enqueueOp({apiMethod:"writeFile",arguments:[t.getPath(),t.getBuffer(),null,t.getFlag(),t.getStats().mode]})},e.prototype.isReadOnly=function(){return!1},e.prototype.supportsSynch=function(){return!0},e.prototype.supportsLinks=function(){return!1},e.prototype.supportsProps=function(){return this._sync.supportsProps()&&this._async.supportsProps()},e.prototype.renameSync=function(t,e){this._sync.renameSync(t,e),this.enqueueOp({apiMethod:"rename",arguments:[t,e]})},e.prototype.statSync=function(t,e){return this._sync.statSync(t,e)},e.prototype.openSync=function(t,e,n){return this._sync.openSync(t,e,n).closeSync(),new ct(this,t,e,this._sync.statSync(t,!1),this._sync.readFileSync(t,null,p.getFileFlag("r")))},e.prototype.unlinkSync=function(t){this._sync.unlinkSync(t),this.enqueueOp({apiMethod:"unlink",arguments:[t]})},e.prototype.rmdirSync=function(t){this._sync.rmdirSync(t),this.enqueueOp({apiMethod:"rmdir",arguments:[t]})},e.prototype.mkdirSync=function(t,e){this._sync.mkdirSync(t,e),this.enqueueOp({apiMethod:"mkdir",arguments:[t,e]})},e.prototype.readdirSync=function(t){return this._sync.readdirSync(t)},e.prototype.existsSync=function(t){return this._sync.existsSync(t)},e.prototype.chmodSync=function(t,e,n){this._sync.chmodSync(t,e,n),this.enqueueOp({apiMethod:"chmod",arguments:[t,e,n]})},e.prototype.chownSync=function(t,e,n,r){this._sync.chownSync(t,e,n,r),this.enqueueOp({apiMethod:"chown",arguments:[t,e,n,r]})},e.prototype.utimesSync=function(t,e,n){this._sync.utimesSync(t,e,n),this.enqueueOp({apiMethod:"utimes",arguments:[t,e,n]})},e.prototype._initialize=function(t){var e=this,n=this._initializeCallbacks;if(this._isInitialized)t();else if(1===n.push(t)){var r=function(t,n,r){"/"!==t&&e._sync.mkdirSync(t,n),e._async.readdir(t,(function(e,n){var o=0;e?r(e):function e(s){s?r(s):o<n.length?(i(a.join(t,n[o]),e),o++):r()}()}))},i=function(t,n){e._async.stat(t,!1,(function(i,o){i?n(i):o.isDirectory()?r(t,o.mode,n):function(t,n,r){e._async.readFile(t,null,p.getFileFlag("r"),(function(i,o){if(i)r(i);else try{e._sync.writeFileSync(t,o,null,p.getFileFlag("w"),n)}catch(t){i=t}finally{r(i)}}))}(t,o.mode,n)}))};r("/",0,(function(t){e._isInitialized=!t,e._initializeCallbacks=[],n.forEach((function(e){return e(t)}))}))}},e.prototype.enqueueOp=function(t){var e=this;if(this._queue.push(t),!this._queueRunning){this._queueRunning=!0;var n=function(t){if(t)throw new Error("WARNING: File system has desynchronized. Received following error: "+t+"\n$");if(e._queue.length>0){var r=e._queue.shift(),i=r.arguments;i.push(n),e._async[r.apiMethod].apply(e._async,i)}else e._queueRunning=!1};n()}},e}(it);ut.Name="AsyncMirror",ut.Options={sync:{type:"object",description:"The synchronous file system to mirror the asynchronous file system to.",validator:function(t,e){t&&"function"==typeof t.supportsSynch&&t.supportsSynch()?e():e(new f(o.EINVAL,"'sync' option must be a file system that supports synchronous operations"))}},async:{type:"object",description:"The asynchronous file system to mirror."}};var ft="undefined"!=typeof XMLHttpRequest&&null!==XMLHttpRequest;function ht(t,e,n){var r=new XMLHttpRequest;r.open("HEAD",e,t),r.onreadystatechange=function(t){if(4===r.readyState){if(200!==r.status)return n(new f(o.EIO,"XHR HEAD error: response returned code "+r.status));try{return n(null,parseInt(r.getResponseHeader("Content-Length")||"-1",10))}catch(t){return n(new f(o.EIO,"XHR HEAD error: Could not read content-length."))}}},r.send()}var lt=function(e,n,r){var i=new XMLHttpRequest;i.open("GET",e,!0);var s=!0;switch(n){case"buffer":i.responseType="arraybuffer";break;case"json":try{i.responseType="json",s="json"===i.responseType}catch(t){s=!1}break;default:return r(new f(o.EINVAL,"Invalid download type: "+n))}i.onreadystatechange=function(e){if(4===i.readyState){if(200!==i.status)return r(new f(o.EIO,"XHR error: response returned code "+i.status));switch(n){case"buffer":return r(null,i.response?t.from(i.response):K());case"json":return r(null,s?i.response:JSON.parse(i.responseText))}}},i.send()},pt=j&&"undefined"!=typeof Blob?function(e,n){var r,i,s=new XMLHttpRequest;switch(s.open("GET",e,!1),n){case"buffer":s.responseType="arraybuffer";break;case"json":break;default:throw new f(o.EINVAL,"Invalid download type: "+n)}if(s.onreadystatechange=function(e){if(4===s.readyState)if(200===s.status)switch(n){case"buffer":r=t.from(s.response);break;case"json":r=JSON.parse(s.response)}else i=new f(o.EIO,"XHR error: response returned code "+s.status)},s.send(),i)throw i;return r}:function(e,n){var r=new XMLHttpRequest;r.open("GET",e,!1);var i=null,s=null;if(r.overrideMimeType("text/plain; charset=x-user-defined"),r.onreadystatechange=function(e){if(4===r.readyState){if(200!==r.status)return void(s=new f(o.EIO,"XHR error: response returned code "+r.status));switch(n){case"buffer":var a=r.responseText;i=t.alloc(a.length);for(var c=0;c<a.length;c++)i[c]=a.charCodeAt(c);return;case"json":return void(i=JSON.parse(r.responseText))}}},r.send(),s)throw s;return i};function dt(t){var e=-1;return ht(!1,t,(function(t,n){if(t)throw t;e=n})),e}function yt(t,e){ht(!0,t,e)}var gt="undefined"!=typeof fetch&&null!==fetch;function wt(e,n,r){var i;try{i=fetch(e)}catch(t){return r(new f(o.EINVAL,t.message))}i.then((function(e){if(!e.ok)return r(new f(o.EIO,"fetch error: response returned code "+e.status));switch(n){case"buffer":e.arrayBuffer().then((function(e){return r(null,t.from(e))})).catch((function(t){return r(new f(o.EIO,t.message))}));break;case"json":e.json().then((function(t){return r(null,t)})).catch((function(t){return r(new f(o.EIO,t.message))}));break;default:r(new f(o.EINVAL,"Invalid download type: "+n))}})).catch((function(t){return r(new f(o.EIO,t.message))}))}function mt(t,e){fetch(t,{method:"HEAD"}).then((function(t){return t.ok?e(null,parseInt(t.headers.get("Content-Length")||"-1",10)):e(new f(o.EIO,"fetch HEAD error: response returned code "+t.status))})).catch((function(t){return e(new f(o.EIO,t.message))}))}var vt=function(){this._index={},this.addPath("/",new bt)};vt.fromListing=function(t){var e=new vt,n=new bt;e._index["/"]=n;for(var r=[["",t,n]];r.length>0;){var i=void 0,o=r.pop(),s=o[0],a=o[1],c=o[2];for(var u in a)if(a.hasOwnProperty(u)){var f=a[u],h=s+"/"+u;f?(e._index[h]=i=new bt,r.push([h,f,i])):i=new _t(new d(l.FILE,-1,365)),c&&(c._ls[u]=i)}}return e},vt.fromUnpkg=function(t){var e=new vt;return e._index["/"]=function t(n,r){var i=new bt;return r.files.forEach((function(n){var r;"file"===n.type?(r=new _t(new d(l.FILE,n.size)),i._ls[a.basename(n.path)]=r):e._index[n.path]=r=t(n.path,n)})),i}(0,t),e},vt.fromJSDelivr=function(t){var e=new vt;return t.files.forEach((function(t){var n=new _t(new d(l.FILE,t.size));e.addPathFast(t.name,n)})),e},vt.prototype.fileIterator=function(t){for(var e in this._index)if(this._index.hasOwnProperty(e))for(var n=this._index[e],r=0,i=n.getListing();r<i.length;r+=1){var o=i[r],s=n.getItem(o);Et(s)&&t(s.getData(),e+"/"+o)}},vt.prototype.addPath=function(t,e){if(!e)throw new Error("Inode must be specified");if("/"!==t[0])throw new Error("Path must be absolute, got: "+t);if(this._index.hasOwnProperty(t))return this._index[t]===e;var n=this._split_path(t),r=n[0],i=n[1],o=this._index[r];return!(void 0===o&&"/"!==t&&(o=new bt,!this.addPath(r,o)))&&(!("/"!==t&&!o.addItem(i,e))&&(St(e)&&(this._index[t]=e),!0))},vt.prototype.addPathFast=function(t,e){var n=t.lastIndexOf("/"),r=0===n?"/":t.substring(0,n),i=t.substring(n+1),o=this._index[r];return void 0===o&&(o=new bt,this.addPathFast(r,o)),!!o.addItem(i,e)&&(e.isDir()&&(this._index[t]=e),!0)},vt.prototype.removePath=function(t){var e=this._split_path(t),n=e[0],r=e[1],i=this._index[n];if(void 0===i)return null;var o=i.remItem(r);if(null===o)return null;if(St(o)){for(var s=0,a=o.getListing();s<a.length;s+=1){var c=a[s];this.removePath(t+"/"+c)}"/"!==t&&delete this._index[t]}return o},vt.prototype.ls=function(t){var e=this._index[t];return void 0===e?null:e.getListing()},vt.prototype.getInode=function(t){var e=this._split_path(t),n=e[0],r=e[1],i=this._index[n];return void 0===i?null:n===t?i:i.getItem(r)},vt.prototype._split_path=function(t){var e=a.dirname(t);return[e,t.substr(e.length+("/"===e?0:1))]};var _t=function(t){this.data=t};_t.prototype.isFile=function(){return!0},_t.prototype.isDir=function(){return!1},_t.prototype.getData=function(){return this.data},_t.prototype.setData=function(t){this.data=t};var bt=function(t){void 0===t&&(t=null),this.data=t,this._ls={}};function Et(t){return!!t&&t.isFile()}function St(t){return!!t&&t.isDir()}function kt(){throw new f(o.ENOTSUP,"Synchronous HTTP download methods are not available in this environment.")}bt.prototype.isFile=function(){return!1},bt.prototype.isDir=function(){return!0},bt.prototype.getData=function(){return this.data},bt.prototype.getStats=function(){return new d(l.DIRECTORY,4096,365)},bt.prototype.getListing=function(){return Object.keys(this._ls)},bt.prototype.getItem=function(t){var e=this._ls[t];return e&&this._ls.hasOwnProperty(t)?e:null},bt.prototype.addItem=function(t,e){return!(t in this._ls)&&(this._ls[t]=e,!0)},bt.prototype.remItem=function(t){var e=this._ls[t];return void 0===e?null:(delete this._ls[t],e)};var It=function(e){function n(n,r,i,o,s){void 0===r&&(r={}),void 0===i&&(i=""),void 0===o&&(o=!1),void 0===s&&(s=!1),e.call(this),i.length>0&&"/"!==i.charAt(i.length-1)&&(i+="/"),this.prefixUrl=i,this._logReads=s,this._index=vt.fromListing(n),this._index.fileIterator((function(e,n){var i=r[n];if(void 0!==i)if("number"==typeof i)e.size=i;else if(!e.fileData){var o=new t(i);e.size=o.length,e.fileData=o}})),!gt||o&&ft?(this._requestFileAsyncInternal=lt,this._requestFileSizeAsyncInternal=yt):(this._requestFileAsyncInternal=wt,this._requestFileSizeAsyncInternal=mt),ft?(this._requestFileSyncInternal=pt,this._requestFileSizeSyncInternal=dt):(this._requestFileSyncInternal=kt,this._requestFileSizeSyncInternal=kt)}return e&&(n.__proto__=e),n.prototype=Object.create(e&&e.prototype),n.prototype.constructor=n,n.Create=function(t,e){if(void 0===t.index&&(t.index="index.json"),"string"==typeof t.index)lt(t.index,"json",(function(r,i){r?e(r):"string"==typeof t.bundle?lt(t.bundle,"json",(function(r,o){r&&console.error("Couldn't preload bundle",r),e(null,new n(i,o||{},t.baseUrl,t.preferXHR,t.logReads))})):e(null,new n(i,t.bundle||{},t.baseUrl,t.preferXHR,t.logReads))}));else{var r=t.index;"string"==typeof t.bundle?lt(t.bundle,"json",(function(i,o){i&&console.error("Couldn't preload bundle",i),e(null,new n(r,o||{},t.baseUrl,t.preferXHR,t.logReads))})):e(null,new n(r,t.bundle||{},t.baseUrl,t.preferXHR,t.logReads))}},n.isAvailable=function(){return ft||gt},n.prototype.empty=function(){this._index.fileIterator((function(t){t.fileData=null}))},n.prototype.getName=function(){return n.Name},n.prototype.diskSpace=function(t,e){e(0,0)},n.prototype.isReadOnly=function(){return!0},n.prototype.supportsLinks=function(){return!1},n.prototype.supportsProps=function(){return!1},n.prototype.supportsSynch=function(){return ft},n.prototype.logRead=function(t,e){var n=self||r;n.fileReads=n.fileReads||{},n.fileReads[t]&&"number"!=typeof n.fileReads[t]||(n.fileReads[t]=e)},n.prototype.preloadFile=function(t,e){var n=this._index.getInode(t);if(!Et(n))throw f.EISDIR(t);if(null===n)throw f.ENOENT(t);var r=n.getData();r.size=e.length,r.fileData=e},n.prototype.stat=function(t,e,n){var r,i=this,s=this._index.getInode(t);if(null===s)return n(f.ENOENT(t));Et(s)?(r=s.getData()).size<0?this._requestFileSizeAsync(t,(function(e,o){if(e)return n(e);i._logReads&&i.logRead(t,o),r.size=o,n(null,d.clone(r))})):n(null,d.clone(r)):St(s)?(r=s.getStats(),n(null,r)):n(f.FileError(o.EINVAL,t))},n.prototype.statSync=function(t,e){var n,r=this._index.getInode(t);if(null===r)throw f.ENOENT(t);if(Et(r)){if((n=r.getData()).size<0){var i=this._requestFileSizeSync(t);this._logReads&&this.logRead(t,i),n.size=i}}else{if(!St(r))throw f.FileError(o.EINVAL,t);n=r.getStats()}return n},n.prototype.open=function(t,e,n,r){if(e.isWriteable())return r(new f(o.EPERM,t));var i=this,s=this._index.getInode(t);if(null===s)return r(f.ENOENT(t));if(!Et(s))return r(f.EISDIR(t));var a=s.getData();switch(e.pathExistsAction()){case u.THROW_EXCEPTION:case u.TRUNCATE_FILE:return r(f.EEXIST(t));case u.NOP:if(a.fileData)return r(null,new at(i,t,e,d.clone(a),a.fileData));this._requestFileAsync(t,"buffer",(function(n,o){return n?r(n):(a.size=o.length,a.fileData=o,r(null,new at(i,t,e,d.clone(a),o)))}));break;default:return r(new f(o.EINVAL,"Invalid FileMode object."))}},n.prototype.openSync=function(t,e,n){if(e.isWriteable())throw new f(o.EPERM,t);var r=this._index.getInode(t);if(null===r)throw f.ENOENT(t);if(!Et(r))throw f.EISDIR(t);var i=r.getData();switch(e.pathExistsAction()){case u.THROW_EXCEPTION:case u.TRUNCATE_FILE:throw f.EEXIST(t);case u.NOP:if(i.fileData)return new at(this,t,e,d.clone(i),i.fileData);var s=this._requestFileSync(t,"buffer");return i.size=s.length,i.fileData=s,new at(this,t,e,d.clone(i),s);default:throw new f(o.EINVAL,"Invalid FileMode object.")}},n.prototype.readdir=function(t,e){try{e(null,this.readdirSync(t))}catch(t){e(t)}},n.prototype.readdirSync=function(t){var e=this._index.getInode(t);if(null===e)throw f.ENOENT(t);if(St(e))return e.getListing();throw f.ENOTDIR(t)},n.prototype.readFile=function(t,e,n,r){var i=this,o=r;this.open(t,n,420,(function(n,s){if(n)return r(n);r=function(t,e){s.close((function(n){return t||(t=n),o(t,e)}))};var a=s.getBuffer();i._logReads&&i.logRead(t,a.toString()),null===e?r(n,Y(a)):function(t,e,n){try{n(null,t.toString(e))}catch(t){n(t)}}(a,e,r)}))},n.prototype.readFileSync=function(t,e,n){var r=this.openSync(t,n,420);try{var i=r.getBuffer();return this._logReads&&this.logRead(t,i.toString()),null===e?Y(i):i.toString(e)}finally{r.closeSync()}},n.prototype._getHTTPPath=function(t){return"/"===t.charAt(0)&&(t=t.slice(1)),this.prefixUrl+t},n.prototype._requestFileAsync=function(t,e,n){this._requestFileAsyncInternal(this._getHTTPPath(t),e,n)},n.prototype._requestFileSync=function(t,e){return this._requestFileSyncInternal(this._getHTTPPath(t),e)},n.prototype._requestFileSizeAsync=function(t,e){this._requestFileSizeAsyncInternal(this._getHTTPPath(t),e)},n.prototype._requestFileSizeSync=function(t){return this._requestFileSizeSyncInternal(this._getHTTPPath(t))},n}(rt);function Nt(t,e){return null===t?e||"":t||""}It.Name="BundledHTTPRequest",It.Options={index:{type:["string","object"],optional:!0,description:"URL to a file index as a JSON file or the file index object itself, generated with the make_http_index script. Defaults to `index.json`."},bundle:{type:["string","object"],optional:!0,description:"URL to a JSON file with the files preloaded."},baseUrl:{type:"string",optional:!0,description:"Used as the URL prefix for fetched files. Default: Fetch files relative to the index."},preferXHR:{type:"boolean",optional:!0,description:"Whether to prefer XmlHttpRequest or fetch for async operations if both are available. Default: false"},logReads:{type:"boolean",optional:!0,description:"Whether to log all reads of files and put them in an object, this is useful for getting initial bundles that you can put in 'bundle' option. Values are put on `global.readFiles`. Default: false."}};var Ot=function(t){function e(e,n,r,i,o){t.call(this,e,n,r,i,o)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.sync=function(t){var e=this;if(this.isDirty()){var n=this.getBuffer();this._fs._sync(this.getPath(),n,(function(n,r){n||e.resetDirty(),t(n)}))}else t()},e.prototype.close=function(t){this.sync(t)},e.prototype.syncSync=function(){this.isDirty()&&(this._fs._syncSync(this.getPath(),this.getBuffer()),this.resetDirty())},e.prototype.closeSync=function(){this.syncSync()},e}(st),Tt=function(e){function n(t){e.call(this),this.api=t}return e&&(n.__proto__=e),n.prototype=Object.create(e&&e.prototype),n.prototype.constructor=n,n.Create=function(t,e){e(null,new n(t.api))},n.isAvailable=function(){return!0},n.prototype.getName=function(){return"CodeSandboxEditorFS"},n.prototype.isReadOnly=function(){return!1},n.prototype.supportsProps=function(){return!1},n.prototype.supportsSynch=function(){return!0},n.prototype.empty=function(t){throw new Error("Empty not supported")},n.prototype.renameSync=function(t,e){throw new Error("Rename not supported")},n.prototype.statSync=function(t,e){var n=this.api.getSandboxFs(),r=n[t];if(!r){if(Object.keys(n).filter((function(e){return e.startsWith(t.endsWith("/")?t:t+"/")||e===t})).length>0)return new d(l.DIRECTORY,0);throw f.FileError(o.ENOENT,t)}return"directory"===r.type?new d(l.DIRECTORY,4096,void 0,+new Date,+new Date(r.updatedAt),+new Date(r.insertedAt)):new d(l.FILE,Nt(r.savedCode,r.code).length,void 0,+new Date,+new Date(r.updatedAt),+new Date(r.insertedAt))},n.prototype.createFileSync=function(t,e,n){throw new Error("Create file not supported")},n.prototype.open=function(e,n,r,i){var o=this,s=this.api.getSandboxFs()[e];if(s)if("directory"===s.type){var a=new d(l.DIRECTORY,4096,void 0,+new Date,+new Date(s.updatedAt),+new Date(s.insertedAt));i(null,new Ot(this,e,n,a))}else{var c=s.isBinary,u=s.savedCode,h=s.code;if(c){var p=Nt(u,h),y=this.api.getJwt&&this.api.getJwt()&&new URL(p).origin===document.location.origin?{Authorization:"Bearer "+(this.api.getJwt&&this.api.getJwt())}:{};return void fetch(p,{headers:y}).then((function(t){return t.blob()})).then((function(r){var a=new d(l.FILE,r.size,void 0,+new Date,+new Date(s.updatedAt),+new Date(s.insertedAt));!function(e,n){if("undefined"==typeof Blob||!(e instanceof Blob))throw new Error("first argument must be a Blob");if("function"!=typeof n)throw new Error("second argument must be a function");var r=new FileReader;r.addEventListener("loadend",(function e(i){r.removeEventListener("loadend",e,!1),i.error?n(i.error):n(null,t.from(r.result))}),!1),r.readAsArrayBuffer(e)}(r,(function(t,r){t?i(t):i(void 0,new Ot(o,e,n,a,r))}))}))}var g=t.from(Nt(u,h)),w=new d(l.FILE,g.length,void 0,+new Date,+new Date(s.updatedAt),+new Date(s.insertedAt));i(null,new Ot(this,e,n,w,g))}else i(f.ENOENT(e))},n.prototype.openFileSync=function(e,n,r){var i=this.api.getSandboxFs()[e];if(!i)throw f.ENOENT(e);if("directory"===i.type){var o=new d(l.DIRECTORY,4096,void 0,+new Date,+new Date(i.updatedAt),+new Date(i.insertedAt));return new Ot(this,e,n,o)}var s=i.savedCode,a=i.code,c=t.from(Nt(s,a)),u=new d(l.FILE,c.length,void 0,+new Date,+new Date(i.updatedAt),+new Date(i.insertedAt));return new Ot(this,e,n,u,c)},n.prototype.writeFileSync=function(){},n.prototype.rmdirSync=function(t){},n.prototype.mkdirSync=function(t){},n.prototype.unlinkSync=function(t){},n.prototype.readdirSync=function(t){var e=Object.keys(this.api.getSandboxFs()),n=t.endsWith("/")?t:t+"/",r=e.filter((function(t){return t.startsWith(n)}));if(0===r.length)return[];var i=new Set,o=n.split("/").length;return r.filter((function(t){return t.split("/").length>=o})).forEach((function(t){var e=t.split("/");e.length=o,i.add(e.join("/"))})),Array.from(i).map((function(t){return t.replace(n,"")}))},n.prototype._sync=function(t,e,n){n(null,void 0)},n.prototype._syncSync=function(t,e){},n}(it);Tt.Name="CodeSandboxEditorFS",Tt.Options={api:{type:"object",description:"The CodeSandbox Editor",validator:function(t,e){t?e():e(new f(o.EINVAL,"Manager is invalid"))}}};var Ft=function(t){function e(e,n,r,i,o){t.call(this,e,n,r,i,o)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.sync=function(t){var e=this;if(this.isDirty()){var n=this.getBuffer();this._fs._sync(this.getPath(),n,(function(n,r){n||e.resetDirty(),t(n)}))}else t()},e.prototype.close=function(t){this.sync(t)},e.prototype.syncSync=function(){this.isDirty()&&(this._fs._syncSync(this.getPath(),this.getBuffer()),this.resetDirty())},e.prototype.closeSync=function(){this.syncSync()},e}(st),xt=function(e){function n(t){e.call(this),this.manager=t}return e&&(n.__proto__=e),n.prototype=Object.create(e&&e.prototype),n.prototype.constructor=n,n.Create=function(t,e){e(null,new n(t.manager))},n.isAvailable=function(){return!0},n.prototype.getName=function(){return"CodeSandboxFS"},n.prototype.isReadOnly=function(){return!1},n.prototype.supportsProps=function(){return!1},n.prototype.supportsSynch=function(){return!0},n.prototype.empty=function(t){var e=this,n=this.manager.getTranspiledModules();Object.keys(n).forEach((function(t){e.manager.removeModule(n[t].module)})),t()},n.prototype.renameSync=function(t,e){var n=this,r=this.manager.getTranspiledModules(),i=Object.keys(r).filter((function(e){return e.startsWith(t)+"/"||!1}));if(0===i.length)throw f.FileError(o.ENOENT,t);i.map((function(t){return{path:t,moduleInfo:r[t]}})).forEach((function(r){var i=r.path,o=r.moduleInfo.module;n.manager.moveModule(o,i.replace(t,e))}))},n.prototype.statSync=function(e,n){var r=this.manager.getTranspiledModules(),i=r[e];if(!i){if(Object.keys(r).filter((function(t){return t.startsWith(e.endsWith("/")?e:e+"/")||t===e})).length>0)return new d(l.DIRECTORY,0);throw f.FileError(o.ENOENT,e)}return new d(l.FILE,t.byteLength(i.module.code||"","utf8"))},n.prototype.createFileSync=function(e,n,r){if("/"===e)throw f.EEXIST(e);if(this.manager.getTranspiledModules()[e])throw f.EEXIST(e);var i={path:e,code:""};this.manager.addModule(i);var o=t.from(i.code||""),s=new d(l.FILE,o.length);return new Ft(this,e,n,s,o)},n.prototype.openFileSync=function(e,n,r){var i=this.manager.getTranspiledModules()[e];if(!i)throw f.ENOENT(e);var o=i.module.code;void 0===o&&(o="");var s=t.from(o||""),a=new d(l.FILE,s.length);return new Ft(this,e,n,a,s)},n.prototype.rmdirSync=function(t){var e=this,n=this.manager.getTranspiledModules();Object.keys(n).filter((function(e){return e.startsWith(t+"/")||t===e})).forEach((function(t){var r=n[t].module;e.manager.removeModule(r)}))},n.prototype.mkdirSync=function(t){},n.prototype.readdirSync=function(t){var e=Object.keys(this.manager.getTranspiledModules()),n=t.endsWith("/")?t:t+"/",r=e.filter((function(t){return t.startsWith(n)}));if(0===r.length)return[];var i=new Set,o=n.split("/").length;return r.filter((function(t){return t.split("/").length>=o})).forEach((function(t){var e=t.split("/");e.length=o,i.add(e.join("/"))})),Array.from(i).map((function(t){return t.replace(n,"")}))},n.prototype._sync=function(t,e,n){var r=this,i=a.dirname(t);this.stat(i,!1,(function(e,s){if(e)n(f.FileError(o.ENOENT,i));else{var a=r.manager.getTranspiledModules()[t].module;r.manager.updateModule(a),n(null)}}))},n.prototype._syncSync=function(t,e){var n=a.dirname(t);this.statSync(n,!1);var r=this.manager.getTranspiledModules()[t].module;this.manager.updateModule(r)},n}(it);function Rt(){throw new f(o.ENOTSUP,"Synchronous HTTP download methods are not available in this environment.")}xt.Name="CodeSandboxFS",xt.Options={manager:{type:"object",description:"The CodeSandbox Manager",validator:function(t,e){t?e():e(new f(o.EINVAL,"Manager is invalid"))}}};var At=function(e){function n(t,n){void 0===t&&(t=""),void 0===n&&(n=!1),e.call(this),t.length>0&&"/"!==t.charAt(t.length-1)&&(t+="/"),this.prefixUrl=t,this._requestFileAsyncInternal=!gt||n&&ft?lt:wt,this._requestFileSyncInternal=ft?pt:Rt}return e&&(n.__proto__=e),n.prototype=Object.create(e&&e.prototype),n.prototype.constructor=n,n.Create=function(t,e){e(null,new n(t.baseUrl))},n.isAvailable=function(){return ft||gt},n.prototype.convertAPIError=function(t){return new f(t.errno,t.message,t.path)},n.prototype.empty=function(){},n.prototype.getName=function(){return n.Name},n.prototype.diskSpace=function(t,e){e(0,0)},n.prototype.isReadOnly=function(){return!0},n.prototype.supportsLinks=function(){return!1},n.prototype.supportsProps=function(){return!1},n.prototype.supportsSynch=function(){return ft},n.prototype.stat=function(e,n,r){var i=this;this._requestFileAsync(e+"?stat","json",(function(e,n){e||n.error?r(e||i.convertAPIError(n.error)):r(null,d.fromBuffer(t.from(n.stats)))}))},n.prototype.statSync=function(e,n){var r=this._requestFileSync(e+"?stat","json");if(r.error)throw this.convertAPIError(r.error);return d.fromBuffer(t.from(r.stats))},n.prototype.open=function(e,n,r,i){var s=this;if(n.isWriteable())return i(new f(o.EPERM,e));var a=this;this._requestFileAsync(e,"json",(function(r,o){return r||o.error?i(r||s.convertAPIError(o.error)):i(null,new at(a,e,n,d.fromBuffer(t.from(o.stats)),t.from(o.result)))}))},n.prototype.openSync=function(e,n,r){if(n.isWriteable())throw new f(o.EPERM,e);var i=this._requestFileSync(e,"json");if(i.error)throw this.convertAPIError(i.error);return new at(this,e,n,d.fromBuffer(t.from(i.stats)),t.from(i.result))},n.prototype.readdir=function(t,e){try{e(null,this.readdirSync(t))}catch(t){e(t)}},n.prototype.readdirSync=function(t){var e=this._requestFileSync(t+"?meta","json");if(e.error)throw this.convertAPIError(e.error);return e.result},n.prototype.readFile=function(t,e,n,r){var i=r;this.open(t,n,420,(function(t,n){if(t)return r(t);r=function(t,e){n.close((function(n){return t||(t=n),i(t,e)}))};var o=n.getBuffer();null===e?r(t,Y(o)):function(t,e,n){try{n(null,t.toString(e))}catch(t){n(t)}}(o,e,r)}))},n.prototype.readFileSync=function(t,e,n){var r=this.openSync(t,n,420);try{var i=r.getBuffer();return null===e?Y(i):i.toString(e)}finally{r.closeSync()}},n.prototype._getHTTPPath=function(t){return"/"===t.charAt(0)&&(t=t.slice(1)),this.prefixUrl+t},n.prototype._requestFileAsync=function(t,e,n){this._requestFileAsyncInternal(this._getHTTPPath(t),e,n)},n.prototype._requestFileSync=function(t,e){return this._requestFileSyncInternal(this._getHTTPPath(t),e)},n}(rt);At.Name="DynamicHTTPRequest",At.Options={baseUrl:{type:"string",optional:!0,description:"Used as the URL prefix for fetched files. Default: Fetch files relative to the index."},preferXHR:{type:"boolean",optional:!0,description:"Whether to prefer XmlHttpRequest or fetch for async operations if both are available. Default: false"}};var Lt=function(t){function e(e,n){t.call(this),this._folder=e,this._wrapped=n}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.Create=function(t,n){var r=new e(t.folder,t.wrapped);r._initialize((function(t){t?n(t):n(null,r)}))},e.isAvailable=function(){return!0},e.prototype.getName=function(){return this._wrapped.getName()},e.prototype.isReadOnly=function(){return this._wrapped.isReadOnly()},e.prototype.supportsProps=function(){return this._wrapped.supportsProps()},e.prototype.supportsSynch=function(){return this._wrapped.supportsSynch()},e.prototype.supportsLinks=function(){return!1},e.prototype._initialize=function(t){var e=this;this._wrapped.exists(this._folder,(function(n){n?t():e._wrapped.isReadOnly()?t(f.ENOENT(e._folder)):e._wrapped.mkdir(e._folder,511,t)}))},e}(rt);function Dt(t,e){if(null!==e&&"object"==typeof e){var n=e,r=n.path;r&&(r="/"+a.relative(t,r),n.message=n.message.replace(n.path,r),n.path=r)}return e}function Pt(t,e){return"function"==typeof e?function(n){arguments.length>0&&(arguments[0]=Dt(t,n)),e.apply(null,arguments)}:e}function Ct(t,e,n){return"Sync"!==t.slice(t.length-4)?function(){return arguments.length>0&&(e&&(arguments[0]=a.join(this._folder,arguments[0])),n&&(arguments[1]=a.join(this._folder,arguments[1])),arguments[arguments.length-1]=Pt(this._folder,arguments[arguments.length-1])),this._wrapped[t].apply(this._wrapped,arguments)}:function(){try{return e&&(arguments[0]=a.join(this._folder,arguments[0])),n&&(arguments[1]=a.join(this._folder,arguments[1])),this._wrapped[t].apply(this._wrapped,arguments)}catch(t){throw Dt(this._folder,t)}}}function Mt(){throw new f(o.ENOTSUP,"Synchronous HTTP download methods are not available in this environment.")}Lt.Name="FolderAdapter",Lt.Options={folder:{type:"string",description:"The folder to use as the root directory"},wrapped:{type:"object",description:"The file system to wrap"}},["diskSpace","stat","statSync","open","openSync","unlink","unlinkSync","rmdir","rmdirSync","mkdir","mkdirSync","readdir","readdirSync","exists","existsSync","realpath","realpathSync","truncate","truncateSync","readFile","readFileSync","writeFile","writeFileSync","appendFile","appendFileSync","chmod","chmodSync","chown","chownSync","utimes","utimesSync","readlink","readlinkSync"].forEach((function(t){Lt.prototype[t]=Ct(t,!0,!1)})),["rename","renameSync","link","linkSync","symlink","symlinkSync"].forEach((function(t){Lt.prototype[t]=Ct(t,!0,!0)}));var Ut=function(t){function e(e,n,r){void 0===n&&(n=""),void 0===r&&(r=!1),t.call(this),n.length>0&&"/"!==n.charAt(n.length-1)&&(n+="/"),this.prefixUrl=n,this._index=vt.fromListing(e),!gt||r&&ft?(this._requestFileAsyncInternal=lt,this._requestFileSizeAsyncInternal=yt):(this._requestFileAsyncInternal=wt,this._requestFileSizeAsyncInternal=mt),ft?(this._requestFileSyncInternal=pt,this._requestFileSizeSyncInternal=dt):(this._requestFileSyncInternal=Mt,this._requestFileSizeSyncInternal=Mt)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.Create=function(t,n){void 0===t.index&&(t.index="index.json"),"string"==typeof t.index?lt(t.index,"json",(function(r,i){r?n(r):n(null,new e(i,t.baseUrl))})):n(null,new e(t.index,t.baseUrl))},e.isAvailable=function(){return ft||gt},e.prototype.empty=function(){this._index.fileIterator((function(t){t.fileData=null}))},e.prototype.getName=function(){return e.Name},e.prototype.diskSpace=function(t,e){e(0,0)},e.prototype.isReadOnly=function(){return!0},e.prototype.supportsLinks=function(){return!1},e.prototype.supportsProps=function(){return!1},e.prototype.supportsSynch=function(){return ft},e.prototype.preloadFile=function(t,e){var n=this._index.getInode(t);if(!Et(n))throw f.EISDIR(t);if(null===n)throw f.ENOENT(t);var r=n.getData();r.size=e.length,r.fileData=e},e.prototype.stat=function(t,e,n){var r,i=this._index.getInode(t);if(null===i)return n(f.ENOENT(t));Et(i)?(r=i.getData()).size<0?this._requestFileSizeAsync(t,(function(t,e){if(t)return n(t);r.size=e,n(null,d.clone(r))})):n(null,d.clone(r)):St(i)?(r=i.getStats(),n(null,r)):n(f.FileError(o.EINVAL,t))},e.prototype.statSync=function(t,e){var n,r=this._index.getInode(t);if(null===r)throw f.ENOENT(t);if(Et(r))(n=r.getData()).size<0&&(n.size=this._requestFileSizeSync(t));else{if(!St(r))throw f.FileError(o.EINVAL,t);n=r.getStats()}return n},e.prototype.open=function(t,e,n,r){if(e.isWriteable())return r(new f(o.EPERM,t));var i=this,s=this._index.getInode(t);if(null===s)return r(f.ENOENT(t));if(!Et(s))return r(f.EISDIR(t));var a=s.getData();switch(e.pathExistsAction()){case u.THROW_EXCEPTION:case u.TRUNCATE_FILE:return r(f.EEXIST(t));case u.NOP:if(a.fileData)return r(null,new at(i,t,e,d.clone(a),a.fileData));this._requestFileAsync(t,"buffer",(function(n,o){return n?r(n):(a.size=o.length,a.fileData=o,r(null,new at(i,t,e,d.clone(a),o)))}));break;default:return r(new f(o.EINVAL,"Invalid FileMode object."))}},e.prototype.openSync=function(t,e,n){if(e.isWriteable())throw new f(o.EPERM,t);var r=this._index.getInode(t);if(null===r)throw f.ENOENT(t);if(!Et(r))throw f.EISDIR(t);var i=r.getData();switch(e.pathExistsAction()){case u.THROW_EXCEPTION:case u.TRUNCATE_FILE:throw f.EEXIST(t);case u.NOP:if(i.fileData)return new at(this,t,e,d.clone(i),i.fileData);var s=this._requestFileSync(t,"buffer");return i.size=s.length,i.fileData=s,new at(this,t,e,d.clone(i),s);default:throw new f(o.EINVAL,"Invalid FileMode object.")}},e.prototype.readdir=function(t,e){try{e(null,this.readdirSync(t))}catch(t){e(t)}},e.prototype.readdirSync=function(t){var e=this._index.getInode(t);if(null===e)throw f.ENOENT(t);if(St(e))return e.getListing();throw f.ENOTDIR(t)},e.prototype.readFile=function(t,e,n,r){var i=r;this.open(t,n,420,(function(t,n){if(t)return r(t);r=function(t,e){n.close((function(n){return t||(t=n),i(t,e)}))};var o=n.getBuffer();null===e?r(t,Y(o)):function(t,e,n){try{n(null,t.toString(e))}catch(t){n(t)}}(o,e,r)}))},e.prototype.readFileSync=function(t,e,n){var r=this.openSync(t,n,420);try{var i=r.getBuffer();return null===e?Y(i):i.toString(e)}finally{r.closeSync()}},e.prototype._getHTTPPath=function(t){return"/"===t.charAt(0)&&(t=t.slice(1)),this.prefixUrl+t},e.prototype._requestFileAsync=function(t,e,n){this._requestFileAsyncInternal(this._getHTTPPath(t),e,n)},e.prototype._requestFileSync=function(t,e){return this._requestFileSyncInternal(this._getHTTPPath(t),e)},e.prototype._requestFileSizeAsync=function(t,e){this._requestFileSizeAsyncInternal(this._getHTTPPath(t),e)},e.prototype._requestFileSizeSync=function(t){return this._requestFileSizeSyncInternal(this._getHTTPPath(t))},e}(rt);Ut.Name="HTTPRequest",Ut.Options={index:{type:["string","object"],optional:!0,description:"URL to a file index as a JSON file or the file index object itself, generated with the make_http_index script. Defaults to `index.json`."},baseUrl:{type:"string",optional:!0,description:"Used as the URL prefix for fetched files. Default: Fetch files relative to the index."},preferXHR:{type:"boolean",optional:!0,description:"Whether to prefer XmlHttpRequest or fetch for async operations if both are available. Default: false"}};var jt=function(t,e,n,r,i,o){this.id=t,this.size=e,this.mode=n,this.atime=r,this.mtime=i,this.ctime=o};jt.fromBuffer=function(t){if(void 0===t)throw new Error("NO");return new jt(t.toString("ascii",30),t.readUInt32LE(0),t.readUInt16LE(4),t.readDoubleLE(6),t.readDoubleLE(14),t.readDoubleLE(22))},jt.prototype.toStats=function(){return new d((61440&this.mode)===l.DIRECTORY?l.DIRECTORY:l.FILE,this.size,this.mode,this.atime,this.mtime,this.ctime)},jt.prototype.getSize=function(){return 30+this.id.length},jt.prototype.toBuffer=function(e){return void 0===e&&(e=t.alloc(this.getSize())),e.writeUInt32LE(this.size,0),e.writeUInt16LE(this.mode,4),e.writeDoubleLE(this.atime,6),e.writeDoubleLE(this.mtime,14),e.writeDoubleLE(this.ctime,22),e.write(this.id,30,this.id.length,"ascii"),e},jt.prototype.update=function(t){var e=!1;this.size!==t.size&&(this.size=t.size,e=!0),this.mode!==t.mode&&(this.mode=t.mode,e=!0);var n=t.atime.getTime();this.atime!==n&&(this.atime=n,e=!0);var r=t.mtime.getTime();this.mtime!==r&&(this.mtime=r,e=!0);var i=t.ctime.getTime();return this.ctime!==i&&(this.ctime=i,e=!0),e},jt.prototype.isFile=function(){return(61440&this.mode)===l.FILE},jt.prototype.isDirectory=function(){return(61440&this.mode)===l.DIRECTORY};var Bt=null;function zt(){return Bt||(Bt=t.from("{}"))}function qt(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){var e=16*Math.random()|0;return("x"===t?e:3&e|8).toString(16)}))}function Wt(t,e){return!t||(e(t),!1)}function Ht(t,e,n){return!t||(e.abort((function(){n(t)})),!1)}var Vt=function(t,e){this.key=t,this.value=e,this.prev=null,this.next=null},Xt=function(t){this.limit=t,this.size=0,this.map={},this.head=null,this.tail=null};Xt.prototype.set=function(t,e){var n=new Vt(t,e);this.map[t]?(this.map[t].value=n.value,this.remove(n.key)):this.size>=this.limit&&(delete this.map[this.tail.key],this.size--,this.tail=this.tail.prev,this.tail.next=null),this.setHead(n)},Xt.prototype.get=function(t){if(this.map[t]){var e=this.map[t].value,n=new Vt(t,e);return this.remove(t),this.setHead(n),e}return null},Xt.prototype.remove=function(t){var e=this.map[t];e&&(null!==e.prev?e.prev.next=e.next:this.head=e.next,null!==e.next?e.next.prev=e.prev:this.tail=e.prev,delete this.map[t],this.size--)},Xt.prototype.removeAll=function(){this.size=0,this.map={},this.head=null,this.tail=null},Xt.prototype.setHead=function(t){t.next=this.head,t.prev=null,null!==this.head&&(this.head.prev=t),this.head=t,null===this.tail&&(this.tail=t),this.size++,this.map[t.key]=t};var Zt=function(t){this.store=t,this.originalData={},this.modifiedKeys=[]};Zt.prototype.get=function(t){var e=this.store.get(t);return this.stashOldValue(t,e),e},Zt.prototype.put=function(t,e,n){return this.markModified(t),this.store.put(t,e,n)},Zt.prototype.del=function(t){this.markModified(t),this.store.del(t)},Zt.prototype.commit=function(){},Zt.prototype.abort=function(){for(var t=0,e=this.modifiedKeys;t<e.length;t+=1){var n=e[t],r=this.originalData[n];r?this.store.put(n,r,!0):this.store.del(n)}},Zt.prototype.stashOldValue=function(t,e){this.originalData.hasOwnProperty(t)||(this.originalData[t]=e)},Zt.prototype.markModified=function(t){-1===this.modifiedKeys.indexOf(t)&&(this.modifiedKeys.push(t),this.originalData.hasOwnProperty(t)||(this.originalData[t]=this.store.get(t)))};var Yt=function(t){function e(e,n,r,i,o){t.call(this,e,n,r,i,o)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.syncSync=function(){this.isDirty()&&(this._fs._syncSync(this.getPath(),this.getBuffer(),this.getStats()),this.resetDirty())},e.prototype.closeSync=function(){this.syncSync()},e}(st),Jt=function(e){function n(t){e.call(this),this.store=t.store,this.makeRootDirectory()}return e&&(n.__proto__=e),n.prototype=Object.create(e&&e.prototype),n.prototype.constructor=n,n.isAvailable=function(){return!0},n.prototype.getName=function(){return this.store.name()},n.prototype.isReadOnly=function(){return!1},n.prototype.supportsSymlinks=function(){return!1},n.prototype.supportsProps=function(){return!1},n.prototype.supportsSynch=function(){return!0},n.prototype.empty=function(){this.store.clear(),this.makeRootDirectory()},n.prototype.renameSync=function(e,n){var r=this.store.beginTransaction("readwrite"),i=a.dirname(e),s=a.basename(e),c=a.dirname(n),u=a.basename(n),h=this.findINode(r,i),l=this.getDirListing(r,i,h);if(!l[s])throw f.ENOENT(e);var p,d,y=l[s];if(delete l[s],0===(c+"/").indexOf(e+"/"))throw new f(o.EBUSY,i);if(c===i?(p=h,d=l):(p=this.findINode(r,c),d=this.getDirListing(r,c,p)),d[u]){var g=this.getINode(r,n,d[u]);if(!g.isFile())throw f.EPERM(n);try{r.del(g.id),r.del(d[u])}catch(t){throw r.abort(),t}}d[u]=y;try{r.put(h.id,t.from(JSON.stringify(l)),!0),r.put(p.id,t.from(JSON.stringify(d)),!0)}catch(t){throw r.abort(),t}r.commit()},n.prototype.statSync=function(t,e){return this.findINode(this.store.beginTransaction("readonly"),t).toStats()},n.prototype.createFileSync=function(t,e,n){var r=this.store.beginTransaction("readwrite"),i=K(),o=this.commitNewFile(r,t,l.FILE,n,i);return new Yt(this,t,e,o.toStats(),i)},n.prototype.openFileSync=function(t,e){var n=this.store.beginTransaction("readonly"),r=this.findINode(n,t),i=n.get(r.id);if(void 0===i)throw f.ENOENT(t);return new Yt(this,t,e,r.toStats(),i)},n.prototype.unlinkSync=function(t){this.removeEntry(t,!1)},n.prototype.rmdirSync=function(t){if(this.readdirSync(t).length>0)throw f.ENOTEMPTY(t);this.removeEntry(t,!0)},n.prototype.mkdirSync=function(e,n){var r=this.store.beginTransaction("readwrite"),i=t.from("{}");this.commitNewFile(r,e,l.DIRECTORY,n,i)},n.prototype.readdirSync=function(t){var e=this.store.beginTransaction("readonly");return Object.keys(this.getDirListing(e,t,this.findINode(e,t)))},n.prototype._syncSync=function(t,e,n){var r=this.store.beginTransaction("readwrite"),i=this._findINode(r,a.dirname(t),a.basename(t)),o=this.getINode(r,t,i),s=o.update(n);try{r.put(o.id,e,!0),s&&r.put(i,o.toBuffer(),!0)}catch(t){throw r.abort(),t}r.commit()},n.prototype.makeRootDirectory=function(){var t=this.store.beginTransaction("readwrite");if(void 0===t.get("/")){var e=(new Date).getTime(),n=new jt(qt(),4096,511|l.DIRECTORY,e,e,e);t.put(n.id,zt(),!1),t.put("/",n.toBuffer(),!1),t.commit()}},n.prototype._findINode=function(t,e,n){var r=this,i=function(i){var o=r.getDirListing(t,e,i);if(o[n])return o[n];throw f.ENOENT(a.resolve(e,n))};return"/"===e?""===n?"/":i(this.getINode(t,e,"/")):i(this.getINode(t,e+a.sep+n,this._findINode(t,a.dirname(e),a.basename(e))))},n.prototype.findINode=function(t,e){return this.getINode(t,e,this._findINode(t,a.dirname(e),a.basename(e)))},n.prototype.getINode=function(t,e,n){var r=t.get(n);if(void 0===r)throw f.ENOENT(e);return jt.fromBuffer(r)},n.prototype.getDirListing=function(t,e,n){if(!n.isDirectory())throw f.ENOTDIR(e);var r=t.get(n.id);if(void 0===r)throw f.ENOENT(e);return JSON.parse(r.toString())},n.prototype.addNewNode=function(t,e){for(var n;;)try{return n=qt(),t.put(n,e,!1),n}catch(t){}throw new f(o.EIO,"Unable to commit data to key-value store.")},n.prototype.commitNewFile=function(e,n,r,i,o){var s,c=a.dirname(n),u=a.basename(n),h=this.findINode(e,c),l=this.getDirListing(e,c,h),p=(new Date).getTime();if("/"===n)throw f.EEXIST(n);if(l[u])throw f.EEXIST(n);try{var d=this.addNewNode(e,o);s=new jt(d,o.length,i|r,p,p,p);var y=this.addNewNode(e,s.toBuffer());l[u]=y,e.put(h.id,t.from(JSON.stringify(l)),!0)}catch(t){throw e.abort(),t}return e.commit(),s},n.prototype.removeEntry=function(e,n){var r=this.store.beginTransaction("readwrite"),i=a.dirname(e),o=this.findINode(r,i),s=this.getDirListing(r,i,o),c=a.basename(e);if(!s[c])throw f.ENOENT(e);var u=s[c];delete s[c];var h=this.getINode(r,e,u);if(!n&&h.isDirectory())throw f.EISDIR(e);if(n&&!h.isDirectory())throw f.ENOTDIR(e);try{r.del(h.id),r.del(u),r.put(o.id,t.from(JSON.stringify(s)),!0)}catch(t){throw r.abort(),t}r.commit()},n}(it),Kt=function(t){function e(e,n,r,i,o){t.call(this,e,n,r,i,o)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.sync=function(t){var e=this;this.isDirty()?this._fs._sync(this.getPath(),this.getBuffer(),this.getStats(),(function(n){n||e.resetDirty(),t(n)})):t()},e.prototype.close=function(t){this.sync(t)},e}(st),Gt=function(e){function n(t){e.call(this),this._cache=null,t>0&&(this._cache=new Xt(t))}return e&&(n.__proto__=e),n.prototype=Object.create(e&&e.prototype),n.prototype.constructor=n,n.isAvailable=function(){return!0},n.prototype.init=function(t,e){this.store=t,this.makeRootDirectory(e)},n.prototype.getName=function(){return this.store.name()},n.prototype.isReadOnly=function(){return!1},n.prototype.supportsSymlinks=function(){return!1},n.prototype.supportsProps=function(){return!1},n.prototype.supportsSynch=function(){return!1},n.prototype.empty=function(t){var e=this;this._cache&&this._cache.removeAll(),this.store.clear((function(n){Wt(n,t)&&e.makeRootDirectory(t)}))},n.prototype.rename=function(e,n,r){var i=this;if(this._cache){var s=this._cache;this._cache=null,s.removeAll();var c=r;r=function(t){i._cache=s,c(t)}}var u=this.store.beginTransaction("readwrite"),h=a.dirname(e),l=a.basename(e),p=a.dirname(n),d=a.basename(n),y={},g={},w=!1;if(0===(p+"/").indexOf(e+"/"))return r(new f(o.EBUSY,h));var m=function(o){i.findINodeAndDirListing(u,o,(function(s,a,c){s?w||(w=!0,u.abort((function(){r(s)}))):(y[o]=a,g[o]=c,function(){if(!w&&g.hasOwnProperty(h)&&g.hasOwnProperty(p)){var o=g[h],s=y[h],a=g[p],c=y[p];if(o[l]){var m=o[l];delete o[l];var v=function(){a[d]=m,u.put(s.id,t.from(JSON.stringify(o)),!0,(function(e){Ht(e,u,r)&&(h===p?u.commit(r):u.put(c.id,t.from(JSON.stringify(a)),!0,(function(t){Ht(t,u,r)&&u.commit(r)})))}))};a[d]?i.getINode(u,n,a[d],(function(t,e){Ht(t,u,r)&&(e.isFile()?u.del(e.id,(function(t){Ht(t,u,r)&&u.del(a[d],(function(t){Ht(t,u,r)&&v()}))})):u.abort((function(t){r(f.EPERM(n))})))})):v()}else r(f.ENOENT(e))}}())}))};m(h),h!==p&&m(p)},n.prototype.stat=function(t,e,n){var r=this.store.beginTransaction("readonly");this.findINode(r,t,(function(t,e){Wt(t,n)&&n(null,e.toStats())}))},n.prototype.createFile=function(t,e,n,r){var i=this,o=this.store.beginTransaction("readwrite"),s=K();this.commitNewFile(o,t,l.FILE,n,s,(function(n,o){Wt(n,r)&&r(null,new Kt(i,t,e,o.toStats(),s))}))},n.prototype.openFile=function(t,e,n){var r=this,i=this.store.beginTransaction("readonly");this.findINode(i,t,(function(o,s){Wt(o,n)&&i.get(s.id,(function(i,o){Wt(i,n)&&(void 0===o?n(f.ENOENT(t)):n(null,new Kt(r,t,e,s.toStats(),o)))}))}))},n.prototype.unlink=function(t,e){this.removeEntry(t,!1,e)},n.prototype.rmdir=function(t,e){var n=this;this.readdir(t,(function(r,i){r?e(r):i.length>0?e(f.ENOTEMPTY(t)):n.removeEntry(t,!0,e)}))},n.prototype.mkdir=function(e,n,r){var i=this.store.beginTransaction("readwrite"),o=t.from("{}");this.commitNewFile(i,e,l.DIRECTORY,n,o,r)},n.prototype.readdir=function(t,e){var n=this,r=this.store.beginTransaction("readonly");this.findINode(r,t,(function(i,o){Wt(i,e)&&n.getDirListing(r,t,o,(function(t,n){Wt(t,e)&&e(null,Object.keys(n))}))}))},n.prototype._sync=function(t,e,n,r){var i=this,o=this.store.beginTransaction("readwrite");this._findINode(o,a.dirname(t),a.basename(t),(function(s,a){Ht(s,o,r)&&i.getINode(o,t,a,(function(t,i){if(Ht(t,o,r)){var s=i.update(n);o.put(i.id,e,!0,(function(t){Ht(t,o,r)&&(s?o.put(a,i.toBuffer(),!0,(function(t){Ht(t,o,r)&&o.commit(r)})):o.commit(r))}))}}))}))},n.prototype.makeRootDirectory=function(t){var e=this.store.beginTransaction("readwrite");e.get("/",(function(n,r){if(n||void 0===r){var i=(new Date).getTime(),o=new jt(qt(),4096,511|l.DIRECTORY,i,i,i);e.put(o.id,zt(),!1,(function(n){Ht(n,e,t)&&e.put("/",o.toBuffer(),!1,(function(n){n?e.abort((function(){t(n)})):e.commit(t)}))}))}else e.commit(t)}))},n.prototype._findINode=function(t,e,n,r){var i=this;if(this._cache){var o=this._cache.get(a.join(e,n));if(o)return r(null,o)}var s=function(t,o,s){if(t)r(t);else if(s[n]){var c=s[n];i._cache&&i._cache.set(a.join(e,n),c),r(null,c)}else r(f.ENOENT(a.resolve(e,n)))};"/"===e?""===n?(this._cache&&this._cache.set(a.join(e,n),"/"),r(null,"/")):this.getINode(t,e,"/",(function(n,o){Wt(n,r)&&i.getDirListing(t,e,o,(function(t,e){s(t,0,e)}))})):this.findINodeAndDirListing(t,e,s)},n.prototype.findINode=function(t,e,n){var r=this;this._findINode(t,a.dirname(e),a.basename(e),(function(i,o){Wt(i,n)&&r.getINode(t,e,o,n)}))},n.prototype.getINode=function(t,e,n,r){t.get(n,(function(t,n){Wt(t,r)&&(void 0===n?r(f.ENOENT(e)):r(null,jt.fromBuffer(n)))}))},n.prototype.getDirListing=function(t,e,n,r){n.isDirectory()?t.get(n.id,(function(t,n){if(Wt(t,r))try{r(null,JSON.parse(n.toString()))}catch(t){r(f.ENOENT(e))}})):r(f.ENOTDIR(e))},n.prototype.findINodeAndDirListing=function(t,e,n){var r=this;this.findINode(t,e,(function(i,o){Wt(i,n)&&r.getDirListing(t,e,o,(function(t,e){Wt(t,n)&&n(null,o,e)}))}))},n.prototype.addNewNode=function(t,e,n){var r,i=0,s=function(){5==++i?n(new f(o.EIO,"Unable to commit data to key-value store.")):(r=qt(),t.put(r,e,!1,(function(t,e){t||!e?s():n(null,r)})))};s()},n.prototype.commitNewFile=function(e,n,r,i,o,s){var c=this,u=a.dirname(n),h=a.basename(n),l=(new Date).getTime();if("/"===n)return s(f.EEXIST(n));this.findINodeAndDirListing(e,u,(function(a,u,p){Ht(a,e,s)&&(p[h]?e.abort((function(){s(f.EEXIST(n))})):c.addNewNode(e,o,(function(n,a){if(Ht(n,e,s)){var f=new jt(a,o.length,i|r,l,l,l);c.addNewNode(e,f.toBuffer(),(function(n,r){Ht(n,e,s)&&(p[h]=r,e.put(u.id,t.from(JSON.stringify(p)),!0,(function(t){Ht(t,e,s)&&e.commit((function(t){Ht(t,e,s)&&s(null,f)}))})))}))}})))}))},n.prototype.removeEntry=function(e,n,r){var i=this;this._cache&&this._cache.remove(e);var o=this.store.beginTransaction("readwrite"),s=a.dirname(e),c=a.basename(e);this.findINodeAndDirListing(o,s,(function(s,a,u){if(Ht(s,o,r))if(u[c]){var h=u[c];delete u[c],i.getINode(o,e,h,(function(i,s){Ht(i,o,r)&&(!n&&s.isDirectory()?o.abort((function(){r(f.EISDIR(e))})):n&&!s.isDirectory()?o.abort((function(){r(f.ENOTDIR(e))})):o.del(s.id,(function(e){Ht(e,o,r)&&o.del(h,(function(e){Ht(e,o,r)&&o.put(a.id,t.from(JSON.stringify(u)),!0,(function(t){Ht(t,o,r)&&o.commit(r)}))}))})))}))}else o.abort((function(){r(f.ENOENT(e))}))}))},n}(rt),Qt=w.indexedDB||w.mozIndexedDB||w.webkitIndexedDB||w.msIndexedDB;function $t(t,e){switch(void 0===e&&(e=t.toString()),t.name){case"NotFoundError":return new f(o.ENOENT,e);case"QuotaExceededError":return new f(o.ENOSPC,e);default:return new f(o.EIO,e)}}function te(t,e,n){return void 0===e&&(e=o.EIO),void 0===n&&(n=null),function(r){r.preventDefault(),t(new f(e,null!==n?n:void 0))}}var ee=function(t,e){this.tx=t,this.store=e};ee.prototype.get=function(t,e){try{var n=this.store.get(t);n.onerror=te(e),n.onsuccess=function(t){var n=t.target.result;e(null,void 0===n?n:Z(n))}}catch(t){e($t(t))}};var ne=function(t){function e(e,n){t.call(this,e,n)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.put=function(t,e,n,r){try{var i,o=W(e);(i=n?this.store.put(o,t):this.store.add(o,t)).onerror=te(r),i.onsuccess=function(t){r(null,!0)}}catch(t){r($t(t))}},e.prototype.del=function(t,e){try{var n=this.store.delete(t);n.onerror=te(e),n.onsuccess=function(t){e()}}catch(t){e($t(t))}},e.prototype.commit=function(t){setTimeout(t,0)},e.prototype.abort=function(t){var e=null;try{this.tx.abort()}catch(t){e=$t(t)}finally{t(e)}},e}(ee),re=function(t,e){this.db=t,this.storeName=e};re.Create=function(t,e){var n=Qt.open(t,1);n.onupgradeneeded=function(e){var n=e.target.result;n.objectStoreNames.contains(t)&&n.deleteObjectStore(t),n.createObjectStore(t)},n.onsuccess=function(n){e(null,new re(n.target.result,t))},n.onerror=te(e,o.EACCES)},re.prototype.name=function(){return ie.Name+" - "+this.storeName},re.prototype.clear=function(t){try{var e=this.db.transaction(this.storeName,"readwrite").objectStore(this.storeName).clear();e.onsuccess=function(e){setTimeout(t,0)},e.onerror=te(t)}catch(e){t($t(e))}},re.prototype.beginTransaction=function(t){void 0===t&&(t="readonly");var e=this.db.transaction(this.storeName,t),n=e.objectStore(this.storeName);if("readwrite"===t)return new ne(e,n);if("readonly"===t)return new ee(e,n);throw new f(o.EINVAL,"Invalid transaction type.")};var ie=function(t){function e(e){t.call(this,e)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.Create=function(t,n){re.Create(t.storeName?t.storeName:"browserfs",(function(r,i){if(i){var o=new e("number"==typeof t.cacheSize?t.cacheSize:100);o.init(i,(function(t){t?n(t):n(null,o)}))}else n(r)}))},e.isAvailable=function(){try{return void 0!==Qt&&null!==Qt.open("__browserfs_test__")}catch(t){return!1}},e}(Gt);ie.Name="IndexedDB",ie.Options={storeName:{type:"string",optional:!0,description:"The name of this file system. You can have multiple IndexedDB file systems operating at once, but each must have a different name."},cacheSize:{type:"number",optional:!0,description:"The size of the inode cache. Defaults to 100. A size of 0 or below disables caching."}};var oe=function(){this.store={}};oe.prototype.name=function(){return se.Name},oe.prototype.clear=function(){this.store={}},oe.prototype.beginTransaction=function(t){return new Zt(this)},oe.prototype.get=function(t){return this.store[t]},oe.prototype.put=function(t,e,n){return!(!n&&this.store.hasOwnProperty(t))&&(this.store[t]=e,!0)},oe.prototype.del=function(t){delete this.store[t]};var se=function(t){function e(){t.call(this,{store:new oe})}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.Create=function(t,n){n(null,new e)},e}(Jt);se.Name="InMemory",se.Options={};var ae,ce=!1;try{w.localStorage.setItem("__test__",String.fromCharCode(55296)),ce=w.localStorage.getItem("__test__")===String.fromCharCode(55296)}catch(t){ce=!1}ae=ce?"binary_string":"binary_string_ie",t.isEncoding(ae)||(ae="base64");var ue=function(){};ue.prototype.name=function(){return fe.Name},ue.prototype.clear=function(){w.localStorage.clear()},ue.prototype.beginTransaction=function(t){return new Zt(this)},ue.prototype.get=function(e){try{var n=w.localStorage.getItem(e);if(null!==n)return t.from(n,ae)}catch(t){}},ue.prototype.put=function(t,e,n){try{return!(!n&&null!==w.localStorage.getItem(t))&&(w.localStorage.setItem(t,e.toString(ae)),!0)}catch(t){throw new f(o.ENOSPC,"LocalStorage is full.")}},ue.prototype.del=function(t){try{w.localStorage.removeItem(t)}catch(e){throw new f(o.EIO,"Unable to delete key "+t+": "+e)}};var fe=function(t){function e(){t.call(this,{store:new ue})}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.Create=function(t,n){n(null,new e)},e.isAvailable=function(){return void 0!==w.localStorage},e}(Jt);fe.Name="LocalStorage",fe.Options={};var he=function(t){function e(e){t.call(this),this.mountList=[],this.mntMap={},this.rootFs=e}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.Create=function(t,n){se.Create({},(function(r,i){if(i){var o=new e(i);try{Object.keys(t).forEach((function(e){o.mount(e,t[e])}))}catch(r){return n(r)}n(null,o)}else n(r)}))},e.isAvailable=function(){return!0},e.prototype.mount=function(t,e){if("/"!==t[0]&&(t="/"+t),t=a.resolve(t),this.mntMap[t])throw new f(o.EINVAL,"Mount point "+t+" is already taken.");q(t,511,this.rootFs),this.mntMap[t]=e,this.mountList.push(t),this.mountList=this.mountList.sort((function(t,e){return e.length-t.length}))},e.prototype.umount=function(t){if("/"!==t[0]&&(t="/"+t),t=a.resolve(t),!this.mntMap[t])throw new f(o.EINVAL,"Mount point "+t+" is already unmounted.");for(delete this.mntMap[t],this.mountList.splice(this.mountList.indexOf(t),1);"/"!==t&&0===this.rootFs.readdirSync(t).length;)this.rootFs.rmdirSync(t),t=a.dirname(t)},e.prototype._getFs=function(t){for(var e=this.mountList,n=e.length,r=0;r<n;r++){var i=e[r];if(i.length<=t.length&&0===t.indexOf(i))return""===(t=t.substr(i.length>1?i.length:0))&&(t="/"),{fs:this.mntMap[i],path:t,mountPoint:i}}return{fs:this.rootFs,path:t,mountPoint:"/"}},e.prototype.getName=function(){return e.Name},e.prototype.diskSpace=function(t,e){e(0,0)},e.prototype.isReadOnly=function(){return!1},e.prototype.supportsLinks=function(){return!1},e.prototype.supportsProps=function(){return!1},e.prototype.supportsSynch=function(){return!0},e.prototype.standardizeError=function(t,e,n){var r=t.message.indexOf(e);return-1!==r&&(t.message=t.message.substr(0,r)+n+t.message.substr(r+e.length),t.path=n),t},e.prototype.rename=function(t,e,n){var r=this,i=this._getFs(t),o=this._getFs(e);return i.fs===o.fs?i.fs.rename(i.path,o.path,(function(s){s&&r.standardizeError(r.standardizeError(s,i.path,t),o.path,e),n(s)})):P.readFile(t,(function(r,i){if(r)return n(r);P.writeFile(e,i,(function(e){if(e)return n(e);P.unlink(t,n)}))}))},e.prototype.renameSync=function(t,e){var n=this._getFs(t),r=this._getFs(e);if(n.fs===r.fs)try{return n.fs.renameSync(n.path,r.path)}catch(i){throw this.standardizeError(this.standardizeError(i,n.path,t),r.path,e),i}var i=P.readFileSync(t);return P.writeFileSync(e,i),P.unlinkSync(t)},e.prototype.readdirSync=function(t){var e=this._getFs(t),n=null;if(e.fs!==this.rootFs)try{n=this.rootFs.readdirSync(t)}catch(t){}try{var r=e.fs.readdirSync(e.path);return null===n?r:r.concat(n.filter((function(t){return-1===r.indexOf(t)})))}catch(r){if(null===n)throw this.standardizeError(r,e.path,t);return n}},e.prototype.readdir=function(t,e){var n=this,r=this._getFs(t);r.fs.readdir(r.path,(function(i,o){if(r.fs!==n.rootFs)try{var s=n.rootFs.readdirSync(t);o=o?o.concat(s.filter((function(t){return-1===o.indexOf(t)}))):s}catch(o){if(i)return e(n.standardizeError(i,r.path,t))}else if(i)return e(n.standardizeError(i,r.path,t));e(null,o)}))},e.prototype.realpathSync=function(t,e){var n=this._getFs(t);try{var r=n.fs.realpathSync(n.path,{});return a.resolve(a.join(n.mountPoint,r))}catch(e){throw this.standardizeError(e,n.path,t)}},e.prototype.realpath=function(t,e,n){var r=this,i=this._getFs(t);i.fs.realpath(i.path,{},(function(e,o){e?n(r.standardizeError(e,i.path,t)):n(null,a.resolve(a.join(i.mountPoint,o)))}))},e.prototype.rmdirSync=function(t){var e=this._getFs(t);if(this._containsMountPt(t))throw f.ENOTEMPTY(t);try{e.fs.rmdirSync(e.path)}catch(n){throw this.standardizeError(n,e.path,t)}},e.prototype.rmdir=function(t,e){var n=this,r=this._getFs(t);this._containsMountPt(t)?e(f.ENOTEMPTY(t)):r.fs.rmdir(r.path,(function(i){e(i?n.standardizeError(i,r.path,t):null)}))},e.prototype._containsMountPt=function(t){for(var e=this.mountList,n=e.length,r=0;r<n;r++){var i=e[r];if(i.length>=t.length&&i.slice(0,t.length)===t)return!0}return!1},e}(rt);function le(t,e,n){return e?function(){for(var e=[],n=arguments.length;n--;)e[n]=arguments[n];var r=e[0],i=this._getFs(r);e[0]=i.path;try{return i.fs[t].apply(i.fs,e)}catch(t){throw this.standardizeError(t,i.path,r),t}}:function(){for(var e=this,n=[],r=arguments.length;r--;)n[r]=arguments[r];var i=n[0],o=this._getFs(i);if(n[0]=o.path,"function"==typeof n[n.length-1]){var s=n[n.length-1];n[n.length-1]=function(){for(var t=[],n=arguments.length;n--;)t[n]=arguments[n];t.length>0&&t[0]instanceof f&&e.standardizeError(t[0],o.path,i),s.apply(null,t)}}return o.fs[t].apply(o.fs,n)}}he.Name="MountableFileSystem",he.Options={};for(var pe=[["exists","unlink","readlink"],["stat","mkdir","truncate"],["open","readFile","chmod","utimes"],["chown"],["writeFile","appendFile"]],de=0;de<pe.length;de++)for(var ye=0,ge=pe[de];ye<ge.length;ye+=1){var we=ge[ye];he.prototype[we]=le(we,!1),he.prototype[we+"Sync"]=le(we+"Sync",!0)}var me=function(){this._locked=!1,this._waiters=[]};me.prototype.lock=function(t){this._locked?this._waiters.push(t):(this._locked=!0,t())},me.prototype.unlock=function(){if(!this._locked)throw new Error("unlock of a non-locked mutex");var t=this._waiters.shift();t?E(t):this._locked=!1},me.prototype.tryLock=function(){return!this._locked&&(this._locked=!0,!0)},me.prototype.isLocked=function(){return this._locked};var ve=function(t){this._fs=t,this._mu=new me};ve.prototype.getName=function(){return"LockedFS<"+this._fs.getName()+">"},ve.prototype.getFSUnlocked=function(){return this._fs},ve.prototype.diskSpace=function(t,e){this._fs.diskSpace(t,e)},ve.prototype.isReadOnly=function(){return this._fs.isReadOnly()},ve.prototype.supportsLinks=function(){return this._fs.supportsLinks()},ve.prototype.supportsProps=function(){return this._fs.supportsProps()},ve.prototype.supportsSynch=function(){return this._fs.supportsSynch()},ve.prototype.rename=function(t,e,n){var r=this;this._mu.lock((function(){r._fs.rename(t,e,(function(t){r._mu.unlock(),n(t)}))}))},ve.prototype.renameSync=function(t,e){if(this._mu.isLocked())throw new Error("invalid sync call");return this._fs.renameSync(t,e)},ve.prototype.stat=function(t,e,n){var r=this;this._mu.lock((function(){r._fs.stat(t,e,(function(t,e){r._mu.unlock(),n(t,e)}))}))},ve.prototype.statSync=function(t,e){if(this._mu.isLocked())throw new Error("invalid sync call");return this._fs.statSync(t,e)},ve.prototype.open=function(t,e,n,r){var i=this;this._mu.lock((function(){i._fs.open(t,e,n,(function(t,e){i._mu.unlock(),r(t,e)}))}))},ve.prototype.openSync=function(t,e,n){if(this._mu.isLocked())throw new Error("invalid sync call");return this._fs.openSync(t,e,n)},ve.prototype.unlink=function(t,e){var n=this;this._mu.lock((function(){n._fs.unlink(t,(function(t){n._mu.unlock(),e(t)}))}))},ve.prototype.unlinkSync=function(t){if(this._mu.isLocked())throw new Error("invalid sync call");return this._fs.unlinkSync(t)},ve.prototype.rmdir=function(t,e){var n=this;this._mu.lock((function(){n._fs.rmdir(t,(function(t){n._mu.unlock(),e(t)}))}))},ve.prototype.rmdirSync=function(t){if(this._mu.isLocked())throw new Error("invalid sync call");return this._fs.rmdirSync(t)},ve.prototype.mkdir=function(t,e,n){var r=this;this._mu.lock((function(){r._fs.mkdir(t,e,(function(t){r._mu.unlock(),n(t)}))}))},ve.prototype.mkdirSync=function(t,e){if(this._mu.isLocked())throw new Error("invalid sync call");return this._fs.mkdirSync(t,e)},ve.prototype.readdir=function(t,e){var n=this;this._mu.lock((function(){n._fs.readdir(t,(function(t,r){n._mu.unlock(),e(t,r)}))}))},ve.prototype.readdirSync=function(t){if(this._mu.isLocked())throw new Error("invalid sync call");return this._fs.readdirSync(t)},ve.prototype.exists=function(t,e){var n=this;this._mu.lock((function(){n._fs.exists(t,(function(t){n._mu.unlock(),e(t)}))}))},ve.prototype.existsSync=function(t){if(this._mu.isLocked())throw new Error("invalid sync call");return this._fs.existsSync(t)},ve.prototype.realpath=function(t,e,n){var r=this;this._mu.lock((function(){r._fs.realpath(t,e,(function(t,e){r._mu.unlock(),n(t,e)}))}))},ve.prototype.realpathSync=function(t,e){if(this._mu.isLocked())throw new Error("invalid sync call");return this._fs.realpathSync(t,e)},ve.prototype.truncate=function(t,e,n){var r=this;this._mu.lock((function(){r._fs.truncate(t,e,(function(t){r._mu.unlock(),n(t)}))}))},ve.prototype.truncateSync=function(t,e){if(this._mu.isLocked())throw new Error("invalid sync call");return this._fs.truncateSync(t,e)},ve.prototype.readFile=function(t,e,n,r){var i=this;this._mu.lock((function(){i._fs.readFile(t,e,n,(function(t,e){i._mu.unlock(),r(t,e)}))}))},ve.prototype.readFileSync=function(t,e,n){if(this._mu.isLocked())throw new Error("invalid sync call");return this._fs.readFileSync(t,e,n)},ve.prototype.writeFile=function(t,e,n,r,i,o){var s=this;this._mu.lock((function(){s._fs.writeFile(t,e,n,r,i,(function(t){s._mu.unlock(),o(t)}))}))},ve.prototype.writeFileSync=function(t,e,n,r,i){if(this._mu.isLocked())throw new Error("invalid sync call");return this._fs.writeFileSync(t,e,n,r,i)},ve.prototype.appendFile=function(t,e,n,r,i,o){var s=this;this._mu.lock((function(){s._fs.appendFile(t,e,n,r,i,(function(t){s._mu.unlock(),o(t)}))}))},ve.prototype.appendFileSync=function(t,e,n,r,i){if(this._mu.isLocked())throw new Error("invalid sync call");return this._fs.appendFileSync(t,e,n,r,i)},ve.prototype.chmod=function(t,e,n,r){var i=this;this._mu.lock((function(){i._fs.chmod(t,e,n,(function(t){i._mu.unlock(),r(t)}))}))},ve.prototype.chmodSync=function(t,e,n){if(this._mu.isLocked())throw new Error("invalid sync call");return this._fs.chmodSync(t,e,n)},ve.prototype.chown=function(t,e,n,r,i){var o=this;this._mu.lock((function(){o._fs.chown(t,e,n,r,(function(t){o._mu.unlock(),i(t)}))}))},ve.prototype.chownSync=function(t,e,n,r){if(this._mu.isLocked())throw new Error("invalid sync call");return this._fs.chownSync(t,e,n,r)},ve.prototype.utimes=function(t,e,n,r){var i=this;this._mu.lock((function(){i._fs.utimes(t,e,n,(function(t){i._mu.unlock(),r(t)}))}))},ve.prototype.utimesSync=function(t,e,n){if(this._mu.isLocked())throw new Error("invalid sync call");return this._fs.utimesSync(t,e,n)},ve.prototype.link=function(t,e,n){var r=this;this._mu.lock((function(){r._fs.link(t,e,(function(t){r._mu.unlock(),n(t)}))}))},ve.prototype.linkSync=function(t,e){if(this._mu.isLocked())throw new Error("invalid sync call");return this._fs.linkSync(t,e)},ve.prototype.symlink=function(t,e,n,r){var i=this;this._mu.lock((function(){i._fs.symlink(t,e,n,(function(t){i._mu.unlock(),r(t)}))}))},ve.prototype.symlinkSync=function(t,e,n){if(this._mu.isLocked())throw new Error("invalid sync call");return this._fs.symlinkSync(t,e,n)},ve.prototype.readlink=function(t,e){var n=this;this._mu.lock((function(){n._fs.readlink(t,(function(t,r){n._mu.unlock(),e(t,r)}))}))},ve.prototype.readlinkSync=function(t){if(this._mu.isLocked())throw new Error("invalid sync call");return this._fs.readlinkSync(t)};var _e="/.deletedFiles.log";function be(t){return 146|t}function Ee(t){return p.getFileFlag(t)}var Se=function(t){function e(e,n,r,i,o){t.call(this,e,n,r,i,o)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.sync=function(t){var e=this;this.isDirty()?this._fs._syncAsync(this,(function(n){e.resetDirty(),t(n)})):t(null)},e.prototype.syncSync=function(){this.isDirty()&&(this._fs._syncSync(this),this.resetDirty())},e.prototype.close=function(t){this.sync(t)},e.prototype.closeSync=function(){this.syncSync()},e}(st),ke=function(t){function e(e,n){if(t.call(this),this._isInitialized=!1,this._initializeCallbacks=[],this._deletedFiles={},this._deleteLog="",this._deleteLogUpdatePending=!1,this._deleteLogUpdateNeeded=!1,this._deleteLogError=null,this._writable=e,this._readable=n,this._writable.isReadOnly())throw new f(o.EINVAL,"Writable file system must be writable.")}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.isAvailable=function(){return!0},e.prototype.getOverlayedFileSystems=function(){return{readable:this._readable,writable:this._writable}},e.prototype._syncAsync=function(t,e){var n=this;this.createParentDirectoriesAsync(t.getPath(),(function(r){if(r)return e(r);n._writable.writeFile(t.getPath(),t.getBuffer(),null,Ee("w"),t.getStats().mode,e)}))},e.prototype._syncSync=function(t){this.createParentDirectories(t.getPath()),this._writable.writeFileSync(t.getPath(),t.getBuffer(),null,Ee("w"),t.getStats().mode)},e.prototype.getName=function(){return Ie.Name},e.prototype._initialize=function(t){var e=this,n=this._initializeCallbacks,r=function(t){e._isInitialized=!t,e._initializeCallbacks=[],n.forEach((function(e){return e(t)}))};if(this._isInitialized)return t();n.push(t),1===n.length&&this._writable.readFile(_e,"utf8",Ee("r"),(function(t,n){if(t){if(t.errno!==o.ENOENT)return r(t)}else e._deleteLog=n;e._reparseDeletionLog(),r()}))},e.prototype.isReadOnly=function(){return!1},e.prototype.supportsSynch=function(){return this._readable.supportsSynch()&&this._writable.supportsSynch()},e.prototype.supportsLinks=function(){return!1},e.prototype.supportsProps=function(){return this._readable.supportsProps()&&this._writable.supportsProps()},e.prototype.getDeletionLog=function(){return this._deleteLog},e.prototype.restoreDeletionLog=function(t){this._deleteLog=t,this._reparseDeletionLog(),this.updateLog("")},e.prototype.rename=function(t,e,n){var r=this;if(this.checkInitAsync(n)&&!this.checkPathAsync(t,n)&&!this.checkPathAsync(e,n))return t===_e||e===_e?n(f.EPERM("Cannot rename deletion log.")):t===e?n():void this.stat(t,!1,(function(i,s){return i?n(i):r.stat(e,!1,(function(i,c){var u=r;function h(r){var i=r.shift();if(!i)return n();var o=a.resolve(t,i),s=a.resolve(e,i);u.rename(o,s,(function(t){if(t)return n(t);h(r)}))}var l=511;if(s.isDirectory()){if(i)return i.errno!==o.ENOENT?n(i):r._writable.exists(t,(function(i){if(i)return r._writable.rename(t,e,n);r._writable.mkdir(e,l,(function(e){if(e)return n(e);r._readable.readdir(t,(function(t,e){if(t)return n();h(e)}))}))}));if(l=c.mode,!c.isDirectory())return n(f.ENOTDIR(e));r.readdir(e,(function(i,o){if(o&&o.length)return n(f.ENOTEMPTY(e));r._readable.readdir(t,(function(t,e){if(t)return n();h(e)}))}))}if(c&&c.isDirectory())return n(f.EISDIR(e));r.readFile(t,null,Ee("r"),(function(i,o){return i?n(i):r.writeFile(e,o,null,Ee("w"),s.mode,(function(e){return e?n(e):r.unlink(t,n)}))}))}))}))},e.prototype.renameSync=function(t,e){var n=this;if(this.checkInitialized(),this.checkPath(t),this.checkPath(e),t===_e||e===_e)throw f.EPERM("Cannot rename deletion log.");var r=this.statSync(t,!1);if(r.isDirectory()){if(t===e)return;var i=511;if(this.existsSync(e)){var o=this.statSync(e,!1);if(i=o.mode,!o.isDirectory())throw f.ENOTDIR(e);if(this.readdirSync(e).length>0)throw f.ENOTEMPTY(e)}this._writable.existsSync(t)?this._writable.renameSync(t,e):this._writable.existsSync(e)||this._writable.mkdirSync(e,i),this._readable.existsSync(t)&&this._readable.readdirSync(t).forEach((function(r){n.renameSync(a.resolve(t,r),a.resolve(e,r))}))}else{if(this.existsSync(e)&&this.statSync(e,!1).isDirectory())throw f.EISDIR(e);this.writeFileSync(e,this.readFileSync(t,null,Ee("r")),null,Ee("w"),r.mode)}t!==e&&this.existsSync(t)&&this.unlinkSync(t)},e.prototype.stat=function(t,e,n){var r=this;this.checkInitAsync(n)&&this._writable.stat(t,e,(function(i,s){i&&i.errno===o.ENOENT?(r._deletedFiles[t]&&n(f.ENOENT(t)),r._readable.stat(t,e,(function(t,e){e&&((e=d.clone(e)).mode=be(e.mode)),n(t,e)}))):n(i,s)}))},e.prototype.statSync=function(t,e){this.checkInitialized();try{return this._writable.statSync(t,e)}catch(r){if(this._deletedFiles[t])throw f.ENOENT(t);var n=d.clone(this._readable.statSync(t,e));return n.mode=be(n.mode),n}},e.prototype.open=function(t,e,n,r){var i=this;this.checkInitAsync(r)&&!this.checkPathAsync(t,r)&&this.stat(t,!1,(function(o,s){if(s)switch(e.pathExistsAction()){case u.TRUNCATE_FILE:return i.createParentDirectoriesAsync(t,(function(o){if(o)return r(o);i._writable.open(t,e,n,r)}));case u.NOP:return i._writable.exists(t,(function(o){o?i._writable.open(t,e,n,r):((s=d.clone(s)).mode=n,i._readable.readFile(t,null,Ee("r"),(function(n,o){if(n)return r(n);-1===s.size&&(s.size=o.length);var a=new Se(i,t,e,s,o);r(null,a)})))}));default:return r(f.EEXIST(t))}else switch(e.pathNotExistsAction()){case u.CREATE_FILE:return i.createParentDirectoriesAsync(t,(function(o){return o?r(o):i._writable.open(t,e,n,r)}));default:return r(f.ENOENT(t))}}))},e.prototype.openSync=function(t,e,n){if(this.checkInitialized(),this.checkPath(t),t===_e)throw f.EPERM("Cannot open deletion log.");if(this.existsSync(t))switch(e.pathExistsAction()){case u.TRUNCATE_FILE:return this.createParentDirectories(t),this._writable.openSync(t,e,n);case u.NOP:if(this._writable.existsSync(t))return this._writable.openSync(t,e,n);var r=this._readable.readFileSync(t,null,Ee("r")),i=d.clone(this._readable.statSync(t,!1));return i.mode=n,new Se(this,t,e,i,r);default:throw f.EEXIST(t)}else switch(e.pathNotExistsAction()){case u.CREATE_FILE:return this.createParentDirectories(t),this._writable.openSync(t,e,n);default:throw f.ENOENT(t)}},e.prototype.unlink=function(t,e){var n=this;this.checkInitAsync(e)&&!this.checkPathAsync(t,e)&&this.exists(t,(function(r){if(!r)return e(f.ENOENT(t));n._writable.exists(t,(function(r){if(r)return n._writable.unlink(t,(function(r){if(r)return e(r);n.exists(t,(function(r){r&&n.deletePath(t),e(null)}))}));n.deletePath(t),e(null)}))}))},e.prototype.unlinkSync=function(t){if(this.checkInitialized(),this.checkPath(t),!this.existsSync(t))throw f.ENOENT(t);this._writable.existsSync(t)&&this._writable.unlinkSync(t),this.existsSync(t)&&this.deletePath(t)},e.prototype.rmdir=function(t,e){var n=this;if(this.checkInitAsync(e)){var r=function(){n.readdir(t,(function(r,i){return r?e(r):i.length?e(f.ENOTEMPTY(t)):(n.deletePath(t),void e(null))}))};this.exists(t,(function(i){if(!i)return e(f.ENOENT(t));n._writable.exists(t,(function(i){i?n._writable.rmdir(t,(function(i){if(i)return e(i);n._readable.exists(t,(function(t){t?r():e()}))})):r()}))}))}},e.prototype.rmdirSync=function(t){if(this.checkInitialized(),!this.existsSync(t))throw f.ENOENT(t);if(this._writable.existsSync(t)&&this._writable.rmdirSync(t),this.existsSync(t)){if(this.readdirSync(t).length>0)throw f.ENOTEMPTY(t);this.deletePath(t)}},e.prototype.mkdir=function(t,e,n){var r=this;this.checkInitAsync(n)&&this.exists(t,(function(i){if(i)return n(f.EEXIST(t));r.createParentDirectoriesAsync(t,(function(i){if(i)return n(i);r._writable.mkdir(t,e,n)}))}))},e.prototype.mkdirSync=function(t,e){if(this.checkInitialized(),this.existsSync(t))throw f.EEXIST(t);this.createParentDirectories(t),this._writable.mkdirSync(t,e)},e.prototype.readdir=function(t,e){var n=this;this.checkInitAsync(e)&&this.stat(t,!1,(function(r,i){return r?e(r):i.isDirectory()?void n._writable.readdir(t,(function(r,i){if(r&&"ENOENT"!==r.code)return e(r);!r&&i||(i=[]),n._readable.readdir(t,(function(r,o){!r&&o||(o=[]);var s={},a=i.concat(o.filter((function(e){return!n._deletedFiles[t+"/"+e]}))).filter((function(t){var e=!s[t];return s[t]=!0,e}));e(null,a)}))})):e(f.ENOTDIR(t))}))},e.prototype.readdirSync=function(t){var e=this;if(this.checkInitialized(),!this.statSync(t,!1).isDirectory())throw f.ENOTDIR(t);var n=[];try{n=n.concat(this._writable.readdirSync(t))}catch(t){}try{n=n.concat(this._readable.readdirSync(t).filter((function(n){return!e._deletedFiles[t+"/"+n]})))}catch(t){}var r={};return n.filter((function(t){var e=!r[t];return r[t]=!0,e}))},e.prototype.exists=function(t,e){var n=this;this.checkInitialized(),this._writable.exists(t,(function(r){if(r)return e(!0);n._readable.exists(t,(function(r){e(r&&!0!==n._deletedFiles[t])}))}))},e.prototype.existsSync=function(t){return this.checkInitialized(),this._writable.existsSync(t)||this._readable.existsSync(t)&&!0!==this._deletedFiles[t]},e.prototype.chmod=function(t,e,n,r){var i=this;this.checkInitAsync(r)&&this.operateOnWritableAsync(t,(function(o){if(o)return r(o);i._writable.chmod(t,e,n,r)}))},e.prototype.chmodSync=function(t,e,n){var r=this;this.checkInitialized(),this.operateOnWritable(t,(function(){r._writable.chmodSync(t,e,n)}))},e.prototype.chown=function(t,e,n,r,i){var o=this;this.checkInitAsync(i)&&this.operateOnWritableAsync(t,(function(s){if(s)return i(s);o._writable.chown(t,e,n,r,i)}))},e.prototype.chownSync=function(t,e,n,r){var i=this;this.checkInitialized(),this.operateOnWritable(t,(function(){i._writable.chownSync(t,e,n,r)}))},e.prototype.utimes=function(t,e,n,r){var i=this;this.checkInitAsync(r)&&this.operateOnWritableAsync(t,(function(o){if(o)return r(o);i._writable.utimes(t,e,n,r)}))},e.prototype.utimesSync=function(t,e,n){var r=this;this.checkInitialized(),this.operateOnWritable(t,(function(){r._writable.utimesSync(t,e,n)}))},e.prototype.deletePath=function(t){this._deletedFiles[t]=!0,this.updateLog("d"+t+"\n")},e.prototype.updateLog=function(t){var e=this;this._deleteLog+=t,this._deleteLogUpdatePending?this._deleteLogUpdateNeeded=!0:(this._deleteLogUpdatePending=!0,this._writable.writeFile(_e,this._deleteLog,"utf8",p.getFileFlag("w"),420,(function(t){e._deleteLogUpdatePending=!1,t?e._deleteLogError=t:e._deleteLogUpdateNeeded&&(e._deleteLogUpdateNeeded=!1,e.updateLog(""))})))},e.prototype._reparseDeletionLog=function(){var t=this;this._deletedFiles={},this._deleteLog.split("\n").forEach((function(e){t._deletedFiles[e.slice(1)]="d"===e.slice(0,1)}))},e.prototype.checkInitialized=function(){if(!this._isInitialized)throw new f(o.EPERM,"OverlayFS is not initialized. Please initialize OverlayFS using its initialize() method before using it.");if(null!==this._deleteLogError){var t=this._deleteLogError;throw this._deleteLogError=null,t}},e.prototype.checkInitAsync=function(t){if(!this._isInitialized)return t(new f(o.EPERM,"OverlayFS is not initialized. Please initialize OverlayFS using its initialize() method before using it.")),!1;if(null!==this._deleteLogError){var e=this._deleteLogError;return this._deleteLogError=null,t(e),!1}return!0},e.prototype.checkPath=function(t){if(t===_e)throw f.EPERM(t)},e.prototype.checkPathAsync=function(t,e){return t===_e&&(e(f.EPERM(t)),!0)},e.prototype.createParentDirectoriesAsync=function(t,e){var n=a.dirname(t),r=[],i=this;this._writable.stat(n,!1,(function t(s,c){s?"/"===n?e(new f(o.EBUSY,"Invariant failed: root does not exist!")):(r.push(n),n=a.dirname(n),i._writable.stat(n,!1,t)):function t(){if(!r.length)return e();var n=r.pop();i._readable.stat(n,!1,(function(r,o){if(!o)return e();i._writable.mkdir(n,o.mode,(function(n){if(n)return e(n);t()}))}))}()}))},e.prototype.createParentDirectories=function(t){for(var e=this,n=a.dirname(t),r=[];!this._writable.existsSync(n);)r.push(n),n=a.dirname(n);(r=r.reverse()).forEach((function(t){e._writable.mkdirSync(t,e.statSync(t,!1).mode)}))},e.prototype.operateOnWritable=function(t,e){if(!this.existsSync(t))throw f.ENOENT(t);this._writable.existsSync(t)||this.copyToWritable(t),e()},e.prototype.operateOnWritableAsync=function(t,e){var n=this;this.exists(t,(function(r){if(!r)return e(f.ENOENT(t));n._writable.exists(t,(function(r){if(!r)return n.copyToWritableAsync(t,e);e()}))}))},e.prototype.copyToWritable=function(t){var e=this.statSync(t,!1);e.isDirectory()?this._writable.mkdirSync(t,e.mode):this.writeFileSync(t,this._readable.readFileSync(t,null,Ee("r")),null,Ee("w"),this.statSync(t,!1).mode)},e.prototype.copyToWritableAsync=function(t,e){var n=this;this.stat(t,!1,(function(r,i){return r?e(r):i.isDirectory()?n._writable.mkdir(t,i.mode,e):void n._readable.readFile(t,null,Ee("r"),(function(r,o){if(r)return e(r);n.writeFile(t,o,null,Ee("w"),i.mode,e)}))}))},e}(rt),Ie=function(t){function e(e,n){t.call(this,new ke(e,n))}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.Create=function(t,n){try{var r=new e(t.writable,t.readable);r._initialize((function(t){n(t,r)}))}catch(t){n(t)}},e.isAvailable=function(){return ke.isAvailable()},e.prototype.getOverlayedFileSystems=function(){return t.prototype.getFSUnlocked.call(this).getOverlayedFileSystems()},e.prototype.unwrap=function(){return t.prototype.getFSUnlocked.call(this)},e.prototype._initialize=function(e){t.prototype.getFSUnlocked.call(this)._initialize(e)},e}(ve);function Ne(){throw new f(o.ENOTSUP,"Synchronous HTTP download methods are not available in this environment.")}Ie.Name="OverlayFS",Ie.Options={writable:{type:"object",description:"The file system to write modified files to."},readable:{type:"object",description:"The file system that initially populates this file system."}};var Oe=function(t){function e(e,n,r,i){void 0===i&&(i=!1),t.call(this),this.dependency=n,this.version=r,this._index=vt.fromUnpkg(e),!gt||i&&ft?(this._requestFileAsyncInternal=lt,this._requestFileSizeAsyncInternal=yt):(this._requestFileAsyncInternal=wt,this._requestFileSizeAsyncInternal=mt),ft?(this._requestFileSyncInternal=pt,this._requestFileSizeSyncInternal=dt):(this._requestFileSyncInternal=Ne,this._requestFileSizeSyncInternal=Ne)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.Create=function(t,n){var r="https://unpkg.com/"+t.dependency+"@"+t.version;lt(r+"/?meta","json",(function(r,i){r?n(r):n(null,new e(i,t.dependency,t.version))}))},e.isAvailable=function(){return ft||gt},e.prototype.empty=function(){this._index.fileIterator((function(t){t.fileData=null}))},e.prototype.getName=function(){return e.Name},e.prototype.diskSpace=function(t,e){e(0,0)},e.prototype.isReadOnly=function(){return!0},e.prototype.supportsLinks=function(){return!1},e.prototype.supportsProps=function(){return!1},e.prototype.supportsSynch=function(){return ft},e.prototype.preloadFile=function(t,e){var n=this._index.getInode(t);if(!Et(n))throw f.EISDIR(t);if(null===n)throw f.ENOENT(t);var r=n.getData();r.size=e.length,r.fileData=e},e.prototype.stat=function(t,e,n){var r,i=this._index.getInode(t);if(null===i)return n(f.ENOENT(t));Et(i)?(r=i.getData()).size<0?this._requestFileSizeAsync(t,(function(t,e){if(t)return n(t);r.size=e,n(null,d.clone(r))})):n(null,d.clone(r)):St(i)?(r=i.getStats(),n(null,r)):n(f.FileError(o.EINVAL,t))},e.prototype.statSync=function(t,e){var n,r=this._index.getInode(t);if(null===r)throw f.ENOENT(t);if(Et(r))(n=r.getData()).size<0&&(n.size=this._requestFileSizeSync(t));else{if(!St(r))throw f.FileError(o.EINVAL,t);n=r.getStats()}return n},e.prototype.open=function(t,e,n,r){if(e.isWriteable())return r(new f(o.EPERM,t));var i=this,s=this._index.getInode(t);if(null===s)return r(f.ENOENT(t));if(!Et(s))return r(f.EISDIR(t));var a=s.getData();switch(e.pathExistsAction()){case u.THROW_EXCEPTION:case u.TRUNCATE_FILE:return r(f.EEXIST(t));case u.NOP:if(a.fileData)return r(null,new at(i,t,e,d.clone(a),a.fileData));this._requestFileAsync(t,"buffer",(function(n,o){return n?r(n):(a.size=o.length,a.fileData=o,r(null,new at(i,t,e,d.clone(a),o)))}));break;default:return r(new f(o.EINVAL,"Invalid FileMode object."))}},e.prototype.openSync=function(t,e,n){if(e.isWriteable())throw new f(o.EPERM,t);var r=this._index.getInode(t);if(null===r)throw f.ENOENT(t);if(!Et(r))throw f.EISDIR(t);var i=r.getData();switch(e.pathExistsAction()){case u.THROW_EXCEPTION:case u.TRUNCATE_FILE:throw f.EEXIST(t);case u.NOP:if(i.fileData)return new at(this,t,e,d.clone(i),i.fileData);var s=this._requestFileSync(t,"buffer");return i.size=s.length,i.fileData=s,new at(this,t,e,d.clone(i),s);default:throw new f(o.EINVAL,"Invalid FileMode object.")}},e.prototype.readdir=function(t,e){try{e(null,this.readdirSync(t))}catch(t){e(t)}},e.prototype.readdirSync=function(t){var e=this._index.getInode(t);if(null===e)throw f.ENOENT(t);if(St(e))return e.getListing();throw f.ENOTDIR(t)},e.prototype.readFile=function(t,e,n,r){var i=r;this.open(t,n,420,(function(t,n){if(t)return r(t);r=function(t,e){n.close((function(n){return t||(t=n),i(t,e)}))};var o=n.getBuffer();null===e?r(t,Y(o)):function(t,e,n){try{n(null,t.toString(e))}catch(t){n(t)}}(o,e,r)}))},e.prototype.readFileSync=function(t,e,n){var r=this.openSync(t,n,420);try{var i=r.getBuffer();return null===e?Y(i):i.toString(e)}finally{r.closeSync()}},e.prototype._getHTTPPath=function(t){return"/"===t.charAt(0)&&(t=t.slice(1)),"https://unpkg.com/"+this.dependency+"@"+this.version+"/"+t},e.prototype._requestFileAsync=function(t,e,n){this._requestFileAsyncInternal(this._getHTTPPath(t),e,n)},e.prototype._requestFileSync=function(t,e){return this._requestFileSyncInternal(this._getHTTPPath(t),e)},e.prototype._requestFileSizeAsync=function(t,e){this._requestFileSizeAsyncInternal(this._getHTTPPath(t),e)},e.prototype._requestFileSizeSync=function(t){return this._requestFileSizeSyncInternal(this._getHTTPPath(t))},e}(rt);function Te(){throw new f(o.ENOTSUP,"Synchronous HTTP download methods are not available in this environment.")}Oe.Name="UNPKGRequest",Oe.Options={dependency:{type:"string",description:"Name of dependency"},version:{type:"string",description:"Version of dependency, can be semver"},preferXHR:{type:"boolean",optional:!0,description:"Whether to prefer XmlHttpRequest or fetch for async operations if both are available. Default: false"}};var Fe=function(t){function e(e,n,r,i){void 0===i&&(i=!1),t.call(this),this.dependency=n,this.version=r,this._index=vt.fromJSDelivr(e),!gt||i&&ft?(this._requestFileAsyncInternal=lt,this._requestFileSizeAsyncInternal=yt):(this._requestFileAsyncInternal=wt,this._requestFileSizeAsyncInternal=mt),ft?(this._requestFileSyncInternal=pt,this._requestFileSizeSyncInternal=dt):(this._requestFileSyncInternal=Te,this._requestFileSizeSyncInternal=Te)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.Create=function(t,n){var r="https://data.jsdelivr.com/v1/package/npm/"+t.dependency+"@"+t.version+"/flat";lt(r,"json",(function(r,i){r?n(r):n(null,new e(i,t.dependency,t.version))}))},e.isAvailable=function(){return ft||gt},e.prototype.empty=function(){this._index.fileIterator((function(t){t.fileData=null}))},e.prototype.getName=function(){return e.Name},e.prototype.diskSpace=function(t,e){e(0,0)},e.prototype.isReadOnly=function(){return!0},e.prototype.supportsLinks=function(){return!1},e.prototype.supportsProps=function(){return!1},e.prototype.supportsSynch=function(){return ft},e.prototype.preloadFile=function(t,e){var n=this._index.getInode(t);if(!Et(n))throw f.EISDIR(t);if(null===n)throw f.ENOENT(t);var r=n.getData();r.size=e.length,r.fileData=e},e.prototype.stat=function(t,e,n){var r,i=this._index.getInode(t);if(null===i)return n(f.ENOENT(t));Et(i)?(r=i.getData()).size<0?this._requestFileSizeAsync(t,(function(t,e){if(t)return n(t);r.size=e,n(null,d.clone(r))})):n(null,d.clone(r)):St(i)?(r=i.getStats(),n(null,r)):n(f.FileError(o.EINVAL,t))},e.prototype.statSync=function(t,e){var n,r=this._index.getInode(t);if(null===r)throw f.ENOENT(t);if(Et(r))(n=r.getData()).size<0&&(n.size=this._requestFileSizeSync(t));else{if(!St(r))throw f.FileError(o.EINVAL,t);n=r.getStats()}return n},e.prototype.open=function(t,e,n,r){if(e.isWriteable())return r(new f(o.EPERM,t));var i=this,s=this._index.getInode(t);if(null===s)return r(f.ENOENT(t));if(!Et(s))return r(f.EISDIR(t));var a=s.getData();switch(e.pathExistsAction()){case u.THROW_EXCEPTION:case u.TRUNCATE_FILE:return r(f.EEXIST(t));case u.NOP:if(a.fileData)return r(null,new at(i,t,e,d.clone(a),a.fileData));this._requestFileAsync(t,"buffer",(function(n,o){return n?r(n):(a.size=o.length,a.fileData=o,r(null,new at(i,t,e,d.clone(a),o)))}));break;default:return r(new f(o.EINVAL,"Invalid FileMode object."))}},e.prototype.openSync=function(t,e,n){if(e.isWriteable())throw new f(o.EPERM,t);var r=this._index.getInode(t);if(null===r)throw f.ENOENT(t);if(!Et(r))throw f.EISDIR(t);var i=r.getData();switch(e.pathExistsAction()){case u.THROW_EXCEPTION:case u.TRUNCATE_FILE:throw f.EEXIST(t);case u.NOP:if(i.fileData)return new at(this,t,e,d.clone(i),i.fileData);var s=this._requestFileSync(t,"buffer");return i.size=s.length,i.fileData=s,new at(this,t,e,d.clone(i),s);default:throw new f(o.EINVAL,"Invalid FileMode object.")}},e.prototype.readdir=function(t,e){try{e(null,this.readdirSync(t))}catch(t){e(t)}},e.prototype.readdirSync=function(t){var e=this._index.getInode(t);if(null===e)throw f.ENOENT(t);if(St(e))return e.getListing();throw f.ENOTDIR(t)},e.prototype.readFile=function(t,e,n,r){var i=r;this.open(t,n,420,(function(t,n){if(t)return r(t);r=function(t,e){n.close((function(n){return t||(t=n),i(t,e)}))};var o=n.getBuffer();null===e?r(t,Y(o)):function(t,e,n){try{n(null,t.toString(e))}catch(t){n(t)}}(o,e,r)}))},e.prototype.readFileSync=function(t,e,n){var r=this.openSync(t,n,420);try{var i=r.getBuffer();return null===e?Y(i):i.toString(e)}finally{r.closeSync()}},e.prototype._getHTTPPath=function(t){return"/"===t.charAt(0)&&(t=t.slice(1)),"https://cdn.jsdelivr.net/npm/"+this.dependency+"@"+this.version+"/"+t},e.prototype._requestFileAsync=function(t,e,n){this._requestFileAsyncInternal(this._getHTTPPath(t),e,n)},e.prototype._requestFileSync=function(t,e){return this._requestFileSyncInternal(this._getHTTPPath(t),e)},e.prototype._requestFileSizeAsync=function(t,e){this._requestFileSizeAsyncInternal(this._getHTTPPath(t),e)},e.prototype._requestFileSizeSync=function(t){return this._requestFileSizeSyncInternal(this._getHTTPPath(t))},e}(rt);Fe.Name="JSDelivrRequest",Fe.Options={dependency:{type:"string",description:"Name of dependency"},version:{type:"string",description:"Version of dependency, has to be absolute"},preferXHR:{type:"boolean",optional:!0,description:"Whether to prefer XmlHttpRequest or fetch for async operations if both are available. Default: false"}};var xe,Re=function(e){function n(t){e.call(this),this.socket=t.socket}return e&&(n.__proto__=e),n.prototype=Object.create(e&&e.prototype),n.prototype.constructor=n,n.Create=function(t,e){e(null,new n(t))},n.isAvailable=function(){return!0},n.prototype.getName=function(){return"WebsocketFS"},n.prototype.isReadOnly=function(){return!1},n.prototype.supportsProps=function(){return!1},n.prototype.supportsSynch=function(){return!0},n.prototype.readFile=function(e,n,r,i){try{this.socket.emit({method:"readFile",args:{path:e,encoding:n,flag:r}},(function(e){var n=e.error,r=e.data;r?i(null,t.from(r)):i(n)}))}catch(t){i(t)}},n.prototype.stat=function(t,e,n){try{this.socket.emit({method:"stat",args:{path:t,isLstat:e}},(function(t){var e=t.error,r=t.data;r?n(null,Object.assign(Object.assign({},r),{atime:new Date(r.atime),mtime:new Date(r.mtime),ctime:new Date(r.ctime),birthtime:new Date(r.birthtime)})):n(e)}))}catch(t){n(t)}},n}(it);Re.Name="WebsocketFS",Re.Options={socket:{type:"object",description:"The socket emitter",validator:function(t,e){t?e():e(new f(o.EINVAL,"Manager is invalid"))}}},function(t){t[t.CB=0]="CB",t[t.FD=1]="FD",t[t.API_ERROR=2]="API_ERROR",t[t.STATS=3]="STATS",t[t.PROBE=4]="PROBE",t[t.FILEFLAG=5]="FILEFLAG",t[t.BUFFER=6]="BUFFER",t[t.ERROR=7]="ERROR"}(xe||(xe={}));var Ae=function(){this._callbacks={},this._nextId=0};Ae.prototype.toRemoteArg=function(t){var e=this._nextId++;return this._callbacks[e]=t,{type:xe.CB,id:e}},Ae.prototype.toLocalArg=function(t){var e=this._callbacks[t];return delete this._callbacks[t],e};var Le=function(){this._fileDescriptors={},this._nextId=0};function De(t){return{type:xe.API_ERROR,errorData:qe(t.writeToBuffer())}}function Pe(t){return f.fromBuffer(We(t.errorData))}function Ce(t){return{type:xe.ERROR,name:t.name,message:t.message,stack:t.stack}}function Me(t){var e=w[t.name];"function"!=typeof e&&(e=Error);var n=new e(t.message);return n.stack=t.stack,n}function Ue(t){return{type:xe.STATS,statsData:qe(t.toBuffer())}}function je(t){return d.fromBuffer(We(t.statsData))}function Be(t){return{type:xe.FILEFLAG,flagStr:t.getFlagString()}}function ze(t){return p.getFileFlag(t.flagStr)}function qe(t){return W(t)}function We(t){return Z(t)}function He(t){return{type:xe.BUFFER,data:qe(t)}}function Ve(t){return We(t.data)}Le.prototype.toRemoteArg=function(e,n,r,i){var o,s,a=this._nextId++;this._fileDescriptors[a]=e,e.stat((function(c,u){c?i(c):(s=qe(u.toBuffer()),r.isReadable()?e.read(t.alloc(u.size),0,u.size,0,(function(t,e,c){t?i(t):(o=qe(c),i(null,{type:xe.FD,id:a,data:o,stat:s,path:n,flag:r.getFlagString()}))})):i(null,{type:xe.FD,id:a,data:new ArrayBuffer(0),stat:s,path:n,flag:r.getFlagString()}))}))},Le.prototype.applyFdAPIRequest=function(t,e){var n=this,r=t.args[0];this._applyFdChanges(r,(function(i,o){i?e(i):o[t.method]((function(i){"close"===t.method&&delete n._fileDescriptors[r.id],e(i)}))}))},Le.prototype._applyFdChanges=function(t,e){var n=this._fileDescriptors[t.id],r=We(t.data),i=d.fromBuffer(We(t.stat)),o=p.getFileFlag(t.flag);o.isWriteable()?n.write(r,0,r.length,o.isAppendable()?n.getPos():0,(function(t){function s(){n.stat((function(t,r){t?e(t):r.mode!==i.mode?n.chmod(i.mode,(function(t){e(t,n)})):e(t,n)}))}t?e(t):o.isAppendable()?s():n.truncate(r.length,(function(){s()}))})):e(null,n)};var Xe=function(t){function e(e,n,r,i,o,s){t.call(this,e,n,r,i,s),this._remoteFdId=o}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.getRemoteFdId=function(){return this._remoteFdId},e.prototype.toRemoteArg=function(){return{type:xe.FD,id:this._remoteFdId,data:qe(this.getBuffer()),stat:qe(this.getStats().toBuffer()),path:this.getPath(),flag:this.getFlag().getFlagString()}},e.prototype.sync=function(t){this._syncClose("sync",t)},e.prototype.close=function(t){this._syncClose("close",t)},e.prototype._syncClose=function(t,e){var n=this;this.isDirty()?this._fs.syncClose(t,this,(function(t){t||n.resetDirty(),e(t)})):e()},e}(st),Ze=function(e){function n(t){var n=this;e.call(this),this._callbackConverter=new Ae,this._isInitialized=!1,this._isReadOnly=!1,this._supportLinks=!1,this._supportProps=!1,this._worker=t,this._worker.addEventListener("message",(function(t){var e,r=t.data;if((e=r)&&"object"==typeof e&&e.hasOwnProperty("browserfsMessage")&&e.browserfsMessage){var i,o=r.args,s=new Array(o.length);for(i=0;i<s.length;i++)s[i]=n._argRemote2Local(o[i]);n._callbackConverter.toLocalArg(r.cbId).apply(null,s)}}))}return e&&(n.__proto__=e),n.prototype=Object.create(e&&e.prototype),n.prototype.constructor=n,n.Create=function(t,e){var r=new n(t.worker);r._initialize((function(){e(null,r)}))},n.isAvailable=function(){return"undefined"!=typeof importScripts||"undefined"!=typeof Worker},n.attachRemoteListener=function(e){var n=new Le;function r(e,r,i){switch(typeof e){case"object":e instanceof d?i(null,Ue(e)):e instanceof f?i(null,De(e)):e instanceof ot?i(null,n.toRemoteArg(e,r[0],r[1],i)):e instanceof p?i(null,Be(e)):e instanceof t?i(null,He(e)):e instanceof Error?i(null,Ce(e)):i(null,e);break;default:i(null,e)}}function i(t,n){if(!t)return t;switch(typeof t){case"object":if("number"!=typeof t.type)return t;var i=t;switch(i.type){case xe.CB:var o=t.id;return function(){var t,i,s=arguments,a=new Array(arguments.length),c=arguments.length;function u(t){c>0&&(c=-1,i={browserfsMessage:!0,cbId:o,args:[De(t)]},e.postMessage(i))}for(t=0;t<arguments.length;t++)!function(t,s){r(s,n,(function(n,r){a[t]=r,n?u(n):0==--c&&(i={browserfsMessage:!0,cbId:o,args:a},e.postMessage(i))}))}(t,s[t]);0===arguments.length&&(i={browserfsMessage:!0,cbId:o,args:a},e.postMessage(i))};case xe.API_ERROR:return Pe(i);case xe.STATS:return je(i);case xe.FILEFLAG:return ze(i);case xe.BUFFER:return Ve(i);case xe.ERROR:return Me(i);default:return t}default:return t}}e.addEventListener("message",(function(t){var r,o,s=t.data;if((o=s)&&"object"==typeof o&&o.hasOwnProperty("browserfsMessage")&&o.browserfsMessage){var a=s.args,c=new Array(a.length);switch(s.method){case"close":case"sync":r=a[1],n.applyFdAPIRequest(s,(function(t){var n={browserfsMessage:!0,cbId:r.id,args:t?[De(t)]:[]};e.postMessage(n)}));break;case"probe":!function(){var t=P.getRootFS(),n=a[1],r={type:xe.PROBE,isReadOnly:t.isReadOnly(),supportsLinks:t.supportsLinks(),supportsProps:t.supportsProps()},i={browserfsMessage:!0,cbId:n.id,args:[r]};e.postMessage(i)}();break;default:for(var u=0;u<a.length;u++)c[u]=i(a[u],c);var f=P.getRootFS();f[s.method].apply(f,c)}}}))},n.prototype.getName=function(){return n.Name},n.prototype.isReadOnly=function(){return this._isReadOnly},n.prototype.supportsSynch=function(){return!1},n.prototype.supportsLinks=function(){return this._supportLinks},n.prototype.supportsProps=function(){return this._supportProps},n.prototype.rename=function(t,e,n){this._rpc("rename",arguments)},n.prototype.stat=function(t,e,n){this._rpc("stat",arguments)},n.prototype.open=function(t,e,n,r){this._rpc("open",arguments)},n.prototype.unlink=function(t,e){this._rpc("unlink",arguments)},n.prototype.rmdir=function(t,e){this._rpc("rmdir",arguments)},n.prototype.mkdir=function(t,e,n){this._rpc("mkdir",arguments)},n.prototype.readdir=function(t,e){this._rpc("readdir",arguments)},n.prototype.exists=function(t,e){this._rpc("exists",arguments)},n.prototype.realpath=function(t,e,n){this._rpc("realpath",arguments)},n.prototype.truncate=function(t,e,n){this._rpc("truncate",arguments)},n.prototype.readFile=function(t,e,n,r){this._rpc("readFile",arguments)},n.prototype.writeFile=function(t,e,n,r,i,o){this._rpc("writeFile",arguments)},n.prototype.appendFile=function(t,e,n,r,i,o){this._rpc("appendFile",arguments)},n.prototype.chmod=function(t,e,n,r){this._rpc("chmod",arguments)},n.prototype.chown=function(t,e,n,r,i){this._rpc("chown",arguments)},n.prototype.utimes=function(t,e,n,r){this._rpc("utimes",arguments)},n.prototype.link=function(t,e,n){this._rpc("link",arguments)},n.prototype.symlink=function(t,e,n,r){this._rpc("symlink",arguments)},n.prototype.readlink=function(t,e){this._rpc("readlink",arguments)},n.prototype.syncClose=function(t,e,n){this._worker.postMessage({browserfsMessage:!0,method:t,args:[e.toRemoteArg(),this._callbackConverter.toRemoteArg(n)]})},n.prototype._initialize=function(t){var e=this;if(this._isInitialized)t();else{var n={browserfsMessage:!0,method:"probe",args:[this._argLocal2Remote(K()),this._callbackConverter.toRemoteArg((function(n){e._isInitialized=!0,e._isReadOnly=n.isReadOnly,e._supportLinks=n.supportsLinks,e._supportProps=n.supportsProps,t()}))]};this._worker.postMessage(n)}},n.prototype._argRemote2Local=function(t){if(!t)return t;switch(typeof t){case"object":if("number"!=typeof t.type)return t;var e=t;switch(e.type){case xe.API_ERROR:return Pe(e);case xe.FD:var n=e;return new Xe(this,n.path,p.getFileFlag(n.flag),d.fromBuffer(We(n.stat)),n.id,We(n.data));case xe.STATS:return je(e);case xe.FILEFLAG:return ze(e);case xe.BUFFER:return Ve(e);case xe.ERROR:return Me(e);default:return t}default:return t}},n.prototype._rpc=function(t,e){for(var n=new Array(e.length),r=0;r<e.length;r++)n[r]=this._argLocal2Remote(e[r]);var i={browserfsMessage:!0,method:t,args:n};this._worker.postMessage(i)},n.prototype._argLocal2Remote=function(e){if(!e)return e;switch(typeof e){case"object":return e instanceof d?Ue(e):e instanceof f?De(e):e instanceof Xe?e.toRemoteArg():e instanceof p?Be(e):e instanceof t?He(e):e instanceof Error?Ce(e):"Unknown argument";case"function":return this._callbackConverter.toRemoteArg(e);default:return e}},n}(rt);Ze.Name="WorkerFS",Ze.Options={worker:{type:"object",description:"The target worker that you want to connect to, or the current worker if in a worker context.",validator:function(t,e){t.postMessage?e():e(new f(o.EINVAL,"option must be a Web Worker instance."))}}};var Ye=function(){};Ye.str2byte=function(t,e){for(var n=t.length>e.length?e.length:t.length,r=0;r<n;r++){var i=t.charCodeAt(r);if(i>127){var o=Ye.extendedChars.indexOf(t.charAt(r));o>-1&&(i=o+128)}e[i]=r}return n},Ye.byte2str=function(t){for(var e=new Array(t.length),n=0;n<t.length;n++){var r=t[n];e[n]=r>127?Ye.extendedChars[r-128]:String.fromCharCode(r)}return e.join("")},Ye.byteLength=function(t){return t.length},Ye.extendedChars=["Ç","ü","é","â","ä","à","å","ç","ê","ë","è","ï","î","ì","Ä","Å","É","æ","Æ","ô","ö","ò","û","ù","ÿ","Ö","Ü","ø","£","Ø","×","ƒ","á","í","ó","ú","ñ","Ñ","ª","º","¿","®","¬","½","¼","¡","«","»","_","_","_","¦","¦","Á","Â","À","©","¦","¦","+","+","¢","¥","+","+","-","-","+","-","+","ã","Ã","+","+","-","-","¦","-","+","¤","ð","Ð","Ê","Ë","È","i","Í","Î","Ï","+","+","_","_","¦","Ì","_","Ó","ß","Ô","Ò","õ","Õ","µ","þ","Þ","Ú","Û","Ù","ý","Ý","¯","´","­","±","_","¾","¶","§","÷","¸","°","¨","·","¹","³","²","_"," "];var Je,Ke,Ge=n(36).inflateRaw,Qe={};function $e(t,e){return new Date(1980+(e>>9),(e>>5&15)-1,31&e,t>>11,t>>5&63,31&t)}function tn(t,e,n,r){return 0===r?"":e?t.toString("utf8",n,n+r):Ye.byte2str(t.slice(n,n+r))}!function(t){t[t.MSDOS=0]="MSDOS",t[t.AMIGA=1]="AMIGA",t[t.OPENVMS=2]="OPENVMS",t[t.UNIX=3]="UNIX",t[t.VM_CMS=4]="VM_CMS",t[t.ATARI_ST=5]="ATARI_ST",t[t.OS2_HPFS=6]="OS2_HPFS",t[t.MAC=7]="MAC",t[t.Z_SYSTEM=8]="Z_SYSTEM",t[t.CP_M=9]="CP_M",t[t.NTFS=10]="NTFS",t[t.MVS=11]="MVS",t[t.VSE=12]="VSE",t[t.ACORN_RISC=13]="ACORN_RISC",t[t.VFAT=14]="VFAT",t[t.ALT_MVS=15]="ALT_MVS",t[t.BEOS=16]="BEOS",t[t.TANDEM=17]="TANDEM",t[t.OS_400=18]="OS_400",t[t.OSX=19]="OSX"}(Je||(Je={})),function(t){t[t.STORED=0]="STORED",t[t.SHRUNK=1]="SHRUNK",t[t.REDUCED_1=2]="REDUCED_1",t[t.REDUCED_2=3]="REDUCED_2",t[t.REDUCED_3=4]="REDUCED_3",t[t.REDUCED_4=5]="REDUCED_4",t[t.IMPLODE=6]="IMPLODE",t[t.DEFLATE=8]="DEFLATE",t[t.DEFLATE64=9]="DEFLATE64",t[t.TERSE_OLD=10]="TERSE_OLD",t[t.BZIP2=12]="BZIP2",t[t.LZMA=14]="LZMA",t[t.TERSE_NEW=18]="TERSE_NEW",t[t.LZ77=19]="LZ77",t[t.WAVPACK=97]="WAVPACK",t[t.PPMD=98]="PPMD"}(Ke||(Ke={}));var en=function(t){if(this.data=t,67324752!==t.readUInt32LE(0))throw new f(o.EINVAL,"Invalid Zip file: Local file header has invalid signature: "+this.data.readUInt32LE(0))};en.prototype.versionNeeded=function(){return this.data.readUInt16LE(4)},en.prototype.flags=function(){return this.data.readUInt16LE(6)},en.prototype.compressionMethod=function(){return this.data.readUInt16LE(8)},en.prototype.lastModFileTime=function(){return $e(this.data.readUInt16LE(10),this.data.readUInt16LE(12))},en.prototype.rawLastModFileTime=function(){return this.data.readUInt32LE(10)},en.prototype.crc32=function(){return this.data.readUInt32LE(14)},en.prototype.fileNameLength=function(){return this.data.readUInt16LE(26)},en.prototype.extraFieldLength=function(){return this.data.readUInt16LE(28)},en.prototype.fileName=function(){return tn(this.data,this.useUTF8(),30,this.fileNameLength())},en.prototype.extraField=function(){var t=30+this.fileNameLength();return this.data.slice(t,t+this.extraFieldLength())},en.prototype.totalSize=function(){return 30+this.fileNameLength()+this.extraFieldLength()},en.prototype.useUTF8=function(){return 2048==(2048&this.flags())};var nn=function(t,e,n){this.header=t,this.record=e,this.data=n};nn.prototype.decompress=function(){var t=this.header.compressionMethod(),e=Qe[t];if(e)return e(this.data,this.record.compressedSize(),this.record.uncompressedSize(),this.record.flag());var n=Ke[t];throw n||(n="Unknown: "+t),new f(o.EINVAL,"Invalid compression method on file '"+this.header.fileName()+"': "+n)},nn.prototype.getHeader=function(){return this.header},nn.prototype.getRecord=function(){return this.record},nn.prototype.getRawData=function(){return this.data};var rn=function(t,e){if(this.zipData=t,this.data=e,33639248!==this.data.readUInt32LE(0))throw new f(o.EINVAL,"Invalid Zip file: Central directory record has invalid signature: "+this.data.readUInt32LE(0));this._filename=this.produceFilename()};rn.prototype.versionMadeBy=function(){return this.data.readUInt16LE(4)},rn.prototype.versionNeeded=function(){return this.data.readUInt16LE(6)},rn.prototype.flag=function(){return this.data.readUInt16LE(8)},rn.prototype.compressionMethod=function(){return this.data.readUInt16LE(10)},rn.prototype.lastModFileTime=function(){return $e(this.data.readUInt16LE(12),this.data.readUInt16LE(14))},rn.prototype.rawLastModFileTime=function(){return this.data.readUInt32LE(12)},rn.prototype.crc32=function(){return this.data.readUInt32LE(16)},rn.prototype.compressedSize=function(){return this.data.readUInt32LE(20)},rn.prototype.uncompressedSize=function(){return this.data.readUInt32LE(24)},rn.prototype.fileNameLength=function(){return this.data.readUInt16LE(28)},rn.prototype.extraFieldLength=function(){return this.data.readUInt16LE(30)},rn.prototype.fileCommentLength=function(){return this.data.readUInt16LE(32)},rn.prototype.diskNumberStart=function(){return this.data.readUInt16LE(34)},rn.prototype.internalAttributes=function(){return this.data.readUInt16LE(36)},rn.prototype.externalAttributes=function(){return this.data.readUInt32LE(38)},rn.prototype.headerRelativeOffset=function(){return this.data.readUInt32LE(42)},rn.prototype.produceFilename=function(){return tn(this.data,this.useUTF8(),46,this.fileNameLength()).replace(/\\/g,"/")},rn.prototype.fileName=function(){return this._filename},rn.prototype.rawFileName=function(){return this.data.slice(46,46+this.fileNameLength())},rn.prototype.extraField=function(){var t=44+this.fileNameLength();return this.data.slice(t,t+this.extraFieldLength())},rn.prototype.fileComment=function(){var t=46+this.fileNameLength()+this.extraFieldLength();return tn(this.data,this.useUTF8(),t,this.fileCommentLength())},rn.prototype.rawFileComment=function(){var t=46+this.fileNameLength()+this.extraFieldLength();return this.data.slice(t,t+this.fileCommentLength())},rn.prototype.totalSize=function(){return 46+this.fileNameLength()+this.extraFieldLength()+this.fileCommentLength()},rn.prototype.isDirectory=function(){var t=this.fileName();return!!(16&this.externalAttributes())||"/"===t.charAt(t.length-1)},rn.prototype.isFile=function(){return!this.isDirectory()},rn.prototype.useUTF8=function(){return 2048==(2048&this.flag())},rn.prototype.isEncrypted=function(){return 1==(1&this.flag())},rn.prototype.getFileData=function(){var t=this.headerRelativeOffset(),e=new en(this.zipData.slice(t));return new nn(e,this,this.zipData.slice(t+e.totalSize()))},rn.prototype.getData=function(){return this.getFileData().decompress()},rn.prototype.getRawData=function(){return this.getFileData().getRawData()},rn.prototype.getStats=function(){return new d(l.FILE,this.uncompressedSize(),365,Date.now(),this.lastModFileTime().getTime())};var on=function(t){if(this.data=t,101010256!==this.data.readUInt32LE(0))throw new f(o.EINVAL,"Invalid Zip file: End of central directory record has invalid signature: "+this.data.readUInt32LE(0))};on.prototype.diskNumber=function(){return this.data.readUInt16LE(4)},on.prototype.cdDiskNumber=function(){return this.data.readUInt16LE(6)},on.prototype.cdDiskEntryCount=function(){return this.data.readUInt16LE(8)},on.prototype.cdTotalEntryCount=function(){return this.data.readUInt16LE(10)},on.prototype.cdSize=function(){return this.data.readUInt32LE(12)},on.prototype.cdOffset=function(){return this.data.readUInt32LE(16)},on.prototype.cdZipCommentLength=function(){return this.data.readUInt16LE(20)},on.prototype.cdZipComment=function(){return tn(this.data,!0,22,this.cdZipCommentLength())},on.prototype.rawCdZipComment=function(){return this.data.slice(22,22+this.cdZipCommentLength())};var sn=function(t,e,n,r){this.index=t,this.directoryEntries=e,this.eocd=n,this.data=r},an=function(t){function e(e,n){void 0===n&&(n=""),t.call(this),this.name=n,this._index=new vt,this._directoryEntries=[],this._eocd=null,this._index=e.index,this._directoryEntries=e.directoryEntries,this._eocd=e.eocd,this.data=e.data}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.Create=function(t,n){try{e._computeIndex(t.zipData,(function(r,i){if(i){var o=new e(i,t.name);n(null,o)}else n(r)}))}catch(t){n(t)}},e.isAvailable=function(){return!0},e.RegisterDecompressionMethod=function(t,e){Qe[t]=e},e._getEOCD=function(t){for(var e=Math.min(65557,t.length-1),n=22;n<e;n++)if(101010256===t.readUInt32LE(t.length-n))return new on(t.slice(t.length-n));throw new f(o.EINVAL,"Invalid ZIP file: Could not locate End of Central Directory signature.")},e._addToIndex=function(t,e){var n=t.fileName();if("/"===n.charAt(0))throw new f(o.EPERM,"Unexpectedly encountered an absolute path in a zip file. Please file a bug.");"/"===n.charAt(n.length-1)&&(n=n.substr(0,n.length-1)),t.isDirectory()?e.addPathFast("/"+n,new bt(t)):e.addPathFast("/"+n,new _t(t))},e._computeIndex=function(t,n){try{var r=new vt,i=e._getEOCD(t);if(i.diskNumber()!==i.cdDiskNumber())return n(new f(o.EINVAL,"ZipFS does not support spanned zip files."));var s=i.cdOffset();if(4294967295===s)return n(new f(o.EINVAL,"ZipFS does not support Zip64."));var a=s+i.cdSize();e._computeIndexResponsive(t,r,s,a,n,[],i)}catch(t){n(t)}},e._computeIndexResponsiveTrampoline=function(t,n,r,i,o,s,a){try{e._computeIndexResponsive(t,n,r,i,o,s,a)}catch(t){o(t)}},e._computeIndexResponsive=function(t,n,r,i,o,s,a){if(r<i){for(var c=0;c++<200&&r<i;){var u=new rn(t,t.slice(r));e._addToIndex(u,n),r+=u.totalSize(),s.push(u)}E((function(){e._computeIndexResponsiveTrampoline(t,n,r,i,o,s,a)}))}else o(null,new sn(n,s,a,t))},e.prototype.getName=function(){return e.Name+(""!==this.name?" "+this.name:"")},e.prototype.getCentralDirectoryEntry=function(t){var e=this._index.getInode(t);if(null===e)throw f.ENOENT(t);if(Et(e))return e.getData();if(St(e))return e.getData();throw f.EPERM("Invalid inode: "+e)},e.prototype.getCentralDirectoryEntryAt=function(t){var e=this._directoryEntries[t];if(!e)throw new RangeError("Invalid directory index: "+t+".");return e},e.prototype.getNumberOfCentralDirectoryEntries=function(){return this._directoryEntries.length},e.prototype.getEndOfCentralDirectory=function(){return this._eocd},e.prototype.diskSpace=function(t,e){e(this.data.length,0)},e.prototype.isReadOnly=function(){return!0},e.prototype.supportsLinks=function(){return!1},e.prototype.supportsProps=function(){return!1},e.prototype.supportsSynch=function(){return!0},e.prototype.statSync=function(t,e){var n,r=this._index.getInode(t);if(null===r)throw f.ENOENT(t);if(Et(r))n=r.getData().getStats();else{if(!St(r))throw new f(o.EINVAL,"Invalid inode.");n=r.getStats()}return n},e.prototype.openSync=function(t,e,n){if(e.isWriteable())throw new f(o.EPERM,t);var r=this._index.getInode(t);if(!r)throw f.ENOENT(t);if(!Et(r))throw f.EISDIR(t);var i=r.getData(),s=i.getStats();switch(e.pathExistsAction()){case u.THROW_EXCEPTION:case u.TRUNCATE_FILE:throw f.EEXIST(t);case u.NOP:return new at(this,t,e,s,i.getData());default:throw new f(o.EINVAL,"Invalid FileMode object.")}},e.prototype.readdirSync=function(t){var e=this._index.getInode(t);if(e){if(St(e))return e.getListing();throw f.ENOTDIR(t)}throw f.ENOENT(t)},e.prototype.readFileSync=function(t,e,n){var r=this.openSync(t,n,420);try{var i=r.getBuffer();return null===e?Y(i):i.toString(e)}finally{r.closeSync()}},e}(it);an.Name="ZipFS",an.Options={zipData:{type:"object",description:"The zip file as a Buffer object.",validator:G},name:{type:"string",optional:!0,description:"The name of the zip file (optional)."}},an.CompressionMethod=Ke,an.RegisterDecompressionMethod(Ke.DEFLATE,(function(t,e,n){return V(Ge(t.slice(0,e),{chunkSize:n}))})),an.RegisterDecompressionMethod(Ke.STORED,(function(t,e,n){return Y(t,0,n)})),[ut,se,ie,Lt,Ie,fe,he,Ze,It,Ut,Oe,Fe,an,xt,Tt,Re,At].forEach((function(t){var e=t.Create;t.Create=function(n,r){var i="function"==typeof n,o=i?n:r,s=i?{}:n;Q(t,s,(function(n){n?o(n):e.call(t,s,o)}))}}));var cn,un={AsyncMirror:ut,FolderAdapter:Lt,InMemory:se,IndexedDB:ie,OverlayFS:Ie,LocalStorage:fe,MountableFileSystem:he,WorkerFS:Ze,BundledHTTPRequest:It,HTTPRequest:Ut,UNPKGRequest:Oe,JSDelivrRequest:Fe,XmlHttpRequest:Ut,ZipFS:an,CodeSandboxFS:xt,CodeSandboxEditorFS:Tt,WebsocketFS:Re,DynamicHTTPRequest:At};function fn(t){switch(t){case"fs":return P;case"path":return a;case"buffer":return s;case"process":return i;case"bfs_utils":return $;default:return un[t]}}function hn(t){return P.initialize(t)}function ln(t,e){var n=t.fs;if(!n)return e(new f(o.EPERM,'Missing "fs" property on configuration object.'));var r=t.options,i=0,s=!1;function a(){if(!s){s=!0;var t=un[n];t?t.Create(r,e):e(new f(o.EPERM,"File system "+n+" is not available in BrowserFS."))}}if(null!==r&&"object"==typeof r){var c=!1;Object.keys(r).filter((function(t){return"fs"!==t})).forEach((function(t){var n=r[t];null!==n&&"object"==typeof n&&n.fs&&(i++,ln(n,(function(n,o){if(i--,n){if(s)return;s=!0,e(n)}else r[t]=o,0===i&&c&&a()})))})),c=!0}0===i&&a()}i.initializeTTYs&&i.initializeTTYs(),"b"!=="ab".substr(-1)&&(String.prototype.substr=(cn=String.prototype.substr,function(t,e){return t<0&&(t=this.length+t),cn.call(this,t,e)})),"undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array&&(Uint8Array.prototype.slice||(Uint8Array.prototype.slice=function(t,e){void 0===t&&(t=0),void 0===e&&(e=this.length);return t<0&&(t=this.length+t)<0&&(t=0),e<0&&(e=this.length+e)<0&&(e=0),e<t&&(e=t),new Uint8Array(this.buffer,this.byteOffset+t,e-t)})),e.BFSRequire=fn,e.EmscriptenFS=nt,e.Errors=h,e.FileSystem=un,e.configure=function(t,e){ln(t,(function(t,n){n?(hn(n),e()):e(t)}))},e.getFileSystem=ln,e.initialize=hn,e.install=function(e){e.Buffer=t,e.process=i;var n=e.require?e.require:null;e.require=function(t){var e=fn(t);return e||n.apply(null,Array.prototype.slice.call(arguments,0))}},e.registerFileSystem=function(t,e){un[t]=e},e.setImmediate=E}).call(this,n(12),n(6),n(3))},function(t,e,n){"use strict";e.byteLength=function(t){var e=u(t),n=e[0],r=e[1];return 3*(n+r)/4-r},e.toByteArray=function(t){var e,n,r=u(t),s=r[0],a=r[1],c=new o(function(t,e,n){return 3*(e+n)/4-n}(0,s,a)),f=0,h=a>0?s-4:s;for(n=0;n<h;n+=4)e=i[t.charCodeAt(n)]<<18|i[t.charCodeAt(n+1)]<<12|i[t.charCodeAt(n+2)]<<6|i[t.charCodeAt(n+3)],c[f++]=e>>16&255,c[f++]=e>>8&255,c[f++]=255&e;2===a&&(e=i[t.charCodeAt(n)]<<2|i[t.charCodeAt(n+1)]>>4,c[f++]=255&e);1===a&&(e=i[t.charCodeAt(n)]<<10|i[t.charCodeAt(n+1)]<<4|i[t.charCodeAt(n+2)]>>2,c[f++]=e>>8&255,c[f++]=255&e);return c},e.fromByteArray=function(t){for(var e,n=t.length,i=n%3,o=[],s=0,a=n-i;s<a;s+=16383)o.push(f(t,s,s+16383>a?a:s+16383));1===i?(e=t[n-1],o.push(r[e>>2]+r[e<<4&63]+"==")):2===i&&(e=(t[n-2]<<8)+t[n-1],o.push(r[e>>10]+r[e>>4&63]+r[e<<2&63]+"="));return o.join("")};for(var r=[],i=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,c=s.length;a<c;++a)r[a]=s[a],i[s.charCodeAt(a)]=a;function u(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var n=t.indexOf("=");return-1===n&&(n=e),[n,n===e?0:4-n%4]}function f(t,e,n){for(var i,o,s=[],a=e;a<n;a+=3)i=(t[a]<<16&16711680)+(t[a+1]<<8&65280)+(255&t[a+2]),s.push(r[(o=i)>>18&63]+r[o>>12&63]+r[o>>6&63]+r[63&o]);return s.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},function(t,e){
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
e.read=function(t,e,n,r,i){var o,s,a=8*i-r-1,c=(1<<a)-1,u=c>>1,f=-7,h=n?i-1:0,l=n?-1:1,p=t[e+h];for(h+=l,o=p&(1<<-f)-1,p>>=-f,f+=a;f>0;o=256*o+t[e+h],h+=l,f-=8);for(s=o&(1<<-f)-1,o>>=-f,f+=r;f>0;s=256*s+t[e+h],h+=l,f-=8);if(0===o)o=1-u;else{if(o===c)return s?NaN:1/0*(p?-1:1);s+=Math.pow(2,r),o-=u}return(p?-1:1)*s*Math.pow(2,o-r)},e.write=function(t,e,n,r,i,o){var s,a,c,u=8*o-i-1,f=(1<<u)-1,h=f>>1,l=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,p=r?0:o-1,d=r?1:-1,y=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(a=isNaN(e)?1:0,s=f):(s=Math.floor(Math.log(e)/Math.LN2),e*(c=Math.pow(2,-s))<1&&(s--,c*=2),(e+=s+h>=1?l/c:l*Math.pow(2,1-h))*c>=2&&(s++,c/=2),s+h>=f?(a=0,s=f):s+h>=1?(a=(e*c-1)*Math.pow(2,i),s+=h):(a=e*Math.pow(2,h-1)*Math.pow(2,i),s=0));i>=8;t[n+p]=255&a,p+=d,a/=256,i-=8);for(s=s<<i|a,u+=i;u>0;t[n+p]=255&s,p+=d,s/=256,u-=8);t[n+p-d]|=128*y}},function(t,e,n){"use strict";(function(e){var r=this&&this.__extends||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);function r(){this.constructor=t}t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)},i=n(4),o=null,s=function(){function t(t,e){this.fun=t,this.array=e}return t.prototype.run=function(){this.fun.apply(null,this.array)},t}(),a=function(){function t(){this._queue=[],this._draining=!1,this._currentQueue=null,this._queueIndex=-1}return t.prototype.push=function(t){var e=this;1!==this._queue.push(t)||this._draining||setTimeout((function(){return e._drainQueue()}),0)},t.prototype._cleanUpNextTick=function(){this._draining=!1,this._currentQueue&&this._currentQueue.length?this._queue=this._currentQueue.concat(this._queue):this._queueIndex=-1,this._queue.length&&this._drainQueue()},t.prototype._drainQueue=function(){var t=this;if(!this._draining){var e=setTimeout((function(){return t._cleanUpNextTick()}));this._draining=!0;for(var n=this._queue.length;n;){for(this._currentQueue=this._queue,this._queue=[];++this._queueIndex<n;)this._currentQueue&&this._currentQueue[this._queueIndex].run();this._queueIndex=-1,n=this._queue.length}this._currentQueue=null,this._draining=!1,clearTimeout(e)}},t}(),c=function(t){function i(){t.apply(this,arguments),this.startTime=Date.now(),this._cwd="/",this.platform="browser",this.argv=[],this.execArgv=[],this.stdout=null,this.stderr=null,this.stdin=null,this.domain=null,this._queue=new a,this.execPath=e,this.env={},this.exitCode=0,this._gid=1,this._uid=1,this.version="v5.0",this.versions={http_parser:"0.0",node:"5.0",v8:"0.0",uv:"0.0",zlib:"0.0",ares:"0.0",icu:"0.0",modules:"0",openssl:"0.0"},this.config={target_defaults:{cflags:[],default_configuration:"Release",defines:[],include_dirs:[],libraries:[]},variables:{clang:0,host_arch:"x32",node_install_npm:!1,node_install_waf:!1,node_prefix:"",node_shared_cares:!1,node_shared_http_parser:!1,node_shared_libuv:!1,node_shared_zlib:!1,node_shared_v8:!1,node_use_dtrace:!1,node_use_etw:!1,node_use_openssl:!1,node_shared_openssl:!1,strict_aliasing:!1,target_arch:"x32",v8_use_snapshot:!1,v8_no_strict_aliasing:0,visibility:""}},this.pid=1e3*Math.random()|0,this.title="node",this.arch="x32",this._mask=18,this.connected=void 0}return r(i,t),i.prototype.chdir=function(t){null===o&&(o=n(13)),this._cwd=o.resolve(t)},i.prototype.cwd=function(){return this._cwd},i.prototype.uptime=function(){return(Date.now()-this.startTime)/1e3|0},i.prototype.nextTick=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];this._queue.push(new s(t,e))},i.prototype.abort=function(){this.emit("abort")},i.prototype.exit=function(t){this.exitCode=t,this.emit("exit",[t])},i.prototype.getgid=function(){return this._gid},i.prototype.setgid=function(t){this._gid="number"==typeof t?t:1},i.prototype.getuid=function(){return this._uid},i.prototype.setuid=function(t){this._uid="number"==typeof t?t:1},i.prototype.kill=function(t,e){this.emit("kill",[t,e])},i.prototype.memoryUsage=function(){return{rss:0,heapTotal:0,heapUsed:0}},i.prototype.umask=function(t){void 0===t&&(t=this._mask);var e=this._mask;return this._mask=t,this.emit("umask",[t]),e},i.prototype.hrtime=function(){var t,e=(t="undefined"!=typeof performance?performance.now():Date.now?Date.now():(new Date).getTime())/1e3|0;return[e,t=1e6*(t-=1e3*e)|0]},i.prototype.initializeTTYs=function(){if(null===this.stdout){var t=n(23);this.stdout=new t,this.stderr=new t,this.stdin=new t}},i.prototype.disconnect=function(){},i}(i.EventEmitter);t.exports=c}).call(this,"/")},function(t,e,n){"use strict";(function(e){var r=this&&this.__extends||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);function r(){this.constructor=t}t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)},i=function(t){function n(){t.call(this),this.isRaw=!1,this.columns=80,this.rows=120,this.isTTY=!0,this._bufferedWrites=[],this._waitingForWrites=!1}return r(n,t),n.prototype.setRawMode=function(t){this.isRaw!==t&&(this.isRaw=t,this.emit("modeChange"))},n.prototype.changeColumns=function(t){t!==this.columns&&(this.columns=t,this.emit("resize"))},n.prototype.changeRows=function(t){t!==this.rows&&(this.rows=t,this.emit("resize"))},n.isatty=function(t){return t&&t instanceof n},n.prototype._write=function(t,n,r){var i;try{var o;o="string"==typeof t?new e(t,n):t,this._bufferedWrites.push(o),this._waitingForWrites&&this._read(1024)}catch(t){i=t}finally{r(i)}},n.prototype._read=function(t){if(0===this._bufferedWrites.length)this._waitingForWrites=!0;else for(;this._bufferedWrites.length>0&&(this._waitingForWrites=this.push(this._bufferedWrites.shift()),this._waitingForWrites););},n}(n(24).Duplex);t.exports=i}).call(this,n(12))},function(t,e,n){t.exports=i;var r=n(4).EventEmitter;function i(){r.call(this)}n(1)(i,r),i.Readable=n(9),i.Writable=n(32),i.Duplex=n(33),i.Transform=n(34),i.PassThrough=n(35),i.Stream=i,i.prototype.pipe=function(t,e){var n=this;function i(e){t.writable&&!1===t.write(e)&&n.pause&&n.pause()}function o(){n.readable&&n.resume&&n.resume()}n.on("data",i),t.on("drain",o),t._isStdio||e&&!1===e.end||(n.on("end",a),n.on("close",c));var s=!1;function a(){s||(s=!0,t.end())}function c(){s||(s=!0,"function"==typeof t.destroy&&t.destroy())}function u(t){if(f(),0===r.listenerCount(this,"error"))throw t}function f(){n.removeListener("data",i),t.removeListener("drain",o),n.removeListener("end",a),n.removeListener("close",c),n.removeListener("error",u),t.removeListener("error",u),n.removeListener("end",f),n.removeListener("close",f),t.removeListener("close",f)}return n.on("error",u),t.on("error",u),n.on("end",f),n.on("close",f),t.on("close",f),t.emit("pipe",n),t}},function(t,e){var n={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==n.call(t)}},function(t,e){},function(t,e,n){"use strict";var r=n(10).Buffer,i=n(28);t.exports=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.head=null,this.tail=null,this.length=0}return t.prototype.push=function(t){var e={data:t,next:null};this.length>0?this.tail.next=e:this.head=e,this.tail=e,++this.length},t.prototype.unshift=function(t){var e={data:t,next:this.head};0===this.length&&(this.tail=e),this.head=e,++this.length},t.prototype.shift=function(){if(0!==this.length){var t=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,t}},t.prototype.clear=function(){this.head=this.tail=null,this.length=0},t.prototype.join=function(t){if(0===this.length)return"";for(var e=this.head,n=""+e.data;e=e.next;)n+=t+e.data;return n},t.prototype.concat=function(t){if(0===this.length)return r.alloc(0);for(var e,n,i,o=r.allocUnsafe(t>>>0),s=this.head,a=0;s;)e=s.data,n=o,i=a,e.copy(n,i),a+=s.data.length,s=s.next;return o},t}(),i&&i.inspect&&i.inspect.custom&&(t.exports.prototype[i.inspect.custom]=function(){var t=i.inspect({length:this.length});return this.constructor.name+" "+t})},function(t,e){},function(t,e,n){(function(e){function n(t){try{if(!e.localStorage)return!1}catch(t){return!1}var n=e.localStorage[t];return null!=n&&"true"===String(n).toLowerCase()}t.exports=function(t,e){if(n("noDeprecation"))return t;var r=!1;return function(){if(!r){if(n("throwDeprecation"))throw new Error(e);n("traceDeprecation")?console.trace(e):console.warn(e),r=!0}return t.apply(this,arguments)}}}).call(this,n(6))},function(t,e,n){
/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */
var r=n(2),i=r.Buffer;function o(t,e){for(var n in t)e[n]=t[n]}function s(t,e,n){return i(t,e,n)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?t.exports=r:(o(r,e),e.Buffer=s),s.prototype=Object.create(i.prototype),o(i,s),s.from=function(t,e,n){if("number"==typeof t)throw new TypeError("Argument must not be a number");return i(t,e,n)},s.alloc=function(t,e,n){if("number"!=typeof t)throw new TypeError("Argument must be a number");var r=i(t);return void 0!==e?"string"==typeof n?r.fill(e,n):r.fill(e):r.fill(0),r},s.allocUnsafe=function(t){if("number"!=typeof t)throw new TypeError("Argument must be a number");return i(t)},s.allocUnsafeSlow=function(t){if("number"!=typeof t)throw new TypeError("Argument must be a number");return r.SlowBuffer(t)}},function(t,e,n){"use strict";t.exports=o;var r=n(18),i=Object.create(n(5));function o(t){if(!(this instanceof o))return new o(t);r.call(this,t)}i.inherits=n(1),i.inherits(o,r),o.prototype._transform=function(t,e,n){n(null,t)}},function(t,e,n){t.exports=n(11)},function(t,e,n){t.exports=n(0)},function(t,e,n){t.exports=n(9).Transform},function(t,e,n){t.exports=n(9).PassThrough},function(t,e,n){"use strict";var r=n(37),i=n(8),o=n(42),s=n(43),a=n(44),c=n(45),u=n(46),f=Object.prototype.toString;function h(t){if(!(this instanceof h))return new h(t);this.options=i.assign({chunkSize:16384,windowBits:0,to:""},t||{});var e=this.options;e.raw&&e.windowBits>=0&&e.windowBits<16&&(e.windowBits=-e.windowBits,0===e.windowBits&&(e.windowBits=-15)),!(e.windowBits>=0&&e.windowBits<16)||t&&t.windowBits||(e.windowBits+=32),e.windowBits>15&&e.windowBits<48&&0==(15&e.windowBits)&&(e.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new c,this.strm.avail_out=0;var n=r.inflateInit2(this.strm,e.windowBits);if(n!==s.Z_OK)throw new Error(a[n]);if(this.header=new u,r.inflateGetHeader(this.strm,this.header),e.dictionary&&("string"==typeof e.dictionary?e.dictionary=o.string2buf(e.dictionary):"[object ArrayBuffer]"===f.call(e.dictionary)&&(e.dictionary=new Uint8Array(e.dictionary)),e.raw&&(n=r.inflateSetDictionary(this.strm,e.dictionary))!==s.Z_OK))throw new Error(a[n])}function l(t,e){var n=new h(e);if(n.push(t,!0),n.err)throw n.msg||a[n.err];return n.result}h.prototype.push=function(t,e){var n,a,c,u,h,l=this.strm,p=this.options.chunkSize,d=this.options.dictionary,y=!1;if(this.ended)return!1;a=e===~~e?e:!0===e?s.Z_FINISH:s.Z_NO_FLUSH,"string"==typeof t?l.input=o.binstring2buf(t):"[object ArrayBuffer]"===f.call(t)?l.input=new Uint8Array(t):l.input=t,l.next_in=0,l.avail_in=l.input.length;do{if(0===l.avail_out&&(l.output=new i.Buf8(p),l.next_out=0,l.avail_out=p),(n=r.inflate(l,s.Z_NO_FLUSH))===s.Z_NEED_DICT&&d&&(n=r.inflateSetDictionary(this.strm,d)),n===s.Z_BUF_ERROR&&!0===y&&(n=s.Z_OK,y=!1),n!==s.Z_STREAM_END&&n!==s.Z_OK)return this.onEnd(n),this.ended=!0,!1;l.next_out&&(0!==l.avail_out&&n!==s.Z_STREAM_END&&(0!==l.avail_in||a!==s.Z_FINISH&&a!==s.Z_SYNC_FLUSH)||("string"===this.options.to?(c=o.utf8border(l.output,l.next_out),u=l.next_out-c,h=o.buf2string(l.output,c),l.next_out=u,l.avail_out=p-u,u&&i.arraySet(l.output,l.output,c,u,0),this.onData(h)):this.onData(i.shrinkBuf(l.output,l.next_out)))),0===l.avail_in&&0===l.avail_out&&(y=!0)}while((l.avail_in>0||0===l.avail_out)&&n!==s.Z_STREAM_END);return n===s.Z_STREAM_END&&(a=s.Z_FINISH),a===s.Z_FINISH?(n=r.inflateEnd(this.strm),this.onEnd(n),this.ended=!0,n===s.Z_OK):a!==s.Z_SYNC_FLUSH||(this.onEnd(s.Z_OK),l.avail_out=0,!0)},h.prototype.onData=function(t){this.chunks.push(t)},h.prototype.onEnd=function(t){t===s.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=i.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},e.Inflate=h,e.inflate=l,e.inflateRaw=function(t,e){return(e=e||{}).raw=!0,l(t,e)},e.ungzip=l},function(t,e,n){"use strict";var r=n(8),i=n(38),o=n(39),s=n(40),a=n(41);function c(t){return(t>>>24&255)+(t>>>8&65280)+((65280&t)<<8)+((255&t)<<24)}function u(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new r.Buf16(320),this.work=new r.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function f(t){var e;return t&&t.state?(e=t.state,t.total_in=t.total_out=e.total=0,t.msg="",e.wrap&&(t.adler=1&e.wrap),e.mode=1,e.last=0,e.havedict=0,e.dmax=32768,e.head=null,e.hold=0,e.bits=0,e.lencode=e.lendyn=new r.Buf32(852),e.distcode=e.distdyn=new r.Buf32(592),e.sane=1,e.back=-1,0):-2}function h(t){var e;return t&&t.state?((e=t.state).wsize=0,e.whave=0,e.wnext=0,f(t)):-2}function l(t,e){var n,r;return t&&t.state?(r=t.state,e<0?(n=0,e=-e):(n=1+(e>>4),e<48&&(e&=15)),e&&(e<8||e>15)?-2:(null!==r.window&&r.wbits!==e&&(r.window=null),r.wrap=n,r.wbits=e,h(t))):-2}function p(t,e){var n,r;return t?(r=new u,t.state=r,r.window=null,0!==(n=l(t,e))&&(t.state=null),n):-2}var d,y,g=!0;function w(t){if(g){var e;for(d=new r.Buf32(512),y=new r.Buf32(32),e=0;e<144;)t.lens[e++]=8;for(;e<256;)t.lens[e++]=9;for(;e<280;)t.lens[e++]=7;for(;e<288;)t.lens[e++]=8;for(a(1,t.lens,0,288,d,0,t.work,{bits:9}),e=0;e<32;)t.lens[e++]=5;a(2,t.lens,0,32,y,0,t.work,{bits:5}),g=!1}t.lencode=d,t.lenbits=9,t.distcode=y,t.distbits=5}function m(t,e,n,i){var o,s=t.state;return null===s.window&&(s.wsize=1<<s.wbits,s.wnext=0,s.whave=0,s.window=new r.Buf8(s.wsize)),i>=s.wsize?(r.arraySet(s.window,e,n-s.wsize,s.wsize,0),s.wnext=0,s.whave=s.wsize):((o=s.wsize-s.wnext)>i&&(o=i),r.arraySet(s.window,e,n-i,o,s.wnext),(i-=o)?(r.arraySet(s.window,e,n-i,i,0),s.wnext=i,s.whave=s.wsize):(s.wnext+=o,s.wnext===s.wsize&&(s.wnext=0),s.whave<s.wsize&&(s.whave+=o))),0}e.inflateReset=h,e.inflateReset2=l,e.inflateResetKeep=f,e.inflateInit=function(t){return p(t,15)},e.inflateInit2=p,e.inflate=function(t,e){var n,u,f,h,l,p,d,y,g,v,_,b,E,S,k,I,N,O,T,F,x,R,A,L,D=0,P=new r.Buf8(4),C=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!t||!t.state||!t.output||!t.input&&0!==t.avail_in)return-2;12===(n=t.state).mode&&(n.mode=13),l=t.next_out,f=t.output,d=t.avail_out,h=t.next_in,u=t.input,p=t.avail_in,y=n.hold,g=n.bits,v=p,_=d,R=0;t:for(;;)switch(n.mode){case 1:if(0===n.wrap){n.mode=13;break}for(;g<16;){if(0===p)break t;p--,y+=u[h++]<<g,g+=8}if(2&n.wrap&&35615===y){n.check=0,P[0]=255&y,P[1]=y>>>8&255,n.check=o(n.check,P,2,0),y=0,g=0,n.mode=2;break}if(n.flags=0,n.head&&(n.head.done=!1),!(1&n.wrap)||(((255&y)<<8)+(y>>8))%31){t.msg="incorrect header check",n.mode=30;break}if(8!=(15&y)){t.msg="unknown compression method",n.mode=30;break}if(g-=4,x=8+(15&(y>>>=4)),0===n.wbits)n.wbits=x;else if(x>n.wbits){t.msg="invalid window size",n.mode=30;break}n.dmax=1<<x,t.adler=n.check=1,n.mode=512&y?10:12,y=0,g=0;break;case 2:for(;g<16;){if(0===p)break t;p--,y+=u[h++]<<g,g+=8}if(n.flags=y,8!=(255&n.flags)){t.msg="unknown compression method",n.mode=30;break}if(57344&n.flags){t.msg="unknown header flags set",n.mode=30;break}n.head&&(n.head.text=y>>8&1),512&n.flags&&(P[0]=255&y,P[1]=y>>>8&255,n.check=o(n.check,P,2,0)),y=0,g=0,n.mode=3;case 3:for(;g<32;){if(0===p)break t;p--,y+=u[h++]<<g,g+=8}n.head&&(n.head.time=y),512&n.flags&&(P[0]=255&y,P[1]=y>>>8&255,P[2]=y>>>16&255,P[3]=y>>>24&255,n.check=o(n.check,P,4,0)),y=0,g=0,n.mode=4;case 4:for(;g<16;){if(0===p)break t;p--,y+=u[h++]<<g,g+=8}n.head&&(n.head.xflags=255&y,n.head.os=y>>8),512&n.flags&&(P[0]=255&y,P[1]=y>>>8&255,n.check=o(n.check,P,2,0)),y=0,g=0,n.mode=5;case 5:if(1024&n.flags){for(;g<16;){if(0===p)break t;p--,y+=u[h++]<<g,g+=8}n.length=y,n.head&&(n.head.extra_len=y),512&n.flags&&(P[0]=255&y,P[1]=y>>>8&255,n.check=o(n.check,P,2,0)),y=0,g=0}else n.head&&(n.head.extra=null);n.mode=6;case 6:if(1024&n.flags&&((b=n.length)>p&&(b=p),b&&(n.head&&(x=n.head.extra_len-n.length,n.head.extra||(n.head.extra=new Array(n.head.extra_len)),r.arraySet(n.head.extra,u,h,b,x)),512&n.flags&&(n.check=o(n.check,u,b,h)),p-=b,h+=b,n.length-=b),n.length))break t;n.length=0,n.mode=7;case 7:if(2048&n.flags){if(0===p)break t;b=0;do{x=u[h+b++],n.head&&x&&n.length<65536&&(n.head.name+=String.fromCharCode(x))}while(x&&b<p);if(512&n.flags&&(n.check=o(n.check,u,b,h)),p-=b,h+=b,x)break t}else n.head&&(n.head.name=null);n.length=0,n.mode=8;case 8:if(4096&n.flags){if(0===p)break t;b=0;do{x=u[h+b++],n.head&&x&&n.length<65536&&(n.head.comment+=String.fromCharCode(x))}while(x&&b<p);if(512&n.flags&&(n.check=o(n.check,u,b,h)),p-=b,h+=b,x)break t}else n.head&&(n.head.comment=null);n.mode=9;case 9:if(512&n.flags){for(;g<16;){if(0===p)break t;p--,y+=u[h++]<<g,g+=8}if(y!==(65535&n.check)){t.msg="header crc mismatch",n.mode=30;break}y=0,g=0}n.head&&(n.head.hcrc=n.flags>>9&1,n.head.done=!0),t.adler=n.check=0,n.mode=12;break;case 10:for(;g<32;){if(0===p)break t;p--,y+=u[h++]<<g,g+=8}t.adler=n.check=c(y),y=0,g=0,n.mode=11;case 11:if(0===n.havedict)return t.next_out=l,t.avail_out=d,t.next_in=h,t.avail_in=p,n.hold=y,n.bits=g,2;t.adler=n.check=1,n.mode=12;case 12:if(5===e||6===e)break t;case 13:if(n.last){y>>>=7&g,g-=7&g,n.mode=27;break}for(;g<3;){if(0===p)break t;p--,y+=u[h++]<<g,g+=8}switch(n.last=1&y,g-=1,3&(y>>>=1)){case 0:n.mode=14;break;case 1:if(w(n),n.mode=20,6===e){y>>>=2,g-=2;break t}break;case 2:n.mode=17;break;case 3:t.msg="invalid block type",n.mode=30}y>>>=2,g-=2;break;case 14:for(y>>>=7&g,g-=7&g;g<32;){if(0===p)break t;p--,y+=u[h++]<<g,g+=8}if((65535&y)!=(y>>>16^65535)){t.msg="invalid stored block lengths",n.mode=30;break}if(n.length=65535&y,y=0,g=0,n.mode=15,6===e)break t;case 15:n.mode=16;case 16:if(b=n.length){if(b>p&&(b=p),b>d&&(b=d),0===b)break t;r.arraySet(f,u,h,b,l),p-=b,h+=b,d-=b,l+=b,n.length-=b;break}n.mode=12;break;case 17:for(;g<14;){if(0===p)break t;p--,y+=u[h++]<<g,g+=8}if(n.nlen=257+(31&y),y>>>=5,g-=5,n.ndist=1+(31&y),y>>>=5,g-=5,n.ncode=4+(15&y),y>>>=4,g-=4,n.nlen>286||n.ndist>30){t.msg="too many length or distance symbols",n.mode=30;break}n.have=0,n.mode=18;case 18:for(;n.have<n.ncode;){for(;g<3;){if(0===p)break t;p--,y+=u[h++]<<g,g+=8}n.lens[C[n.have++]]=7&y,y>>>=3,g-=3}for(;n.have<19;)n.lens[C[n.have++]]=0;if(n.lencode=n.lendyn,n.lenbits=7,A={bits:n.lenbits},R=a(0,n.lens,0,19,n.lencode,0,n.work,A),n.lenbits=A.bits,R){t.msg="invalid code lengths set",n.mode=30;break}n.have=0,n.mode=19;case 19:for(;n.have<n.nlen+n.ndist;){for(;I=(D=n.lencode[y&(1<<n.lenbits)-1])>>>16&255,N=65535&D,!((k=D>>>24)<=g);){if(0===p)break t;p--,y+=u[h++]<<g,g+=8}if(N<16)y>>>=k,g-=k,n.lens[n.have++]=N;else{if(16===N){for(L=k+2;g<L;){if(0===p)break t;p--,y+=u[h++]<<g,g+=8}if(y>>>=k,g-=k,0===n.have){t.msg="invalid bit length repeat",n.mode=30;break}x=n.lens[n.have-1],b=3+(3&y),y>>>=2,g-=2}else if(17===N){for(L=k+3;g<L;){if(0===p)break t;p--,y+=u[h++]<<g,g+=8}g-=k,x=0,b=3+(7&(y>>>=k)),y>>>=3,g-=3}else{for(L=k+7;g<L;){if(0===p)break t;p--,y+=u[h++]<<g,g+=8}g-=k,x=0,b=11+(127&(y>>>=k)),y>>>=7,g-=7}if(n.have+b>n.nlen+n.ndist){t.msg="invalid bit length repeat",n.mode=30;break}for(;b--;)n.lens[n.have++]=x}}if(30===n.mode)break;if(0===n.lens[256]){t.msg="invalid code -- missing end-of-block",n.mode=30;break}if(n.lenbits=9,A={bits:n.lenbits},R=a(1,n.lens,0,n.nlen,n.lencode,0,n.work,A),n.lenbits=A.bits,R){t.msg="invalid literal/lengths set",n.mode=30;break}if(n.distbits=6,n.distcode=n.distdyn,A={bits:n.distbits},R=a(2,n.lens,n.nlen,n.ndist,n.distcode,0,n.work,A),n.distbits=A.bits,R){t.msg="invalid distances set",n.mode=30;break}if(n.mode=20,6===e)break t;case 20:n.mode=21;case 21:if(p>=6&&d>=258){t.next_out=l,t.avail_out=d,t.next_in=h,t.avail_in=p,n.hold=y,n.bits=g,s(t,_),l=t.next_out,f=t.output,d=t.avail_out,h=t.next_in,u=t.input,p=t.avail_in,y=n.hold,g=n.bits,12===n.mode&&(n.back=-1);break}for(n.back=0;I=(D=n.lencode[y&(1<<n.lenbits)-1])>>>16&255,N=65535&D,!((k=D>>>24)<=g);){if(0===p)break t;p--,y+=u[h++]<<g,g+=8}if(I&&0==(240&I)){for(O=k,T=I,F=N;I=(D=n.lencode[F+((y&(1<<O+T)-1)>>O)])>>>16&255,N=65535&D,!(O+(k=D>>>24)<=g);){if(0===p)break t;p--,y+=u[h++]<<g,g+=8}y>>>=O,g-=O,n.back+=O}if(y>>>=k,g-=k,n.back+=k,n.length=N,0===I){n.mode=26;break}if(32&I){n.back=-1,n.mode=12;break}if(64&I){t.msg="invalid literal/length code",n.mode=30;break}n.extra=15&I,n.mode=22;case 22:if(n.extra){for(L=n.extra;g<L;){if(0===p)break t;p--,y+=u[h++]<<g,g+=8}n.length+=y&(1<<n.extra)-1,y>>>=n.extra,g-=n.extra,n.back+=n.extra}n.was=n.length,n.mode=23;case 23:for(;I=(D=n.distcode[y&(1<<n.distbits)-1])>>>16&255,N=65535&D,!((k=D>>>24)<=g);){if(0===p)break t;p--,y+=u[h++]<<g,g+=8}if(0==(240&I)){for(O=k,T=I,F=N;I=(D=n.distcode[F+((y&(1<<O+T)-1)>>O)])>>>16&255,N=65535&D,!(O+(k=D>>>24)<=g);){if(0===p)break t;p--,y+=u[h++]<<g,g+=8}y>>>=O,g-=O,n.back+=O}if(y>>>=k,g-=k,n.back+=k,64&I){t.msg="invalid distance code",n.mode=30;break}n.offset=N,n.extra=15&I,n.mode=24;case 24:if(n.extra){for(L=n.extra;g<L;){if(0===p)break t;p--,y+=u[h++]<<g,g+=8}n.offset+=y&(1<<n.extra)-1,y>>>=n.extra,g-=n.extra,n.back+=n.extra}if(n.offset>n.dmax){t.msg="invalid distance too far back",n.mode=30;break}n.mode=25;case 25:if(0===d)break t;if(b=_-d,n.offset>b){if((b=n.offset-b)>n.whave&&n.sane){t.msg="invalid distance too far back",n.mode=30;break}b>n.wnext?(b-=n.wnext,E=n.wsize-b):E=n.wnext-b,b>n.length&&(b=n.length),S=n.window}else S=f,E=l-n.offset,b=n.length;b>d&&(b=d),d-=b,n.length-=b;do{f[l++]=S[E++]}while(--b);0===n.length&&(n.mode=21);break;case 26:if(0===d)break t;f[l++]=n.length,d--,n.mode=21;break;case 27:if(n.wrap){for(;g<32;){if(0===p)break t;p--,y|=u[h++]<<g,g+=8}if(_-=d,t.total_out+=_,n.total+=_,_&&(t.adler=n.check=n.flags?o(n.check,f,_,l-_):i(n.check,f,_,l-_)),_=d,(n.flags?y:c(y))!==n.check){t.msg="incorrect data check",n.mode=30;break}y=0,g=0}n.mode=28;case 28:if(n.wrap&&n.flags){for(;g<32;){if(0===p)break t;p--,y+=u[h++]<<g,g+=8}if(y!==(4294967295&n.total)){t.msg="incorrect length check",n.mode=30;break}y=0,g=0}n.mode=29;case 29:R=1;break t;case 30:R=-3;break t;case 31:return-4;case 32:default:return-2}return t.next_out=l,t.avail_out=d,t.next_in=h,t.avail_in=p,n.hold=y,n.bits=g,(n.wsize||_!==t.avail_out&&n.mode<30&&(n.mode<27||4!==e))&&m(t,t.output,t.next_out,_-t.avail_out)?(n.mode=31,-4):(v-=t.avail_in,_-=t.avail_out,t.total_in+=v,t.total_out+=_,n.total+=_,n.wrap&&_&&(t.adler=n.check=n.flags?o(n.check,f,_,t.next_out-_):i(n.check,f,_,t.next_out-_)),t.data_type=n.bits+(n.last?64:0)+(12===n.mode?128:0)+(20===n.mode||15===n.mode?256:0),(0===v&&0===_||4===e)&&0===R&&(R=-5),R)},e.inflateEnd=function(t){if(!t||!t.state)return-2;var e=t.state;return e.window&&(e.window=null),t.state=null,0},e.inflateGetHeader=function(t,e){var n;return t&&t.state?0==(2&(n=t.state).wrap)?-2:(n.head=e,e.done=!1,0):-2},e.inflateSetDictionary=function(t,e){var n,r=e.length;return t&&t.state?0!==(n=t.state).wrap&&11!==n.mode?-2:11===n.mode&&i(1,e,r,0)!==n.check?-3:m(t,e,r,r)?(n.mode=31,-4):(n.havedict=1,0):-2},e.inflateInfo="pako inflate (from Nodeca project)"},function(t,e,n){"use strict";t.exports=function(t,e,n,r){for(var i=65535&t|0,o=t>>>16&65535|0,s=0;0!==n;){n-=s=n>2e3?2e3:n;do{o=o+(i=i+e[r++]|0)|0}while(--s);i%=65521,o%=65521}return i|o<<16|0}},function(t,e,n){"use strict";var r=function(){for(var t,e=[],n=0;n<256;n++){t=n;for(var r=0;r<8;r++)t=1&t?3988292384^t>>>1:t>>>1;e[n]=t}return e}();t.exports=function(t,e,n,i){var o=r,s=i+n;t^=-1;for(var a=i;a<s;a++)t=t>>>8^o[255&(t^e[a])];return-1^t}},function(t,e,n){"use strict";t.exports=function(t,e){var n,r,i,o,s,a,c,u,f,h,l,p,d,y,g,w,m,v,_,b,E,S,k,I,N;n=t.state,r=t.next_in,I=t.input,i=r+(t.avail_in-5),o=t.next_out,N=t.output,s=o-(e-t.avail_out),a=o+(t.avail_out-257),c=n.dmax,u=n.wsize,f=n.whave,h=n.wnext,l=n.window,p=n.hold,d=n.bits,y=n.lencode,g=n.distcode,w=(1<<n.lenbits)-1,m=(1<<n.distbits)-1;t:do{d<15&&(p+=I[r++]<<d,d+=8,p+=I[r++]<<d,d+=8),v=y[p&w];e:for(;;){if(p>>>=_=v>>>24,d-=_,0===(_=v>>>16&255))N[o++]=65535&v;else{if(!(16&_)){if(0==(64&_)){v=y[(65535&v)+(p&(1<<_)-1)];continue e}if(32&_){n.mode=12;break t}t.msg="invalid literal/length code",n.mode=30;break t}b=65535&v,(_&=15)&&(d<_&&(p+=I[r++]<<d,d+=8),b+=p&(1<<_)-1,p>>>=_,d-=_),d<15&&(p+=I[r++]<<d,d+=8,p+=I[r++]<<d,d+=8),v=g[p&m];n:for(;;){if(p>>>=_=v>>>24,d-=_,!(16&(_=v>>>16&255))){if(0==(64&_)){v=g[(65535&v)+(p&(1<<_)-1)];continue n}t.msg="invalid distance code",n.mode=30;break t}if(E=65535&v,d<(_&=15)&&(p+=I[r++]<<d,(d+=8)<_&&(p+=I[r++]<<d,d+=8)),(E+=p&(1<<_)-1)>c){t.msg="invalid distance too far back",n.mode=30;break t}if(p>>>=_,d-=_,E>(_=o-s)){if((_=E-_)>f&&n.sane){t.msg="invalid distance too far back",n.mode=30;break t}if(S=0,k=l,0===h){if(S+=u-_,_<b){b-=_;do{N[o++]=l[S++]}while(--_);S=o-E,k=N}}else if(h<_){if(S+=u+h-_,(_-=h)<b){b-=_;do{N[o++]=l[S++]}while(--_);if(S=0,h<b){b-=_=h;do{N[o++]=l[S++]}while(--_);S=o-E,k=N}}}else if(S+=h-_,_<b){b-=_;do{N[o++]=l[S++]}while(--_);S=o-E,k=N}for(;b>2;)N[o++]=k[S++],N[o++]=k[S++],N[o++]=k[S++],b-=3;b&&(N[o++]=k[S++],b>1&&(N[o++]=k[S++]))}else{S=o-E;do{N[o++]=N[S++],N[o++]=N[S++],N[o++]=N[S++],b-=3}while(b>2);b&&(N[o++]=N[S++],b>1&&(N[o++]=N[S++]))}break}}break}}while(r<i&&o<a);r-=b=d>>3,p&=(1<<(d-=b<<3))-1,t.next_in=r,t.next_out=o,t.avail_in=r<i?i-r+5:5-(r-i),t.avail_out=o<a?a-o+257:257-(o-a),n.hold=p,n.bits=d}},function(t,e,n){"use strict";var r=n(8),i=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],o=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],s=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],a=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];t.exports=function(t,e,n,c,u,f,h,l){var p,d,y,g,w,m,v,_,b,E=l.bits,S=0,k=0,I=0,N=0,O=0,T=0,F=0,x=0,R=0,A=0,L=null,D=0,P=new r.Buf16(16),C=new r.Buf16(16),M=null,U=0;for(S=0;S<=15;S++)P[S]=0;for(k=0;k<c;k++)P[e[n+k]]++;for(O=E,N=15;N>=1&&0===P[N];N--);if(O>N&&(O=N),0===N)return u[f++]=20971520,u[f++]=20971520,l.bits=1,0;for(I=1;I<N&&0===P[I];I++);for(O<I&&(O=I),x=1,S=1;S<=15;S++)if(x<<=1,(x-=P[S])<0)return-1;if(x>0&&(0===t||1!==N))return-1;for(C[1]=0,S=1;S<15;S++)C[S+1]=C[S]+P[S];for(k=0;k<c;k++)0!==e[n+k]&&(h[C[e[n+k]]++]=k);if(0===t?(L=M=h,m=19):1===t?(L=i,D-=257,M=o,U-=257,m=256):(L=s,M=a,m=-1),A=0,k=0,S=I,w=f,T=O,F=0,y=-1,g=(R=1<<O)-1,1===t&&R>852||2===t&&R>592)return 1;for(;;){v=S-F,h[k]<m?(_=0,b=h[k]):h[k]>m?(_=M[U+h[k]],b=L[D+h[k]]):(_=96,b=0),p=1<<S-F,I=d=1<<T;do{u[w+(A>>F)+(d-=p)]=v<<24|_<<16|b|0}while(0!==d);for(p=1<<S-1;A&p;)p>>=1;if(0!==p?(A&=p-1,A+=p):A=0,k++,0==--P[S]){if(S===N)break;S=e[n+h[k]]}if(S>O&&(A&g)!==y){for(0===F&&(F=O),w+=I,x=1<<(T=S-F);T+F<N&&!((x-=P[T+F])<=0);)T++,x<<=1;if(R+=1<<T,1===t&&R>852||2===t&&R>592)return 1;u[y=A&g]=O<<24|T<<16|w-f|0}}return 0!==A&&(u[w+A]=S-F<<24|64<<16|0),l.bits=O,0}},function(t,e,n){"use strict";var r=n(8),i=!0,o=!0;try{String.fromCharCode.apply(null,[0])}catch(t){i=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(t){o=!1}for(var s=new r.Buf8(256),a=0;a<256;a++)s[a]=a>=252?6:a>=248?5:a>=240?4:a>=224?3:a>=192?2:1;function c(t,e){if(e<65534&&(t.subarray&&o||!t.subarray&&i))return String.fromCharCode.apply(null,r.shrinkBuf(t,e));for(var n="",s=0;s<e;s++)n+=String.fromCharCode(t[s]);return n}s[254]=s[254]=1,e.string2buf=function(t){var e,n,i,o,s,a=t.length,c=0;for(o=0;o<a;o++)55296==(64512&(n=t.charCodeAt(o)))&&o+1<a&&56320==(64512&(i=t.charCodeAt(o+1)))&&(n=65536+(n-55296<<10)+(i-56320),o++),c+=n<128?1:n<2048?2:n<65536?3:4;for(e=new r.Buf8(c),s=0,o=0;s<c;o++)55296==(64512&(n=t.charCodeAt(o)))&&o+1<a&&56320==(64512&(i=t.charCodeAt(o+1)))&&(n=65536+(n-55296<<10)+(i-56320),o++),n<128?e[s++]=n:n<2048?(e[s++]=192|n>>>6,e[s++]=128|63&n):n<65536?(e[s++]=224|n>>>12,e[s++]=128|n>>>6&63,e[s++]=128|63&n):(e[s++]=240|n>>>18,e[s++]=128|n>>>12&63,e[s++]=128|n>>>6&63,e[s++]=128|63&n);return e},e.buf2binstring=function(t){return c(t,t.length)},e.binstring2buf=function(t){for(var e=new r.Buf8(t.length),n=0,i=e.length;n<i;n++)e[n]=t.charCodeAt(n);return e},e.buf2string=function(t,e){var n,r,i,o,a=e||t.length,u=new Array(2*a);for(r=0,n=0;n<a;)if((i=t[n++])<128)u[r++]=i;else if((o=s[i])>4)u[r++]=65533,n+=o-1;else{for(i&=2===o?31:3===o?15:7;o>1&&n<a;)i=i<<6|63&t[n++],o--;o>1?u[r++]=65533:i<65536?u[r++]=i:(i-=65536,u[r++]=55296|i>>10&1023,u[r++]=56320|1023&i)}return c(u,r)},e.utf8border=function(t,e){var n;for((e=e||t.length)>t.length&&(e=t.length),n=e-1;n>=0&&128==(192&t[n]);)n--;return n<0||0===n?e:n+s[t[n]]>e?n:e}},function(t,e,n){"use strict";t.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},function(t,e,n){"use strict";t.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},function(t,e,n){"use strict";t.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},function(t,e,n){"use strict";t.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}}])}));