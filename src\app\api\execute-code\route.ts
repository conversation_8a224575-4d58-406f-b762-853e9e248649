import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { code, language } = await request.json();

    if (!code || !language) {
      return NextResponse.json(
        { error: 'Code and language are required' },
        { status: 400 }
      );
    }

    // Simulate code execution with different languages
    let output = '';
    let error = '';

    try {
      switch (language.toLowerCase()) {
        case 'javascript':
          output = executeJavaScript(code);
          break;
        case 'python':
          output = executePython(code);
          break;
        case 'html':
          output = executeHTML(code);
          break;
        case 'css':
          output = executeCSS(code);
          break;
        case 'sql':
          output = executeSQL(code);
          break;
        case 'java':
          output = executeJava(code);
          break;
        case 'cpp':
          output = executeCpp(code);
          break;
        default:
          error = `Language ${language} is not supported for execution`;
      }
    } catch (e) {
      error = e instanceof Error ? e.message : 'Unknown error occurred';
    }

    return NextResponse.json({
      success: !error,
      output: error ? '' : output,
      error: error || null
    });

  } catch (error) {
    console.error('Code Execution API Error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

function executeJavaScript(code: string): string {
  // Create a safe execution context
  const logs: string[] = [];
  const originalConsoleLog = console.log;
  
  console.log = (...args) => {
    logs.push(args.map(arg => String(arg)).join(' '));
  };

  try {
    // Remove dangerous functions and execute
    const safeCode = code
      .replace(/import|require|fetch|XMLHttpRequest|eval|Function/g, '')
      .replace(/window\./g, '')
      .replace(/document\./g, '')
      .replace(/global\./g, '');

    // Execute the code
    new Function(safeCode)();
    
    return logs.join('\n') || 'Code executed successfully (no output)';
  } catch (error) {
    throw new Error(`JavaScript Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    console.log = originalConsoleLog;
  }
}

function executePython(code: string): string {
  // Simulate Python execution
  const lines = code.split('\n');
  const outputs: string[] = [];
  
  for (const line of lines) {
    const trimmed = line.trim();
    
    if (trimmed.startsWith('print(') && trimmed.endsWith(')')) {
      // Extract the content inside print()
      const content = trimmed.slice(6, -1);
      // Simple string interpolation simulation
      const processed = content
        .replace(/f["'](.*)["']/g, '$1') // Remove f-string prefix
        .replace(/\{([^}]+)\}/g, (match, varName) => {
          // Simple variable substitution simulation
          if (varName.includes('+') || varName.includes('-') || varName.includes('*') || varName.includes('/')) {
            try {
              return String(eval(varName));
            } catch {
              return `{${varName}}`;
            }
          }
          return `[${varName}]`;
        });
      
      outputs.push(processed);
    } else if (trimmed.includes('=') && !trimmed.startsWith('#')) {
      // Variable assignment simulation
      outputs.push(`// Variable assigned: ${trimmed}`);
    }
  }
  
  return outputs.join('\n') || 'Python code structure is valid';
}

function executeHTML(code: string): string {
  // Validate HTML structure
  const doctypeMatch = code.match(/<!DOCTYPE\s+html/i);
  const htmlTag = code.match(/<html[^>]*>/i);
  const headTag = code.match(/<head[^>]*>/i);
  const bodyTag = code.match(/<body[^>]*>/i);
  
  const issues = [];
  
  if (!doctypeMatch) issues.push('Missing DOCTYPE declaration');
  if (!htmlTag) issues.push('Missing <html> tag');
  if (!headTag) issues.push('Missing <head> tag');
  if (!bodyTag) issues.push('Missing <body> tag');
  
  if (issues.length > 0) {
    return `HTML Validation Issues:\n${issues.join('\n')}`;
  }
  
  // Check for common HTML elements
  const hasTitle = code.includes('<title>');
  const hasMeta = code.includes('<meta');
  const hasLinks = code.includes('<a ') || code.includes('<link');
  
  const features = [];
  if (hasTitle) features.push('✓ Title tag present');
  if (hasMeta) features.push('✓ Meta tags present');
  if (hasLinks) features.push('✓ Links present');
  
  return `HTML Structure: Valid\nFeatures:\n${features.join('\n') || '✓ Basic structure'}`;
}

function executeCSS(code: string): string {
  // Validate CSS syntax
  const rules = code.match(/[^{}]+\{[^}]*\}/g) || [];
  const properties = code.match(/[^:]+:[^;]+;/g) || [];
  
  const issues = [];
  
  if (rules.length === 0) issues.push('No valid CSS rules found');
  if (properties.length === 0) issues.push('No CSS properties found');
  
  // Check for common CSS properties
  const hasColors = code.includes('color:') || code.includes('background:');
  const hasLayout = code.includes('display:') || code.includes('position:') || code.includes('float:');
  const hasSpacing = code.includes('margin:') || code.includes('padding:');
  
  const features = [];
  if (hasColors) features.push('✓ Color properties');
  if (hasLayout) features.push('✓ Layout properties');
  if (hasSpacing) features.push('✓ Spacing properties');
  
  const result = [
    `CSS Rules: ${rules.length}`,
    `CSS Properties: ${properties.length}`,
    issues.length > 0 ? `Issues:\n${issues.join('\n')}` : '',
    features.length > 0 ? `Features:\n${features.join('\n')}` : ''
  ].filter(Boolean).join('\n');
  
  return result || 'CSS syntax appears valid';
}

function executeSQL(code: string): string {
  // Basic SQL validation and simulation
  const statements = code.split(';').filter(s => s.trim());
  const results: string[] = [];
  
  for (const statement of statements) {
    const trimmed = statement.trim().toUpperCase();
    
    if (trimmed.startsWith('SELECT')) {
      // Simulate SELECT query
      const fromMatch = statement.match(/FROM\s+(\w+)/i);
      const table = fromMatch ? fromMatch[1] : 'unknown_table';
      results.push(`SELECT query executed on table: ${table}`);
      results.push(`// Simulated: Retrieved data from ${table}`);
    } else if (trimmed.startsWith('CREATE TABLE')) {
      const tableNameMatch = statement.match(/CREATE TABLE\s+(\w+)/i);
      const tableName = tableNameMatch ? tableNameMatch[1] : 'unknown_table';
      results.push(`CREATE TABLE executed: ${tableName}`);
    } else if (trimmed.startsWith('INSERT')) {
      results.push('INSERT statement executed');
    } else if (trimmed.startsWith('UPDATE')) {
      results.push('UPDATE statement executed');
    } else if (trimmed.startsWith('DELETE')) {
      results.push('DELETE statement executed');
    } else if (trimmed.length > 0) {
      results.push(`SQL statement parsed: ${trimmed.substring(0, 20)}...`);
    }
  }
  
  return results.join('\n') || 'SQL syntax appears valid';
}

function executeJava(code: string): string {
  // Basic Java structure validation
  const hasClass = code.includes('class') && code.includes('{');
  const hasMain = code.includes('public static void main');
  const hasBraces = (code.match(/\{/g) || []).length === (code.match(/\}/g) || []).length;
  
  const issues = [];
  
  if (!hasClass) issues.push('Missing class definition');
  if (!hasMain) issues.push('Missing main method');
  if (!hasBraces) issues.push('Unbalanced braces');
  
  // Check for common Java elements
  const hasImports = code.includes('import ');
  const hasMethods = code.includes('void ') || code.includes('int ') || code.includes('String ');
  const hasOutput = code.includes('System.out.print');
  
  const features = [];
  if (hasImports) features.push('✓ Import statements');
  if (hasMethods) features.push('✓ Method definitions');
  if (hasOutput) features.push('✓ Output statements');
  
  const result = [
    'Java Structure Analysis:',
    issues.length > 0 ? `Issues:\n${issues.join('\n')}` : '✓ Basic structure valid',
    features.length > 0 ? `Features:\n${features.join('\n')}` : ''
  ].filter(Boolean).join('\n');
  
  return result || 'Java code structure analyzed';
}

function executeCpp(code: string): string {
  // Basic C++ structure validation
  const hasInclude = code.includes('#include');
  const hasMain = code.includes('int main(');
  const hasBraces = (code.match(/\{/g) || []).length === (code.match(/\}/g) || []).length;
  
  const issues = [];
  
  if (!hasInclude) issues.push('Missing include statements');
  if (!hasMain) issues.push('Missing main function');
  if (!hasBraces) issues.push('Unbalanced braces');
  
  // Check for common C++ elements
  const hasNamespace = code.includes('namespace');
  const hasPointers = code.includes('*') || code.includes('&');
  const hasOutput = code.includes('cout');
  
  const features = [];
  if (hasNamespace) features.push('✓ Namespace usage');
  if (hasPointers) features.push('✓ Pointer usage');
  if (hasOutput) features.push('✓ Output statements');
  
  const result = [
    'C++ Structure Analysis:',
    issues.length > 0 ? `Issues:\n${issues.join('\n')}` : '✓ Basic structure valid',
    features.length > 0 ? `Features:\n${features.join('\n')}` : ''
  ].filter(Boolean).join('\n');
  
  return result || 'C++ code structure analyzed';
}